services:
  redis:
    image: redis:7
    ports:
      - "6379:6379"
    networks:
      default:
        aliases:
          - dapr_redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  clickhouse:
    image: clickhouse/clickhouse-server:23.8
    environment:
      CLICKHOUSE_DB: analytics
      CLICKHOUSE_USER: default
      CLICKHOUSE_PASSWORD: ""
      CLICKHOUSE_DEFAULT_ACCESS_MANAGEMENT: "1"
    ports:
      - "9000:9000"
      - "8123:8123"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8123/ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  analytics-microservice:
    build:
      context: ./go-microservices/analytics_microservice
    ports:
      - "2020:2020"
    environment:
      # service binding
      ANALYTICS_HTTP_HOST:         "0.0.0.0"
      ANALYTICS_HTTP_PORT:         "2020"

      # infra
      ANALYTICS_CLICKHOUSE_DSN:    "tcp://clickhouse:9000/analytics"
      ANALYTICS_REDIS_ADDR:        "redis:6379"
      ANALYTICS_REDIS_DB:          "0"
      ANALYTICS_REDIS_POOL_SIZE:   "10"
      ANALYTICS_REDIS_MIN_IDLE_CONNS: "5"
      ANALYTICS_REDIS_MAX_RETRIES:    "3"
      ANALYTICS_REDIS_KEY_PREFIX:     "template:"
      ANALYTICS_REDIS_DEFAULT_TTL:    "1h"

      # Dapr
      # ANALYTICS_DAPR_ENABLED:      "true"
      # ANALYTICS_DAPR_APP_ID:       "analytics-microservice"
      # ANALYTICS_DAPR_HTTP_PORT:    "3500"
      # ANALYTICS_DAPR_GRPC_PORT:    "50001"
      DAPR_ENABLED:      "true"
      DAPR_APP_ID:       "analytics-microservice"
      DAPR_HTTP_PORT:    "3500"
      DAPR_GRPC_PORT:    "50001"
    depends_on:
      clickhouse:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:2020/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  analytics-dapr:
    image: daprio/daprd:1.15.6
    network_mode: "service:analytics-microservice"
    # ports:
    #   - "3500:3500"    # expose the HTTP API
    #   - "50001:50001"  # expose the gRPC API (if you ever need it)
    command: >
      /daprd
      --app-id analytics-microservice
      --app-port 2020
      --dapr-http-port 3500
      --dapr-grpc-port 50001
      --resources-path /components
    volumes:
      - ./go-microservices/analytics_microservice/components:/components:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3500/v1.0/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  webhook-microservice:
    build:
      context: ./platform-sdks/webhook-microservice
      dockerfile: Dockerfile
    ports:
      - "8080:8080"  # Fixed: Match CQRS port configuration
    environment:

      # Server configuration (used when CQRS disabled)
      WEBHOOK_SERVER_HOST: "0.0.0.0"
      WEBHOOK_SERVER_PORT: "8080"  # Fixed: Match the exposed port

      # Environment and logging
      WEBHOOK_ENVIRONMENT: "development"
      WEBHOOK_LOGGING_LEVEL: "info"

      # Observability
      WEBHOOK_OBSERVABILITY_PROMETHEUS_ENABLED: "false"
      WEBHOOK_OBSERVABILITY_TRACING_ENABLED: "false"

      # CQRS configuration (NEW)
      WEBHOOK_CQRS_ENABLED: "true"  # Enable CQRS mode
      WEBHOOK_CQRS_HTTP_PORT: "8080"  # Same port as server
      WEBHOOK_CQRS_SERVICE_NAME: "webhook-microservice-cqrs"
      WEBHOOK_CQRS_PUBLISHER_PUBSUB_NAME: "redis-pubsub"
      WEBHOOK_CQRS_PUBLISHER_TOPIC_NAME: "webhook-events"

      # Dapr configuration
      WEBHOOK_DAPR_ENABLED: "true"
      WEBHOOK_DAPR_APP_ID: "webhook-microservice"
      WEBHOOK_DAPR_HTTP_PORT: "3501"
      WEBHOOK_DAPR_GRPC_PORT: "50002"
      WEBHOOK_DAPR_STATE_STORE: "statestore"
      WEBHOOK_DAPR_PUBSUB_NAME: "redis-pubsub"
      WEBHOOK_DAPR_SECRET_STORE: "config-secret-store"

      # Webhook-specific configuration
      WEBHOOK_WEBHOOK_TOPIC_NAME: "webhook-events"
      WEBHOOK_WEBHOOK_RATE_LIMIT_RPM: "1000"
      WEBHOOK_WEBHOOK_WAF_ALLOW_LIST: "0.0.0.0/0"

      # Redis configuration
      WEBHOOK_REDIS_ADDR: "redis:6379"
      WEBHOOK_REDIS_DB: "0"
      WEBHOOK_REDIS_KEY_PREFIX: "webhook:"


    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3

  webhook-dapr:
    image: daprio/daprd:1.15.6
    network_mode: "service:webhook-microservice"
    # ports:
    #   - "3501:3501"
    #   - "50002:50002"
    command: >
      /daprd
      --app-id webhook-microservice
      --app-port 8080
      --dapr-http-port 3501
      --dapr-grpc-port 50002
      --resources-path /components
    volumes:
      - ./platform-sdks/webhook-microservice/components:/components:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3501/v1.0/healthz"]
      interval: 10s
      timeout: 5s
      retries: 3