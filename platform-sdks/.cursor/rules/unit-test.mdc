---
description: Write unit tests
globs: 
alwaysApply: false
---
# Go Unit Testing Guidelines

When I mention @generate-unit-tests in a Go file, generate unit tests for the code in that file.

## Testing Framework

- Use **Ginkgo** for test structure and BDD-style tests
- Use **Gomega** for assertions and matchers
- Use **Gomock** for mocking dependencies

## Structure

- Create a `{dir_name}_suite_test.go` file for the directory under which the file you are unit testing is in case the `suite_test` file does not exist.
- Follow the file naming convention: `{filename}_test.go` when creating the test file for the src file you are unit testing.

## Organization

- Top-level `Describe` for the component being tested
- Nested `Describe` for each function/method
- `Context` blocks for different scenarios
- `It` blocks for specific expectations

## AAA Pattern

Always structure tests using the Arrange-Act-Assert pattern:

1. **Arrange**: Set up the test environment and prepare data
   - Create instances of objects
   - Set up mock expectations
   - Prepare input data

2. **Act**: Execute the code being tested
   - Call the function or method
   - Capture results and errors

3. **Assert**: Verify behavior
   - Check return values
   - Verify mock expectations
   - Ensure side effects

Always comment your code to create the distinction between the Arrange, Act & Assert sections
of the test.

## Best Practices
- Follow the AAA pattern (Arrange-Act-Assert)
- Use descriptive test names
- Clean up resources in AfterEach
- Group related tests using Describe and Context
- Use table-driven tests for multiple similar test cases
- Mock external dependencies
- Test both success and error cases
- Test edge cases and boundary conditions
- Keep tests focused and independent
- Use meaningful test data
- Avoid test interdependence

## Setup & Teardown

```go
var _ = Describe("ComponentName", func() {
    var (
        mockCtrl *gomock.Controller
        mockDep  *mocks.MockDependency
        testObj  *package.Component
    )

    BeforeEach(func() {
        mockCtrl = gomock.NewController(GinkgoT())
        mockDep = mocks.NewMockDependency(mockCtrl)
        testObj = package.NewComponent(mockDep)
    })

    AfterEach(func() {
        mockCtrl.Finish()
    })

    // Tests here
})
```

## Mock Expectations

```go
// ARRANGE
mockDep.EXPECT().
    MethodName(gomock.Any()).
    Return(expectedValue, nil).
    Times(1)
```

## Testing Error Paths

Always test both success and error conditions:

```go
Context("when operation succeeds", func() { ... })
Context("when operation fails", func() { ... })
```

## Table-Driven Tests

Use `DescribeTable` for parameter variations:

```go
DescribeTable("validation scenarios",
    func(input string, expectedResult bool) {
        // Test with different inputs
        result := component.Validate(input)
        Expect(result).To(Equal(expectedResult))
    },
    Entry("valid input", "valid", true),
    Entry("invalid input", "invalid", false),
)
```

## Example Test Structure

```go
Describe("UserService", func() {
    // Setup variables and mocks

    BeforeEach(func() {
        // Initialize before each test
    })

    Describe("GetUser", func() {
        Context("when user exists", func() {
            It("returns the user without error", func() {
                // ARRANGE
                // Set up expectations

                // ACT
                // Call the method

                // ASSERT
                // Verify results
            })
        })

        Context("when user doesn't exist", func() {
            It("returns appropriate error", func() {
                // Test error case
            })
        })
    })

    // More tests for other functions
})
```

For detailed examples, refer to our testing guide: @apps/documentation/docs/guidelines/utingo.md
