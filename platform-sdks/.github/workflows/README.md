# Repo Automation Guide

This repo has **two GitHub Actions workflows** that keep every commit healthy and ship a versioned Go SDK whenever a release branch is merged.

| Workflow          | File                                            | When it runs                                                                          | What it does                                                                                                                                                                                                                              |
| ----------------- | ----------------------------------------------- | ------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Monorepo CI**   | `.github/workflows/ci.yaml`                     | • Any push to `main`  <br>• Any pull‑request targeting `main`                         |  Runs `nx` lint ➜ test ➜ build for only the projects that changed (docs are excluded). Guarantees the codebase stays green before merge.                                                                                                  |
| **Tag & Release** | `.github/workflows/go-sdk-tag-and-release.yaml` | When a PR sourced from a branch that starts with `release/` is **merged** into `main` | • Builds & tests the SDK once more.<br>• Extracts **X.Y.Z** from the branch name.<br>• Creates tag `sdk-go-vX.Y.Z` and pushes it.<br>• (Quick smoke build on the tag.)<br>• Creates a GitHub Release with the matching changelog section. |

---

## 1  Monorepo CI (Nx + Go + TS)

```
Trigger   : push, pull_request → main
Key steps :
  ▸ Checkout (fetch‑depth 0)
  ▸ node 22  + pnpm 10
  ▸ go 1.24.*   (GOPRIVATE=github.com/Matrics-io/*)
  ▸ Go module cache
  ▸ nx affected: lint / test / build  --exclude=documentation
  ▸ No artefacts written, no tags pushed
```

*Why exclude `documentation`?*  The Docusaurus build currently fails on unresolved links; skipping it keeps day‑to‑day CI fast and green.

---

## 2  Tag & Release Workflow

```
Trigger   : pull_request (closed) → main
Condition : PR merged && source branch begins with "release/"
Permissions: contents: write (default GITHUB_TOKEN is enough)
```

### What counts as a release branch?

```
release/1.6.0          ✓  → version 1.6.0
release/v2.0.3         ✓  → version 2.0.3
release/hotfix3.2.1    ✗  (fails — suffix must be X.Y.Z)
```

The regex grabs **the final X.Y.Z** at the end of the branch name.

### Pipeline steps

1. **Checkout** the merge commit.
2. **Toolchain** – same Node/Go setup as CI.
3. **Build safety‑net** – run Nx lint/test/build again.
4. **Extract version** → `$VERSION`.
5. **Tag & push** `sdk-go-v$VERSION` using the built‑in token.
6. **Smoke test** the SDK on the exact tag.
7. **Collect changelog** section for that version (if present).
8. **Publish GitHub Release** (draft =false, prerelease =false).

No Personal‑Access Token is required because all actions happen inside one job.

---

## Typical Maintainer Flow

1. Branch from `main` → `release/1.7.0`.
2. Commit any final changes (version file, changelog, etc.).
3. Open PR → GitHub runs **Monorepo CI**; fix anything red.
4. Merge PR.
5. Tag & Release workflow runs automatically → **`sdk-go-v1.7.0`** appears under *Releases*.

> **Tip:** protect `main` with the required status check **“CI (Nx affected)”** so only green PRs can be merged.

---

## FAQ

| Question                                                           | Answer                                                                                          |
| ------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------- |
| **Do I need a PAT?**                                               | No. The built‑in `GITHUB_TOKEN` (with `contents: write`) tags and releases inside the same job. |
| **How do consumers import?**                                       | \`\`\`bash                                                                                      |
| export GOPRIVATE=github.com/Matrics-io/\*                          |                                                                                                 |
| go get github.com/Matrics-io/platform-sdks/sdks/go/datakit\@vX.Y.Z |                                                                                                 |

```|
| **Docs build is failing; can we re‑enable it?** | Fix the broken links or set `onBrokenLinks: 'warn'` in `docusaurus.config.js`, then remove `--exclude=documentation` from both workflows. |

---

## File Map
```

.github/workflows/
ci.yaml                    → Monorepo CI
go-sdk-tag-and-release.yaml→ Tag & Release
README (this file)           → How it all works

```

Happy shipping! 🚀

```
