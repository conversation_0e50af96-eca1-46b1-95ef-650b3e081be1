name: "Go SDK – tag & release on merge"

on:
  pull_request:
    types: [closed]
    branches: [main]

permissions:
  contents: write        # allow this job to push the tag and create the release

jobs:
  tag_and_release:
    if: >
      github.event.pull_request.merged == true &&
      startsWith(github.event.pull_request.head.ref, 'release/')
    runs-on: ${{ vars.DEFAULT_GITHUB_RUNNER }}
    env:
      NX_NO_CLOUD: true

    steps:
    # Checkout code
    - uses: actions/checkout@v4
      with: { fetch-depth: 0 }

    # Toolchain setup
    - uses: actions/setup-node@v3
      with: { node-version: '22' }

    - uses: pnpm/action-setup@v2
      with: { version: 10, run_install: false }

    - run: pnpm install

    - uses: actions/setup-go@v4
      with: { go-version: '1.24.3' }

    - run: go env -w GOPRIVATE=github.com/Matrics-io/*

    - run: git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    # Lint / test / build
    - run: npx nx affected:lint  --base=origin/main --head=HEAD --exclude=documentation,datakit-samples
    - run: npx nx affected:test  --base=origin/main --head=HEAD --exclude=documentation,datakit-samples
    - run: npx nx affected:build --base=origin/main --head=HEAD --exclude=documentation,datakit-samples

    # Extract version X.Y.Z
    - id: get_ver
      run: |
        BRANCH="${{ github.event.pull_request.head.ref }}"
        if [[ "$BRANCH" =~ ([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
          VERSION="${BASH_REMATCH[1]}"
          echo "VERSION=$VERSION" >> "$GITHUB_ENV"
          echo "version=$VERSION" >> "$GITHUB_OUTPUT"
        else
          echo "::error ::Branch '$BRANCH' must end in X.Y.Z" ; exit 1
        fi

    # Tag & push
    - name: Create tag and push
      run: |
        TAG="sdks/go/datakit/v${VERSION}"
        git config user.name  "release-bot"
        git config user.email "<EMAIL>"
        git tag "$TAG"
        git push origin "refs/tags/$TAG"

    # (optional) quick smoke test on tag
    - run: npx nx run datakit:test
    - run: npx nx run datakit:build

    # Collect changelog section
    - id: notes
      run: |
        VERSION=${{ steps.get_ver.outputs.version }}
        BODY="Release Go SDK v$VERSION"
        FILE="sdks/go/datakit/CHANGELOG.md"
        if [ -f "$FILE" ]; then
          NOTES=$(awk "/^## \\[${VERSION}\\]/ {flag=1; next} /^## \\[?/ && flag {flag=0} flag" "$FILE")
          [ -n "$NOTES" ] && BODY="## Changelog\n$NOTES"
        fi
        printf 'body<<EOF\n%s\nEOF\n' "$BODY" >> "$GITHUB_OUTPUT"

    # Publish GitHub release
    - uses: actions/create-release@v1
      with:
        tag_name:     sdks/go/datakit/v${{ steps.get_ver.outputs.version }}
        release_name: sdks/go/datakit/v${{ steps.get_ver.outputs.version }}
        body:         ${{ steps.notes.outputs.body }}
        draft:        false
        prerelease:   false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}