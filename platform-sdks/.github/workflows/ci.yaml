name: "<PERSON><PERSON><PERSON> CI (Nx + Go + TS)"

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  ci:
    name: <PERSON><PERSON> (Nx affected)
    runs-on: ${{ vars.DEFAULT_GITHUB_RUNNER }}
    env:
      NX_NO_CLOUD: true
    steps:
      # 0) Checkout
      - uses: actions/checkout@v3
        with: { fetch-depth: 0 }       # for nx affected

      # 1) Node & pnpm
      - uses: actions/setup-node@v3
        with: { node-version: '22' }
      - uses: pnpm/action-setup@v2
        with: { version: 10, run_install: false }
      - run: pnpm install

      # 2) Go
      - uses: actions/setup-go@v4
        with: { go-version: '1.24.3' }
      - run: go env -w GOPRIVATE=github.com/Matrics-io/*

      # 3) Git auth for private Go modules
      - run: git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      # 4) Cache Go modules
      - uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: go-mod-${{ hashFiles('sdks/go/datakit/go.sum') }}
          restore-keys: |
            go-mod-

      # 5) Nx affected tasks  (skip docs)
      - name: Lint (affected, no docs)
        run: npx nx affected:lint   --base=origin/main --head=HEAD --exclude=documentation
      - name: Test (affected, no docs)
        run: npx nx affected:test   --base=origin/main --head=HEAD --exclude=documentation
      - name: Build (affected, no docs)
        run: npx nx affected:build  --base=origin/main --head=HEAD --exclude=documentation
