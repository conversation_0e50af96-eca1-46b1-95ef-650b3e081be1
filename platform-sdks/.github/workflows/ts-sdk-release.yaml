name: "TypeScript SDK → GAR + GitHub Release"

on:
  pull_request:
    types: [closed]
    branches: [main]

permissions:
  contents: write          # tag + GitHub Release

env:
  NX_NO_CLOUD: true
  NX_GO_SKIP: 'true'

jobs:
  tag_build_publish:
    runs-on: ${{ vars.DEFAULT_GITHUB_RUNNER }}

    steps:
    # checkout
    - uses: actions/checkout@v4
      with: {fetch-depth: 0}

    # toolchain
    - uses: actions/setup-go@v4
      with: {go-version: '1.24.x'}

    - uses: actions/setup-node@v3
      with: {node-version: '22'}

    - uses: pnpm/action-setup@v2
      with: {version: 10, run_install: false}

    - run: pnpm install

    - run: npx nx run ts-datakit:lint
    - run: npx nx run ts-datakit:test
    - run: npx nx run ts-datakit:build

    # extract version from branch name
    - id: version
      run: |
        BRANCH="${{ github.event.pull_request.head.ref }}"
        if [[ "$BRANCH" =~ ([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
          VERSION="${BASH_REMATCH[1]}"
          echo "VERSION=$VERSION"      >> "$GITHUB_ENV"
          echo "version=$VERSION"      >> "$GITHUB_OUTPUT"
        else
          echo "::error ::Branch name must be release/X.Y.Z" ; exit 1
        fi

    # extract version from commit message
    # - id: version
    #   run: |
    #     RAW="${{ github.event.head_commit.message }}"
    #     [[ "$RAW" =~ release/([0-9]+\.[0-9]+\.[0-9]+) ]] || {
    #       echo "::error ::commit must contain release/X.Y.Z"; exit 1; }
    #     echo "VERSION=${BASH_REMATCH[1]}" >> "$GITHUB_ENV"
    #     echo "version=${BASH_REMATCH[1]}" >> "$GITHUB_OUTPUT"

    # tag & github release
    - run: |
        git config user.name  "release-bot"
        git config user.email "<EMAIL>"
        git tag "ts-datakit-v${VERSION}"
        git push origin "refs/tags/ts-datakit-v${VERSION}"

    - uses: actions/create-release@v1
      with:
        tag_name:     ts-datakit-v${{ steps.version.outputs.version }}
        release_name: ts-datakit-v${{ steps.version.outputs.version }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    # auth to GCP
    # authenticate with the JSON key
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: '${{ secrets.GOOGLE_CREDENTIALS }}'

    # install Cloud SDK
    - name: Install gcloud CLI
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ninetyone-devops
        version: 'latest'

    # write npmrc for GAR
    - name: Configure npm for GAR
      env:
        GAR_REPO: us-east1-npm.pkg.dev/ninetyone-devops/hplus-devops-npm
      run: |
        TOKEN=$(gcloud auth print-access-token)
        cat > ~/.npmrc <<EOF
        registry=https://${GAR_REPO}/
        always-auth=true
        //${GAR_REPO}/:_authToken=${TOKEN}
        EOF

    # publish to GAR
    - name: Publish package
      env:
        GAR_URL: https://us-east1-npm.pkg.dev/ninetyone-devops/hplus-devops-npm/
      run: |
        cd sdks/typescript/datakit
        npm version --no-git-tag-version "${VERSION}"
        pnpm publish --registry="$GAR_URL" --no-git-checks --access restricted
