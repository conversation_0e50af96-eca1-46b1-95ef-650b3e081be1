{"name": "@matricsio/datakit", "version": "0.1.0", "description": "A lightweight, type-safe data toolkit with CQRS and PubSub", "license": "MIT", "keywords": ["datakit", "CQRS", "pubsub", "typescript", "matrics"], "main": "./index.js", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./cqrs": {"types": "./cqrs/index.d.ts", "default": "./cqrs/index.js"}, "./pubsub": {"types": "./pubsub/index.d.ts", "default": "./pubsub/index.js"}, "./grpc": {"types": "./grpc/index.d.ts", "default": "./grpc/index.js"}}, "files": ["dist", "README.md"], "sideEffects": false, "scripts": {"build": "nx build ts-datakit", "clean": "nx clean ts-datakit", "test": "nx test ts-datakit", "test:watch": "jest --watch", "test:coverage": "nx test ts-datakit --configuration=ci", "test:ci": "nx test ts-datakit --configuration=ci", "lint": "nx lint ts-datakit", "lint:fix": "nx lint ts-datakit --fix"}, "peerDependencies": {"@dapr/dapr": "^3.5.2", "zod": "^3", "zod-to-json-schema": "^3"}, "dependencies": {"@trpc/server": "^11.3.1", "fastmcp": "^3.6.2", "protobufjs": "^7.4.0", "uuid": "^11.1.0", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@dapr/dapr": "^3.5.2", "@fastify/compress": "^8.0.3", "@fastify/cors": "^11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.2", "@fastify/websocket": "^11.1.0", "@modelcontextprotocol/sdk": "^1.13.2", "@trpc/client": "^11.3.1", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@types/uuid": "^10.0.0", "@types/wait-on": "^5.3.4", "cross-fetch": "^4.1.0", "expect-type": "^1.2.1", "fastify": "^5.3.3", "jest": "^29.5.0", "portfinder": "^1.0.37", "supertest": "^7.1.1", "trpc-ui": "^1.0.15", "ts-jest": "^29.1.0", "typescript": "^5.3.3", "wait-on": "^8.0.3", "zod": "^3.22.4", "zod-to-json-schema": "^3.24.5"}, "repository": {"type": "git", "url": "https://github.com/Matrics-io/platform-sdks.git", "directory": "sdks/typescript/datakit"}, "bugs": {"url": "https://github.com/Matrics-io/platform-sdks/issues"}, "homepage": "https://github.com/Matrics-io/platform-sdks#readme", "engines": {"node": ">=18"}}