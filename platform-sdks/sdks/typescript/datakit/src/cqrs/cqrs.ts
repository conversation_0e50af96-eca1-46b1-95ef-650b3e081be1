import type { Registry, InferProcedureInput, InferProcedureOutput, CreateContext, CQRSRunConfig, RegistryToTRPC, HealthResponse, MockedRegistry, MaybePromise, AdapterRuntimes, InferProceduresFromRecord, AdapterStatus, CQRSMetadata, MCPResource, MCPResourceTemplate, MCPResourceContent, CreateCQRSOptions, ExecuteContextParameters } from './types';
import { runWithTrace, getTrace } from './trace';
import { createDefine } from './define';
import { createRegistry } from './registry';
import { CQRSInterface, CQRSServer } from './interfaces';
import { Executor } from './executor';

export type CQRSOptions<TRegistry extends Registry<any, TCustomContext>, TCustomContext extends Record<string, any> = Record<string, any>> = {
    registry: TRegistry;
    metadata?: CQRSMetadata;
    getContext?: CreateContext<TCustomContext>;
    runtimes?: CQRSRunConfig;
};

function createCQRSServer(adapters: Map<AdapterRuntimes, any>): CQRSServer {
    return {
        stop: async () => {
            const stopPromises = Array.from(adapters.values()).map(adapter => {
                if (adapter && typeof adapter.stop === 'function') {
                    return adapter.stop();
                }
                return Promise.resolve();
            });
            await Promise.all(stopPromises);
        },

        getHealth: async () => {
            const healthPromises = Array.from(adapters.entries()).map(async ([runtime, adapter]) => {
                try {
                    if (adapter && typeof adapter.getHealth === 'function') {
                        const health = await adapter.getHealth();
                        return [runtime, health] as const;
                    }
                    return [runtime, { status: 'unknown', message: 'Adapter does not support health checks' }] as const;
                } catch (error) {
                    return [runtime, { status: 'error', message: error instanceof Error ? error.message : 'Unknown error' }] as const;
                }
            });

            const results = await Promise.all(healthPromises);
            return Object.fromEntries(results) as Record<AdapterRuntimes, HealthResponse>;
        },

        getStatus: async () => {
            const status: Record<string, any> = {};

            for (const [name, adapter] of adapters) {
                status[name] = {
                    name: adapter.name || name,
                    status: adapter.status || 'unknown',
                };
            }

            return status as Record<AdapterRuntimes, { name: string; status: AdapterStatus }>;
        }
    };
}

export class CQRSInternal<TRegistry extends Registry<any, any>, TCustomContext extends Record<string, any> = Record<string, any>> implements CQRSInterface<TRegistry, TCustomContext> {
    private registry: TRegistry;
    private _innerGetContext?: CreateContext<TCustomContext>;
    private _metadata?: CQRSMetadata;
    private _adapters: Map<AdapterRuntimes, any> = new Map();
    private _runtimes: CQRSRunConfig;
    private _isMocked: boolean = false;
    private _mcpResources: Map<string, MCPResource> = new Map();
    private _mcpResourceTemplates: Map<string, MCPResourceTemplate> = new Map();

    readonly commands: InferProceduresFromRecord<TRegistry['_def'], 'command', TCustomContext>;
    readonly queries: InferProceduresFromRecord<TRegistry['_def'], 'query', TCustomContext>;

    public static create<TCustomContext extends object = Record<string, any>>(options?: CreateCQRSOptions) {
        return {
            define: createDefine<TCustomContext>(options),
            registry: createRegistry<TCustomContext>(),
        }
    }

    public getMetadata(): CQRSMetadata | undefined {
        return this._metadata;
    }

    public getContext() {
        return getTrace<TCustomContext>();
    }

    public getContextFunction() {
        return this._innerGetContext;
    }

    /**
     * Execute a procedure with enhanced context (e.g., REST headers).
     */
    async executeWithContext<TProcedure extends Extract<keyof TRegistry['_def'], string>>(
        procedureName: TProcedure,
        input: InferProcedureInput<TRegistry['_def'][TProcedure]>,
        contextParams: ExecuteContextParameters<TCustomContext>
    ): Promise<InferProcedureOutput<TRegistry['_def'][TProcedure]>> {
        const procedure = this.registry._def[procedureName];
        if (!procedure) {
            throw new Error(`Procedure '${procedureName}' not found`);
        }

        // Create context function for this specific execution
        const getContextForExecution = () => {
            const baseContext = this._innerGetContext
                ? this._innerGetContext(contextParams)
                : {} as TCustomContext;

            return {
                ...(contextParams.mcp && { mcp: contextParams.mcp }),
                ...(contextParams.origin && { origin: contextParams.origin }),
                ...baseContext,
            };
        };

        return runWithTrace(
            contextParams.traceId,
            contextParams.parentId,
            procedureName,
            procedure._type,
            input,
            getContextForExecution,
            async (traceContext) => {
                return Executor.execute(
                    procedureName,
                    procedure,
                    input,
                    traceContext,
                    {
                        observers: new Set(), // No observers for direct CQRS execution
                        onError: undefined,   // Error handling done at higher level
                    }
                );
            }
        );
    }

    constructor(opts: CQRSOptions<TRegistry, TCustomContext>) {
        this.registry = opts.registry;
        this._innerGetContext = opts.getContext;
        this._runtimes = opts.runtimes || {};
        this._metadata = opts.metadata;

        const procedures = Object.entries(this.registry._def);
        const caller = this.registry.createCaller({
            getContext: this._innerGetContext,
        });

        // Initialize commands
        const commands = Object.fromEntries(procedures.filter(([_, procedure]) => (procedure as any)._type === 'command').map(([key]) => [key, caller[key]]));
        const queries = Object.fromEntries(procedures.filter(([_, procedure]) => (procedure as any)._type === 'query').map(([key]) => [key, caller[key]]));

        this.commands = commands as InferProceduresFromRecord<TRegistry['_def'], 'command', TCustomContext>;
        this.queries = queries as InferProceduresFromRecord<TRegistry['_def'], 'query', TCustomContext>;
    }

    /**
     * Add an MCP resource that can be referenced by tools
     */
    public addResource(resource: MCPResource): void {
        this._mcpResources.set(resource.uri, resource);
    }

    /**
     * Add an MCP resource template that can be referenced by tools
     */
    public addResourceTemplate(template: MCPResourceTemplate): void {
        this._mcpResourceTemplates.set(template.uriTemplate, template);
    }

    /**
     * Get an embedded resource for use in tool responses
     */
    public async embedded(uri: string): Promise<MCPResourceContent> {
        const resource = this._mcpResources.get(uri);
        if (!resource) {
            throw new Error(`Resource not found: ${uri}`);
        }

        const content = await resource.load();
        return {
            type: 'resource',
            resource: {
                uri,
                text: content.text,
                mimeType: content.mimeType || resource.mimeType
            }
        };
    }

    /**
     * Runs the CQRS instance runtime adapters.
     *
     * @param config - Allowed for convenience. Overrides the runtime config set in the constructor.
     * */
    public async run(config?: CQRSRunConfig): Promise<CQRSServer> {
        const promises: Promise<void>[] = [];

        const runtimeConfig = config || this._runtimes;

        // Start unified server adapter if configured
        const httpConfig = runtimeConfig.http;
        if (httpConfig) {
            promises.push(this.startHTTPAdapter(httpConfig));
        }

        // Start MCP adapter if configured
        const mcpConfig = runtimeConfig.mcp;
        if (mcpConfig) {
            promises.push(this.startMCPAdapter(mcpConfig));
        }

        // Wait for all adapters to start
        await Promise.all(promises);

        return createCQRSServer(this._adapters);
    }

    private async startHTTPAdapter(serverConfig: NonNullable<CQRSRunConfig['http']>): Promise<void> {
        let adapter: any;

        // Create adapter instance
        if (typeof serverConfig.adapter === 'string') {
            // Built-in adapter
            switch (serverConfig.adapter) {
                case 'fastify':
                    const { FastifyServerAdapter } = await import('../adapters/fastify/fastify');
                    adapter = new FastifyServerAdapter({ cqrs: this });
                    break;
                default:
                    throw new Error(`Unknown server adapter: ${(serverConfig as any)?.adapter}`);
            }
        } else {
            // Custom adapter instance
            adapter = serverConfig.adapter;
        }

        // Type guard to ensure adapter implements ServerAdapter interface
        if (!adapter ||
            typeof adapter.configure !== 'function' ||
            typeof adapter.registerRoutes !== 'function' ||
            typeof adapter.registerTRPCRouter !== 'function') {
            throw new Error('Invalid server adapter: must implement ServerAdapter interface');
        }

        // Configure adapter
        await adapter.configure(serverConfig.config || {});

        // Register REST routes if REST protocol is enabled
        if (adapter.supportsProtocol('rest')) {
            const routes = (this.registry as any).getRouteDefinitions?.() || [];
            const restRoutes = routes.filter((route: any) => route.metadata.rest !== null);
            await adapter.registerRoutes(restRoutes);
        }

        // Register tRPC router if tRPC protocol is enabled
        if (adapter.supportsProtocol('trpc')) {
            const trpcRouter = this.getTRPCRouter();
            await adapter.registerTRPCRouter(trpcRouter);
        }

        // Start adapter
        await adapter.start();

        // Store adapter for lifecycle management
        this._adapters.set('http', adapter);
    }

    private async startMCPAdapter(serverConfig: NonNullable<CQRSRunConfig['mcp']>): Promise<void> {
        let adapter: any;

        // Create adapter instance
        if (typeof serverConfig.adapter === 'string') {
            // Built-in adapter
            switch (serverConfig.adapter) {
                case 'fastmcp':
                    const { FastMCPAdapter } = await import('../adapters/mcp/mcp-adapter');
                    adapter = new FastMCPAdapter({ cqrs: this });
                    break;
                default:
                    throw new Error(`Unknown MCP adapter: ${(serverConfig as any)?.adapter}`);
            }
        } else {
            // Custom adapter instance
            adapter = serverConfig.adapter;
        }

        // Type guard to ensure adapter implements MCPAdapter interface
        if (!adapter ||
            typeof adapter.configure !== 'function' ||
            typeof adapter.registerRoutes !== 'function' ||
            typeof adapter.registerResources !== 'function' ||
            typeof adapter.registerResourceTemplates !== 'function') {
            throw new Error('Invalid MCP adapter: must implement MCPAdapter interface');
        }

        // Configure adapter
        await adapter.configure(serverConfig.config || {});

        // Register routes (procedures with MCP metadata)
        const routes = (this.registry as any).getRouteDefinitions?.() || [];
        const mcpRoutes = routes.filter((route: any) => route.metadata.mcp !== null);
        await adapter.registerRoutes(mcpRoutes);

        // Register resources and resource templates
        await adapter.registerResources(Array.from(this._mcpResources.values()));
        await adapter.registerResourceTemplates(Array.from(this._mcpResourceTemplates.values()));

        // Start adapter
        await adapter.start();

        // Store adapter for lifecycle management
        this._adapters.set('mcp', adapter);
    }

    public async stop(): Promise<void> {
        const promises: Promise<void>[] = [];

        // Stop all adapters
        for (const [_, adapter] of this._adapters) {
            if (adapter.stop) {
                promises.push(adapter.stop());
            }
        }

        await Promise.all(promises);
        this._adapters.clear();
    }

    getRouteDefinitions() {
        return this.registry.getRouteDefinitions?.() || [];
    }

    public getTRPCRouter(): RegistryToTRPC<TRegistry['_def']> {
        return this.registry._trpc as RegistryToTRPC<TRegistry['_def']>;
    }

    /**
     * Enable mocking for this CQRS instance. This swaps the real registry with a mocked version,
     * and all subsequent calls to commands and queries will go through the mock system.
     *
     * @param ctx - Optional context or context function for the mocked registry
     * @returns The mocked registry for direct access to spy methods
     */
    public enableMocking(ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)): MockedRegistry<TRegistry['_def'], TCustomContext> {
        if (this._isMocked) {
            // Already mocked, return the current registry
            return this.registry as MockedRegistry<TRegistry['_def'], TCustomContext>;
        }

        // Create mocked registry
        const mockedRegistry = this.registry.enableMocking(ctx || this._innerGetContext);

        // Replace the registry
        this.registry = mockedRegistry as any;
        this._isMocked = true;

        // Update commands and queries to use mocked procedures
        const procedures = Object.entries(mockedRegistry._def);
        const mockedCaller = mockedRegistry.createCaller({
            getContext: this._innerGetContext,
        });

        // Update commands
        const commands = Object.fromEntries(
            procedures
                .filter(([_, procedure]) => (procedure as any)._type === 'command')
                .map(([key]) => [key, mockedCaller[key]])
        );

        // Update queries 
        const queries = Object.fromEntries(
            procedures
                .filter(([_, procedure]) => (procedure as any)._type === 'query')
                .map(([key]) => [key, mockedCaller[key]])
        );

        // Replace the readonly properties (using Object.defineProperty to bypass readonly)
        Object.defineProperty(this, 'commands', {
            value: commands,
            writable: false,
            enumerable: true,
            configurable: true
        });

        Object.defineProperty(this, 'queries', {
            value: queries,
            writable: false,
            enumerable: true,
            configurable: true
        });

        return mockedRegistry as MockedRegistry<TRegistry['_def'], TCustomContext>;
    }

    /**
     * Check if this CQRS instance is currently using mocked procedures
     */
    public isMocked(): boolean {
        return this._isMocked;
    }
}

/**
 * Usage:
 * const { define, registry } = CQRS.create<TestContext>();
 */
export class CQRS<TRegistry extends Registry<any, any>, TCustomContext extends Record<string, any> = Record<string, any>> implements CQRSInterface<TRegistry, TCustomContext> {
    private _internal: CQRSInternal<TRegistry, TCustomContext>;

    readonly queries: InferProceduresFromRecord<TRegistry['_def'], 'query', TCustomContext>;
    readonly commands: InferProceduresFromRecord<TRegistry['_def'], 'command', TCustomContext>;

    public static create<TCustomContext extends object = Record<string, any>>(options?: CreateCQRSOptions) {
        return {
            define: createDefine<TCustomContext>(options),
            registry: createRegistry<TCustomContext>(),
        }
    }

    constructor(opts: CQRSOptions<TRegistry, TCustomContext>) {
        this._internal = new CQRSInternal(opts);
    
        // Expose only the interface properties
        this.queries = this._internal.queries;
        this.commands = this._internal.commands;
    }

    public async run(config?: CQRSRunConfig): Promise<CQRSServer> {
        return this._internal.run(config);
    }

    public async stop(): Promise<void> {
        return this._internal.stop();
    }

    public getTRPCRouter(): RegistryToTRPC<TRegistry['_def']> {
        return this._internal.getTRPCRouter();
    }

    public enableMocking(ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)): MockedRegistry<TRegistry['_def'], TCustomContext> {
        return this._internal.enableMocking(ctx);
    }

    public getContext() {
        return this._internal.getContext();
    }

    public addResource(resource: MCPResource): void {
        return this._internal.addResource(resource);
    }

    public addResourceTemplate(template: MCPResourceTemplate): void {
        return this._internal.addResourceTemplate(template);
    }

    public async embedded(uri: string): Promise<MCPResourceContent> {
        return this._internal.embedded(uri);
    }

    public getRouteDefinitions() {
        return this._internal.getRouteDefinitions();
    }

    public getMetadata() {
        // Expose metadata
        return (this._internal as any).getMetadata?.();
    }
}
