import { v4 as uuidv4 } from 'uuid';
import { AsyncLocalStorage } from 'async_hooks';

/**
 * Base structure for all execution contexts.
 * Custom data can be added by extending this interface.
 */
export interface BaseExecutionContext {
    /** Unique identifier for the current trace or operation. */
    readonly traceId: string;
    /** Identifier of the parent trace or operation, if any. */
    readonly parentId?: string;
    /** Timestamp (in milliseconds) when this context scope was entered. */
    readonly startTime: number;
}

// Stores the active execution context (BaseExecutionContext & TCustomContext)
const executionContextStorage = new AsyncLocalStorage<BaseExecutionContext & Record<string, any>>();

/**
 * Generates a unique trace ID.
 * @returns A new UUID v4 string.
 */
export const generateTraceId = (): string => uuidv4();

/**
 * Runs a callback function within a new or existing execution context.
 * This is the core function for establishing a managed asynchronous context.
 *
 * @param options Configuration for the execution context.
 *   - `traceId`: Explicitly set the traceId for this scope. If not provided, it's inherited or generated.
 *   - `parentId`: Explicitly set the parentId for this scope.
 *   - `inheritTraceId`: If true (default) and no `traceId` is provided, the `traceId` from an existing parent context is used. If false or no parent context, a new `traceId` is generated.
 *   - `initialContext`: Custom data to be included in this execution context.
 * @param callback The function to execute within the context. It receives the established context as an argument.
 * @returns The promise returned by the callback function.
 */
export function runInContext<R, TCustomContext extends Record<string, any> = Record<string, any>>(
    options: {
        traceId?: string;
        parentId?: string;
        inheritTraceId?: boolean; 
        initialContext?: TCustomContext | ((context: { traceId: string, parentId?: string }) => TCustomContext);
    },
    callback: (context: BaseExecutionContext & TCustomContext) => Promise<R>
): Promise<R> {
    const parentContext = executionContextStorage.getStore();
    let currentTraceId: string;
    let currentParentId: string | undefined = options.parentId; // Explicit parentId always wins

    const shouldInheritTraceId = options.inheritTraceId !== undefined ? options.inheritTraceId : true;

    if (options.traceId) {
        currentTraceId = options.traceId;
        // If traceId is provided, parentId is either also provided or is parentContext's traceId (if different)
        if (currentParentId === undefined && parentContext && parentContext.traceId !== currentTraceId) {
            currentParentId = parentContext.traceId;
        }
    } else if (shouldInheritTraceId && parentContext) {
        currentTraceId = parentContext.traceId;
        // Inheriting traceId, so also inherit parentContext's parentId unless explicitly overridden by options.parentId
        if (currentParentId === undefined) {
           currentParentId = parentContext.parentId;
        }
    } else {
        currentTraceId = generateTraceId();
        // New trace, so parent is the parentContext's traceId (if any and if not already set by options.parentId)
        if (currentParentId === undefined && parentContext) {
            currentParentId = parentContext.traceId;
        }
    }

    const initialContext = typeof options.initialContext === 'function'
    ? (options.initialContext as any)({ traceId: currentTraceId, parentId: currentParentId })
    : options.initialContext;

    const baseProperties: BaseExecutionContext = {
        traceId: currentTraceId,
        startTime: Date.now(),
        // Conditionally add parentId to the base properties
        ...(currentParentId && { parentId: currentParentId }),
    };

    // Spread initialContext first, then spread baseProperties.
    // This ensures that traceId, parentId (if present), and startTime from baseProperties
    // will overwrite any identically named properties in initialContext.
    const context: BaseExecutionContext & TCustomContext = {
        ...(initialContext || {} as TCustomContext),
        ...baseProperties,
    };

    return executionContextStorage.run(context, () => callback(context));
}

/**
 * A convenience wrapper for `runInContext` that simplifies starting a new trace or continuing an existing one.
 * It always attempts to inherit the `traceId` and `parentId` from an active context if available.
 *
 * @param initialContextOrFn Either an object containing the initial custom context data,
 *                           or a function that returns such an object. The function can accept the parent context.
 * @param callback The function to execute within the context.
 * @returns The promise returned by the callback function.
 */
export function run<R, TCustomContext extends Record<string, any> = Record<string, any>>(
    initialContextOrFn: TCustomContext | ((parentContext?: BaseExecutionContext & Record<string, any>) => TCustomContext) | undefined,
    callback: (context: BaseExecutionContext & TCustomContext) => Promise<R>
): Promise<R> {
    const parentContext = executionContextStorage.getStore();
    const initialContext = typeof initialContextOrFn === 'function'
        ? initialContextOrFn(parentContext)
        : initialContextOrFn;

    return runInContext(
        {
            inheritTraceId: true, 
            initialContext: initialContext || {} as TCustomContext
        },
        callback
    );
}

/**
 * Retrieves the currently active execution context.
 * @returns The current context object (including BaseExecutionContext and custom data), or undefined if not within a context.
 */
export function getCurrentContext<TCustomContext extends Record<string, any> = Record<string, any>>(): (BaseExecutionContext & TCustomContext) | undefined {
    return executionContextStorage.getStore() as (BaseExecutionContext & TCustomContext) | undefined;
}

/**
 * Adds or updates data in the currently active execution context.
 * If no context is active, this function does nothing.
 *
 * @param data An object containing the data to merge into the current context. Existing keys will be overwritten.
 */
export function addToCurrentContext(data: Record<string, any>): void {
    const currentContext = executionContextStorage.getStore();
    if (currentContext) {
        Object.assign(currentContext, data);
    }
}
