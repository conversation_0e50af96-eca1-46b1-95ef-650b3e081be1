import { z } from 'zod';
import type {
    Define,
    Command,
    Query,
    ProcedureParams,
    InputType,
    OutputType,
    ProcedureBuilder,
    MiddlewareFunction,
    ProcedureType,
    ProcedureMetadata,
    CreateDefineOptions,
} from './types';
import { initTRPC } from '@trpc/server';

// Deep merge utility for metadata objects
export const deepMergeMetadata = (base?: ProcedureMetadata, ...overrides: (ProcedureMetadata | undefined)[]): ProcedureMetadata => {
    if (!base) return overrides.find(Boolean) || {};
    if (!overrides.length) return base;
    return { ...base, ...overrides.reduce((acc, override) => ({ ...acc, ...override }), {}) };
};

export const createDefine = <TCustomTrace extends Record<string, any> = Record<string, any>>(options?: CreateDefineOptions): Define<TCustomTrace> => {
    const t = initTRPC.context<TCustomTrace>().create();

    const createProcedureBuilder = <TType extends ProcedureType>(
        type: TType,
        middlewares: MiddlewareFunction<any, any>[] = []
    ): ProcedureBuilder<TType, TCustomTrace> => {
        const builder = {
            use: <TNewContext extends Record<string, any> = TCustomTrace>(
                middleware: MiddlewareFunction<TCustomTrace, TNewContext>
            ) => {
                return createProcedureBuilder(type, [...middlewares, middleware]);
            },
        };

        // Add the procedure creation function
        const procedureCreator = <TInput extends InputType, TOutput extends OutputType>(
            def: ProcedureParams<TInput, TOutput, TCustomTrace>
        ) => {
            // Create enhanced handler that runs through middleware stack
            const createEnhancedHandler = (procedureName?: string) => {
                return async (opts: { input: any; ctx: any }) => {
                    let currentIndex = 0;

                    const next = async (overrides?: { ctx?: any }): Promise<any> => {
                        const ctx = {
                            ...(opts.ctx || {}),
                            ...(overrides?.ctx || {}),
                            ...(procedureName ? { procedureName } : {})
                        };

                        if (currentIndex < middlewares.length) {
                            const middleware = middlewares[currentIndex++];
                            return middleware({
                                ctx,
                                input: opts.input,
                                path: procedureName || 'unknown',
                                type,
                                next
                            });
                        } else {
                            // Call the original handler
                            return def.handler({ input: opts.input, ctx });
                        }
                    };

                    return next();
                };
            };

            // TODO: Fix this with a better deep merge
            const metadata = deepMergeMetadata(options?.defaultMetadata, def.metadata);

            const enhancedDef = {
                ...def,
                metadata,
                handler: createEnhancedHandler(), // Default handler without procedure name
                _middlewares: middlewares, // Store middlewares for registry use
                _createEnhancedHandler: createEnhancedHandler // Store factory for creating handlers with procedure name
            };

            if (type === 'command') {
                return {
                    _type: 'command',
                    _def: enhancedDef,
                    _trpc: t.procedure
                        .input(def.input)
                        .output(def.output || z.any())
                        .meta(metadata as any)
                        .mutation(null as any),
                } as unknown as Command<TInput, TOutput, TCustomTrace>;
            } else {
                return {
                    _type: 'query',
                    _def: enhancedDef,
                    _trpc: t.procedure
                        .input(def.input)
                        .output(def.output || z.any())
                        .meta(metadata as any)
                        .query(null as any),
                } as unknown as Query<TInput, TOutput, TCustomTrace>;
            }
        };

        return Object.assign(procedureCreator, builder) as ProcedureBuilder<TType, TCustomTrace>;
    };

    return {
        command: createProcedureBuilder('command'),
        query: createProcedureBuilder('query'),
    };
};
