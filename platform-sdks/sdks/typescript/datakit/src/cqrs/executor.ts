import type { 
    Procedure, 
    TraceContext, 
    CommandObserver, 
    CommandObserverOpts,
} from './types';

export interface ExecutorOptions<TCustomContext> {
    observers?: Set<CommandObserver<TCustomContext>>;
    onError?: (opts: {
        error: Error;
        input: unknown;
        name: string;
        type: 'command' | 'query';
        ctx?: TCustomContext;
    }) => void;
}

export class Executor {
    /**
     * Centralized handler execution for both registry and CQRS
     */
    static async execute<TInput, TOutput, TCustomContext>(
        procedureName: string,
        procedure: Procedure<any, any, any, any, TCustomContext>,
        input: TInput,
        context: TraceContext<TCustomContext>,
        options?: ExecutorOptions<TCustomContext>
    ): Promise<TOutput> {
        const startTime = Date.now();

        try {
            // Get handler - check for enhanced handler first
            const handler = (procedure._def as any)._createEnhancedHandler?.(procedureName) ?? procedure._def.handler;

            // Parse input if procedure has input validation
            const parsedInput = procedure._def.input ? await procedure._def.input.parseAsync(input) : input;

            // Execute handler
            let result = await handler({ input: parsedInput, ctx: context });

            // Validate output
            if (procedure._def.output) {
                result = await procedure._def.output.parseAsync(result);
            }

            // Strip _mcp from results for non-MCP protocols
            const origin = (context as any).origin;
            if (origin !== 'MCP') {
                result = this.stripMCP(result);
            }

            const endTime = Date.now();

            // Notify observers for commands
            if (procedure._type === 'command' && options?.observers) {
                this.notifyObservers(options.observers, {
                    commandName: procedureName,
                    metadata: procedure._def.metadata || {},
                    input: parsedInput,
                    output: result,
                    ctx: context,
                    startTime,
                    endTime,
                });
            }

            return result;

        } catch (cause) {
            const error = cause instanceof Error ? cause : new Error(String(cause));
            const endTime = Date.now();

            // Notify observers of error for commands
            if (procedure._type === 'command' && options?.observers) {
                this.notifyObservers(options.observers, {
                    commandName: procedureName,
                    metadata: procedure._def.metadata || {},
                    input: input,
                    error,
                    ctx: context,
                    startTime,
                    endTime,
                });
            }

            // Handle errors through the error handler if provided
            if (options?.onError) {
                options.onError({
                    error,
                    input,
                    name: procedureName,
                    type: procedure._type,
                    ctx: context,
                });
            }

            throw error;
        }
    }

    /**
     * Strip _mcp from results for non-MCP protocols
     */
    private static stripMCP(data: any): any {
        if (data && typeof data === 'object') {
            const { _mcp, ...rest } = data as any;
            return rest;
        }
        return data;
    }

    /**
     * Notify observers without affecting command execution
     */
    private static async notifyObservers<TCustomContext>(
        observers: Set<CommandObserver<TCustomContext>>,
        opts: CommandObserverOpts<TCustomContext>
    ): Promise<void> {
        // Execute each observer in isolation
        const notifyPromises = Array.from(observers).map(async (observer) => {
            try {
                await observer.onCommandExecuted(opts);
            } catch (error) {
                console.error(`Error in command observer for command ${opts.commandName}:`, error);
            }
        });

        // Wait for all observers but ignore errors
        await Promise.allSettled(notifyPromises);
    }
}
