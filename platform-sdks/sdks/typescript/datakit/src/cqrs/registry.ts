import type { Registry, ProcedureRecord, RouteDefinition, HttpMethod, ProcedureMetadata, RegistryCaller, RegistryCallerOptions, DecorateRegistryRecord, Procedure, MockedRegistry, TraceContext, MaybePromise, CommandObserver } from './types';
import { initTRPC } from '@trpc/server';
import { runWithTrace, runWithinExistingTrace, getCurrentTrace } from './trace';
import { z } from 'zod';
import { createMockedRegistry } from './mock';
import { Executor } from './executor';

export const createRegistry = <TCustomContext extends Record<string, any> = Record<string, any>>() => {
    const t = initTRPC.context<TCustomContext>().meta<ProcedureMetadata>().create();
    return <TRegistryDef extends ProcedureRecord<TCustomContext>>(def: TRegistryDef): Registry<TRegistryDef, TCustomContext> => {
        // Store observers
        const commandObservers = new Set<CommandObserver<TCustomContext>>();

        // Validate for duplicate route names
        const routeNames = Object.keys(def);
        const duplicates = routeNames.filter((name, index) => routeNames.indexOf(name) !== index);
        if (duplicates.length > 0) {
            throw new Error(`Duplicate route names found: ${duplicates.join(', ')}`);
        }

        function getRouteDefinitions(): RouteDefinition[] {
            const routes: RouteDefinition[] = [];

            for (const [procedureName, procedure] of Object.entries(def)) {
                const procedureDef = procedure._def;

                const metadata: ProcedureMetadata = procedureDef.metadata || {};

                routes.push({
                    procedureName,
                    procedureType: procedure._type,
                    inputSchema: procedureDef.input,
                    outputSchema: procedureDef.output,
                    metadata: {
                        ...metadata,
                        rest: !metadata.rest ? null : computeRestMetadata(procedureName, procedure._type, metadata)
                    },
                    handler: procedureDef.handler,
                });
            }

            return routes;
        }

        const createCaller: RegistryCaller<TRegistryDef, TCustomContext> = (
            options?: RegistryCallerOptions<TCustomContext>
        ) => {
            const result = {} as DecorateRegistryRecord<TRegistryDef, TCustomContext>;

            for (const [key, procedure] of Object.entries(def)) {
                const caller = createProcedureCaller(key, procedure, options);
                (result as any)[key] = caller;
            }

            return result;
        };

        function createProcedureCaller(
            key: string,
            procedure: Procedure<any, any, any, any, TCustomContext>,
            options?: RegistryCallerOptions<TCustomContext>
        ) {
            const executeHandler = async (input: unknown, traceContext: TraceContext<TCustomContext>) => {
                return Executor.execute(
                    key,
                    procedure,
                    input,
                    traceContext,
                    {
                        observers: commandObservers,
                        onError: options?.onError,
                    }
                );
            };

            const execute = async (input: unknown, ctx?: TCustomContext) => {
                try {
                    // Check if we're already within a trace context (nested call)
                    const isNested = getCurrentTrace() !== undefined;

                    if (isNested) {
                        // For nested calls, run within the existing trace context to ensure proper inheritance
                        return runWithinExistingTrace((traceContext) => executeHandler(input, traceContext));
                    }

                    return runWithTrace(
                        undefined,
                        undefined,
                        key,
                        procedure._type,
                        input,
                        options?.getContext,
                        (traceContext) => executeHandler(input, {
                            ...traceContext,
                            ...ctx,
                        })
                    );
                } catch (cause) {
                    // This catch block is now mainly for context resolution errors
                    // Handler execution errors are caught in executeHandler
                    if (options?.onError) {
                        options.onError({
                            error: cause instanceof Error ? cause : new Error(String(cause)),
                            input,
                            name: key,
                            type: procedure._type,
                        });
                    }
                    throw cause;
                }
            };

            return execute;
        }

        const trpcProcedures = Object.entries(def).filter(([_, procedure]) => !!procedure._def.metadata?.trpc);
        const trpcRouter = t.router(Object.fromEntries(
            trpcProcedures.map(([key, procedure]) => {
                // Create a new tRPC procedure that uses the registry caller
                const baseProcedure = t.procedure
                    .input(procedure._def.input || z.any())
                    .output(procedure._def.output || z.any())
                    .meta(procedure._def.metadata as any);

                const procedureCaller = createProcedureCaller(key, procedure) as any;
                if (procedure._type === 'command') {
                    return [key, baseProcedure.mutation(async (args) => {
                        return await procedureCaller(args.input, args.ctx);
                    })];
                } else {
                    return [key, baseProcedure.query(async (args) => {
                        return await procedureCaller(args.input, args.ctx);
                    })];
                }
            })
        )) as any;

        trpcRouter.createCaller = (ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)) => createCaller({
            getContext: typeof ctx === 'function' ? ctx : () => ctx as any,
        });

        return {
            _def: def,
            _ctx: {} as TCustomContext,
            _trpc: trpcRouter,
            getRouteDefinitions,
            createCaller,
            createProcedureCaller,
            // Introspection methods
            getCommandNames(): string[] {
                return Object.entries(def)
                    .filter(([_, procedure]) => procedure._type === 'command')
                    .map(([name]) => name);
            },
            getQueryNames(): string[] {
                return Object.entries(def)
                    .filter(([_, procedure]) => procedure._type === 'query')
                    .map(([name]) => name);
            },
            getAllProcedureNames(): string[] {
                return Object.keys(def);
            },
            getProcedureMetadata(name: string) {
                const procedure = def[name];
                if (!procedure) {
                    throw new Error(`Procedure '${name}' not found`);
                }
                return {
                    ...procedure._def.metadata,
                    type: procedure._type,
                    inputSchema: procedure._def.input,
                    outputSchema: procedure._def.output,
                };
            },
            // Mocking functionality
            enableMocking(ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)): MockedRegistry<TRegistryDef, TCustomContext> {
                // Use provided context or create a default context function
                const contextOrCallback = ctx || (() => ({} as TCustomContext));
                return createMockedRegistry(this as any, contextOrCallback) as any;
            },
            // Command observer methods
            addCommandObserver(observer: CommandObserver<TCustomContext>): void {
                commandObservers.add(observer);
            },
            removeCommandObserver(observer: CommandObserver<TCustomContext>): void {
                commandObservers.delete(observer);
            },
        };
    };
};

// Smart metadata computation with inheritance and defaults
function computeRestMetadata(
    procedureName: string,
    procedureType: 'command' | 'query',
    metadata: ProcedureMetadata
) {
    // Extract REST-specific metadata or use empty object
    const restMeta = typeof metadata.rest === 'object' ? metadata.rest || {} : {};

    // Smart defaults based on procedure type
    const defaultMethod: HttpMethod = procedureType === 'query' ? 'GET' : 'POST';
    const defaultPath = `/${procedureName}`;

    // Inherit common fields and merge with REST-specific overrides
    const summary = metadata.title;
    const description = metadata.description;
    const tags = metadata.tags;
    const deprecated = metadata.deprecated;

    return {
        ...metadata,
        method: restMeta.method || defaultMethod,
        path: restMeta.path || defaultPath,
        openapi: {
            summary,
            description,
            tags,
            deprecated,
            operationId: restMeta?.openapi?.operationId || `${procedureType}_${procedureName}`,
            ...restMeta?.openapi,
        }
    };
}
