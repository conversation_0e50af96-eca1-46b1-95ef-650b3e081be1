import { AnyTRPCProcedure, TRPCMutationProcedure, TRPCQueryProcedure } from '@trpc/server';
import type { BuiltRouter } from '@trpc/server/unstable-core-do-not-import';
import type { z, AnyZodObject as ZodAnyObject, ZodTypeAny, ZodNullable, ZodOptional, ZodVoid, ZodUndefined, ZodNull, ZodAny } from 'zod';

// Used for input schema
export type InputType = ZodAnyObject | ZodNullable<ZodAnyObject> | ZodOptional<ZodAnyObject> | ZodVoid | ZodUndefined | ZodNull | ZodAny;
export type OutputType = ZodTypeAny;

// Utility types
export type MaybePromise<TType> = Promise<TType> | TType;

// Utility types for detecting optional input cases
export type IsVoidInput<T> = T extends void ? true : false;
export type IsAnyInput<T> = 0 extends (1 & T) ? true : false;
export type IsNullishInput<T> = T extends null | undefined ? true : false;
export type IsOptionalInput<T> = T extends undefined ? true : false;

// Check if input should be optional (void, any, null, undefined, or optional)
// This covers most common cases where input should be optional
export type ShouldInputBeOptional<T> = 
  IsVoidInput<T> extends true ? true :
  IsAnyInput<T> extends true ? true :
  IsNullishInput<T> extends true ? true :
  undefined extends T ? true :  // This catches optional types better
  false;

// Enhanced context type that includes trace access and MCP capabilities
export type EnhancedContext<TContext> = TContext & {
    mcp?: MCPContext;
    /**
     * Origin of the current call (e.g. 'REST', 'tRPC', 'MCP', 'INTERNAL').
     * Useful for distinguishing execution environments.
     */
    origin?: ProcedureOrigin;
};

// REST Metadata Types
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';

export type RestMetadata = {
    method?: HttpMethod;
    path?: string;
    // OpenAPI fields can be overridden at REST level if needed
    openapi?: {
        summary?: string;
        description?: string;
        tags?: string[];
        deprecated?: boolean;
        operationId?: string;
        externalDocs?: {
            description?: string;
            url: string;
        };
    }
};

export type TRPCMetadata = {
};

// MCP (Model Context Protocol) Metadata Types - compatible with FastMCP annotations
export type MCPMetadata = {
    enabled?: boolean;
    openWorldHint?: boolean; // Tool interacts with external entities
    streamingHint?: boolean; // Tool supports streaming output
    readOnlyHint?: boolean; // Tool doesn't modify environment
    destructiveHint?: boolean; // Tool may perform destructive updates
    idempotentHint?: boolean; // Repeated calls have no additional effect
    timeoutMs?: number;
    /**
     * Additional custom keys are allowed for forward compatibility.
     * @internal
     */
    [key: string]: unknown;
};

// MCP Content types compatible with FastMCP
export type MCPTextContent = {
    type: 'text';
    text: string;
};

export type MCPImageContent = {
    type: 'image';
    data: string; // base64 encoded
    mimeType: string;
};

export type MCPResourceContent = {
    type: 'resource';
    resource: {
        uri: string;
        text?: string;
        blob?: string; // base64 encoded binary data
        mimeType?: string;
    };
};

export type MCPContent = MCPTextContent | MCPImageContent | MCPResourceContent;

// MCP Context types for enhanced handler execution
export type MCPContext = {
    streamContent: (content: MCPContent | MCPContent[]) => Promise<void>;
    reportProgress: (progress: { progress: number; total?: number }) => Promise<void>;
    /** @deprecated Logging moved to CQRS level */
    log?: any;
};

// MCP Resource types - Generic compatible with FastMCP
export type MCPResourceResult = {
    text?: string;
    blob?: string; // base64 encoded binary data
    mimeType?: string;
};

export type MCPResource = {
    uri: string;
    name: string;
    description?: string;
    mimeType?: string;
    load: () => Promise<MCPResourceResult> | MCPResourceResult;
};

// MCP Resource Template types
export type MCPResourceTemplateArgument = {
    name: string;
    description?: string;
    required: boolean;
};

export type MCPResourceTemplate = {
    uriTemplate: string;
    name: string;
    description?: string;
    mimeType?: string;
    arguments: MCPResourceTemplateArgument[];
    load: (args: Record<string, string>) => Promise<MCPResourceResult> | MCPResourceResult;
};



/**
 * General metadata for the CQRS class
 * */
export type CQRSMetadata = {
    serviceName?: string;
    serviceVersion?: string;
    serviceType?: (string & {}) | "etl" | "transform" | "analytics" | "api" | "web";
};

// Simplified metadata structure with common fields at top level
export type ProcedureMetadata = {
    // Common fields that apply across all protocols
    title?: string;
    description?: string;
    tags?: string[];
    deprecated?: boolean;

    // Cross-cutting concerns
    // rateLimit?: {
    //     requests: number;
    //     window: string;
    // };
    // cache?: boolean | {
    //     enabled?: boolean;
    //     ttl?: string;
    //     key?: string | ((input: any) => string);
    // };

    // Protocol-specific configurations
    rest?: RestMetadata | null;
    mcp?: MCPMetadata | null;
    trpc?: TRPCMetadata | null;
    // Future: grpc?, graphql?, pubsub?, etc.
};

export type ProcedureParams<TInput extends InputType, TOutput extends OutputType, TCustomContext = Record<string, any>> = {
    input: TInput;
    output: TOutput;
    metadata?: ProcedureMetadata;
    handler: (opts: { input: z.infer<TInput>; ctx: EnhancedContext<TCustomContext> }) => Promise<z.infer<TOutput>>;
    // _middlewares?: MiddlewareFunction<any, any>[];
    // _createEnhancedHandler?: (procedureName?: string) => (opts: { input: any; ctx: any }) => Promise<any>;
};

export type ProcedureType = 'query' | 'command';

export type Procedure<TType extends ProcedureType, TRPCType extends AnyTRPCProcedure, TInput extends InputType, TOutput extends OutputType, TCustomContext = Record<string, any>> = {
    _type: TType;
    _def: ProcedureParams<TInput, TOutput, TCustomContext>;
    _trpc: TRPCType;
};

export type Command<TInput extends InputType, TOutput extends OutputType, TCustomTrace extends Record<string, any>> =
    Procedure<'command', TRPCMutationProcedure<{
        input: TInput;
        output: TOutput;
        meta: ProcedureMetadata;
    }>, TInput, TOutput, TCustomTrace>;

export type Query<TInput extends InputType, TOutput extends OutputType, TCustomTrace extends Record<string, any> = Record<string, any>> =
    Procedure<'query', TRPCQueryProcedure<{
        input: TInput;
        output: TOutput;
        meta: ProcedureMetadata;
    }>, TInput, TOutput, TCustomTrace>;

export type AnyProcedure<TInput extends InputType, TOutput extends OutputType, TCustomTrace extends Record<string, any> = Record<string, any>> =
    Command<TInput, TOutput, TCustomTrace> | Query<TInput, TOutput, TCustomTrace>;

export type InferProceduresFromRecord<
    TRecord extends ProcedureRecord<TCustomContext>,
    TType extends ProcedureType,
    TCustomContext extends Record<string, any> = Record<string, any>
> = {
    [K in keyof TRecord as TRecord[K]['_type'] extends TType ? K : never]: 
        TRecord[K] extends Procedure<any, any, any, any, TCustomContext>
            ? TType extends 'command'
                ? CommandHandler<InferProcedureInput<TRecord[K]>, InferProcedureOutput<TRecord[K]>>
                : QueryHandler<InferProcedureInput<TRecord[K]>, InferProcedureOutput<TRecord[K]>>
            : never;
};
// Middleware types
export type MiddlewareFunction<TContext extends Record<string, any> = Record<string, any>, TNewContext extends Record<string, any> = Record<string, any>> = (opts: {
    ctx: TContext;
    input: any;
    path: string;
    type: 'command' | 'query';
    next: (opts?: { ctx?: TNewContext }) => Promise<any>;
}) => Promise<any>;

export type ProcedureBuilder<TType extends ProcedureType, TContext extends Record<string, any> = Record<string, any>> = {
    use: <TNewContext extends Record<string, any> = Record<string, any>>(
        middleware: MiddlewareFunction<TContext, TNewContext>
    ) => ProcedureBuilder<TType, TContext & TNewContext>;
} & (TType extends 'command'
    ? <TInput extends InputType, TOutput extends OutputType>(
        def: ProcedureParams<TInput, TOutput, TContext>
      ) => Command<TInput, TOutput, TContext>
    : <TInput extends InputType, TOutput extends OutputType>(
        def: ProcedureParams<TInput, TOutput, TContext>
      ) => Query<TInput, TOutput, TContext>
);

export type Define<TCustomTrace extends Record<string, any> = Record<string, any>> = {
    command: ProcedureBuilder<'command', TCustomTrace>;
    query: ProcedureBuilder<'query', TCustomTrace>;
};

export type CreateDefineOptions = {
    defaultMetadata?: ProcedureMetadata;
}

export type CreateCQRSOptions = CreateDefineOptions;

export type ProcedureRecord<TCustomContext extends Record<string, any> = Record<string, any>> = Record<string, Procedure<ProcedureType, any, any, any, TCustomContext>>;
export type RegistryDef<TCustomContext extends Record<string, any> = Record<string, any>> = ProcedureRecord<TCustomContext>;

export type DefineToTRPC<T extends AnyProcedure<any, any, any>> = T extends Command<infer TInput, infer TOutput, any> ? TRPCMutationProcedure<{
    input: z.infer<TInput>;
    output: z.infer<TOutput>;
    meta: ProcedureMetadata;
}> : T extends Query<infer TInput, infer TOutput, any> ? TRPCQueryProcedure<{
    input: z.infer<TInput>;
    output: z.infer<TOutput>;
    meta: ProcedureMetadata;
}> : never;

export type RegistryToTRPC<T extends ProcedureRecord<any>> = T extends ProcedureRecord<infer TCustomContext> ? BuiltRouter<{
    ctx: TCustomContext;
    meta: ProcedureMetadata;
    errorShape: any;
    transformer: any;
}, {
    [K in keyof T]: DefineToTRPC<T[K]>;
}> : never;

export type CommandObserverOpts<TCustomContext = any> = {
    commandName: string;
    metadata: ProcedureMetadata;
    input: unknown;
    output?: unknown;
    error?: Error;
    ctx: TCustomContext;
    startTime: number;
    endTime: number;
};

export type CommandObserver<TCustomContext = any> = {
    /**
     * Called when a command is executed (success or failure)
     */
    onCommandExecuted: (opts: CommandObserverOpts<TCustomContext>) => void | Promise<void>;
};

export type Registry<TRegistryDef extends ProcedureRecord<any>, TCustomContext extends Record<string, any> = Record<string, any>> = {
    _def: TRegistryDef;
    _ctx: TCustomContext;
    _trpc: RegistryToTRPC<TRegistryDef>;
    getRouteDefinitions: () => RouteDefinition[];
    createCaller: RegistryCaller<TRegistryDef, TCustomContext>;
    createProcedureCaller: (procedureName: string, procedure: Procedure<any, any, any, any, TCustomContext>) => (input: unknown, ctx?: TCustomContext) => Promise<any>;
    // Introspection methods
    getCommandNames(): string[];
    getQueryNames(): string[];
    getAllProcedureNames(): string[];
    getProcedureMetadata(name: string): ProcedureMetadata & {
        type: 'command' | 'query';
        inputSchema: InputType;
        outputSchema: InputType;
    };
    enableMocking(ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)): MockedRegistry<TRegistryDef, TCustomContext>;
    // Command observer methods
    addCommandObserver(observer: CommandObserver<TCustomContext>): void;
    removeCommandObserver(observer: CommandObserver<TCustomContext>): void;
};

export type InferProcedureInput<TProcedure extends Procedure<any, any, any, any, any>> =
    z.infer<TProcedure['_def']['input']>;

export type InferProcedureOutput<TProcedure extends Procedure<any, any, any, any, any>> =
    z.infer<TProcedure['_def']['output']>;

// Enhanced trace context types for flexible tracing
export type BaseTraceContext = {
    traceId: string;
    parentId?: string;
    startTime: number;
};

// User-defined trace context that extends the base
export type TraceContext<TCustomTrace = Record<string, any>> = BaseTraceContext & TCustomTrace;

export type ProcedureOrigin = 'REST' | 'tRPC' | 'MCP' | (string & {});

// Function to create custom trace context
export type CreateContext<TCustomTrace = Record<string, any>> = (params: {
    traceId: string;
    parentId?: string;
    lineageId?: string;
    procedureName: string;
    procedureType: 'command' | 'query';
    input: unknown;
    rest?: {
        headers?: Record<string, string>;
        ip?: string;
        url?: string;
        query?: Record<string, string>;
        body?: any;
        [key: string]: any;
    };
    origin?: ProcedureOrigin;
}) => TCustomTrace;

export type ExecuteContextParameters<TCustomContext extends Record<string, any> = Record<string, any>> = Parameters<CreateContext<TCustomContext>>[0] & { mcp?: MCPContext };

// Utility type for accessing trace information in handlers
export type TraceInfo<TCustomTrace = Record<string, any>> = {
    traceId: string;
    parentId?: string;
} & TCustomTrace;

// Command object types for deferred execution
export type CommandObject<TInput, TOutput> = {
    readonly input: TInput;
    readonly commandName: string;
    readonly _type: 'command';
    readonly timestamp: number;
    execute(): Promise<TOutput>;
    // Future: add serialization, metadata, etc.
};

// Enhanced command handler type with both direct and object-based execution
export type CommandHandler<TInput, TOutput> = 
  ShouldInputBeOptional<TInput> extends true
    ? {
        // Direct execution - input optional
        (input?: TInput): Promise<TOutput>;
        // New object-based API - input optional
        handle(input?: TInput): Promise<TOutput>;
        make(input?: TInput): CommandObject<TInput, TOutput>;
      }
    : {
        // Direct execution - input required
        (input: TInput): Promise<TOutput>;
        // New object-based API - input required
        handle(input: TInput): Promise<TOutput>;
        make(input: TInput): CommandObject<TInput, TOutput>;
      };

// Query handler 
export type QueryHandler<TInput, TOutput> = 
  ShouldInputBeOptional<TInput> extends true
    ? (input?: TInput) => Promise<TOutput>
    : (input: TInput) => Promise<TOutput>;

// Metadata utility types
export type InferProcedureMetadata<TProcedure extends Procedure<any, any, any, any>> =
    TProcedure['_def']['metadata'];

export type HasRestMetadata<TProcedure extends Procedure<any, any, any, any>> =
    TProcedure['_def']['metadata'] extends { rest: RestMetadata } ? true : false;

export type ExtractRestMetadata<TProcedure extends Procedure<any, any, any, any>> =
    TProcedure['_def']['metadata'] extends { rest: infer R } ? R : never;

// Route definition for adapters
export type RouteDefinition = {
    procedureName: string;
    procedureType: ProcedureType;
    inputSchema: InputType;
    outputSchema: InputType;
    metadata: ProcedureMetadata;
    handler: (opts: { input: any; ctx: any }) => Promise<any>;
};

// CQRS Runtime Configuration
// This is the unified interface for starting all adapters through cqrs.run()

export type HealthResponse = {
    status: 'healthy' | 'unhealthy' | 'degraded';
    timestamp: number;
    details?: Record<string, any>;
};

export type AdapterStatus = 'stopped' | 'starting' | 'configured' | 'running' | 'stopping' | 'error' | 'unknown';

// Base adapter interface that all adapters must implement
export interface BaseAdapter<TConfig = any> {
    readonly name: string;
    readonly status: AdapterStatus;
    configure(config: TConfig): Promise<void>;
    start(): Promise<void>;
    stop(): Promise<void>;
    getHealth(): Promise<HealthResponse>;
}

export interface HTTPAdapter<TConfig = any> extends BaseAdapter<TConfig> {
    registerRoutes(routes: RouteDefinition[]): Promise<void>;
    registerTRPCRouter(router: any): Promise<void>;
    supportsProtocol(protocol: 'rest' | 'trpc' | 'websocket'): boolean;
}

export interface MCPAdapter<TConfig = any> extends BaseAdapter<TConfig> {
    registerRoutes(routes: RouteDefinition[]): Promise<void>;
    registerResources(resources: MCPResource[]): Promise<void>;
    registerResourceTemplates(templates: MCPResourceTemplate[]): Promise<void>;
    supportsProtocol(protocol: 'mcp'): boolean;
}

// Built-in REST adapter configurations
export type FastifyConfig = {
    port?: number;
    host?: string;
    cors?: boolean | {
        origin?: string | string[];
        credentials?: boolean;
        methods?: string[];
    };
    swagger?: {
        enabled: boolean;
        path?: string;
        title?: string;
        version?: string;
        info?: {
            title?: string;
            version?: string;
            description?: string;
        };
    };
    middleware?: {
        auth?: boolean;
        rateLimit?: boolean;
        compression?: boolean;
        helmet?: boolean;
    };
    fastifyOptions?: {
        logger?: boolean | object;
        trustProxy?: boolean;
        bodyLimit?: number;
    };
};

// Unified HTTP server configuration
export type ServerProtocolConfig = {
    rest?: {
        enabled: boolean;
        prefix?: string;
        /** If false, the health endpoint will not be registered */
        health?: boolean;
        swagger?: {
            enabled: boolean;
            prefix?: string;
            title?: string;
            version?: string;
        };
    };
    trpc?: {
        enabled: boolean;
        prefix?: string;
        useWSS?: boolean;
        keepAlive?: {
            enabled: boolean;
            pingMs?: number;
            pongWaitMs?: number;
        };
        ui?: {
            enabled: boolean;
            prefix?: string;
            transformer?: 'superjson';
        };
    };
};

export type FastifyServerConfig = FastifyConfig & {
    protocols?: ServerProtocolConfig;
};

// Built-in adapter names and their configurations
export type BuiltInHTTPAdapters = {
    fastify: FastifyConfig;
};

// MCP Configuration
export type MCPConfig = {
    name?: string;
    version?: string;
    transportType?: 'stdio' | 'httpStream';
    httpStream?: {
        port?: number;
        endpoint?: string;
    };
    instructions?: string;
};

export type BuiltInMCPAdapters = {
    fastmcp: MCPConfig;
};

// Generic adapter configuration that supports both built-in and custom adapters
export type AdapterConfig<TAdapterName extends keyof BuiltInHTTPAdapters | string, TConfig = any> =
    TAdapterName extends keyof BuiltInHTTPAdapters
        ? {
            adapter: TAdapterName;
            config?: BuiltInHTTPAdapters[TAdapterName];
          }
        : {
            adapter: HTTPAdapter<TConfig>;
            config?: TConfig;
          };

// Type-safe CQRS run configuration
export type CQRSRunConfig = {
    // Unified HTTP server configuration
    http?: {
        adapter: 'fastify';
        config?: FastifyServerConfig;
    } | {
        adapter: HTTPAdapter<any>;
        config?: any;
    };

    // MCP (Model Context Protocol) configuration
    mcp?: {
        adapter: 'fastmcp';
        config?: MCPConfig;
    } | {
        adapter: MCPAdapter<any>;
        config?: any;
    };

    // Future adapter configurations can be added here with full type safety:
    // grpc?: { adapter: 'grpc-js' | 'grpc-node' | GrpcAdapter<any>; config?: any };
    // graphql?: { adapter: 'apollo' | 'yoga' | GraphQLAdapter<any>; config?: any };
    // pubsub?: { adapter: 'dapr' | 'redis' | 'kafka' | PubSubAdapter<any>; config?: any };
    // websocket?: { adapter: 'socket.io' | 'ws' | WebSocketAdapter<any>; config?: any };
};

export type AdapterRuntimes = Extract<keyof CQRSRunConfig, string>;

// Registry caller types - inspired by tRPC v11 createCaller pattern
export type RegistryCallerErrorHandler<TCustomContext> = (opts: {
    error: Error;
    input: unknown;
    name: string;
    type: ProcedureType;
    ctx?: TCustomContext;
}) => void;

export type RegistryCallerOptions<TCustomContext> = {
    onError?: RegistryCallerErrorHandler<TCustomContext>;
    signal?: AbortSignal;
    getContext?: CreateContext<TCustomContext>;
};

export type RegistryCaller<
    TRegistryDef extends ProcedureRecord<TCustomContext>,
    TCustomContext extends Record<string, any> = Record<string, any>
> = (
    options?: RegistryCallerOptions<TCustomContext>
) => DecorateRegistryRecord<TRegistryDef, TCustomContext>;

// Transform registry procedures into callable handlers
export type DecorateRegistryRecord<
    TRecord extends ProcedureRecord<TCustomContext>,
    TCustomContext extends Record<string, any> = Record<string, any>
> = {
    [K in keyof TRecord]: TRecord[K] extends Procedure<infer TType, any, infer TInput, infer TOutput, TCustomContext>
        ? TType extends 'command'
            ? CommandHandler<z.infer<TInput>, z.infer<TOutput>>
            : QueryHandler<z.infer<TInput>, z.infer<TOutput>>
        : never;
};

// Jest/Vitest-inspired mocking types for testing
export type MockedFunction<T extends (...args: any[]) => any> = T & {
    mockReturnValue(value: Awaited<ReturnType<T>>): MockedFunction<T>;
    mockResolvedValue(value: Awaited<ReturnType<T>>): MockedFunction<T>;
    mockRejectedValue(error: any): MockedFunction<T>;
    mockImplementation(fn: T): MockedFunction<T>;
    mockImplementationOnce(fn: T): MockedFunction<T>;
    mockClear(): void;
    mockReset(): void;
    mockRestore(): void;
    
    // Call tracking
    mock: {
        calls: Parameters<T>[];
        results: Array<{ type: 'return' | 'throw'; value: any }>;
        instances: any[];
        lastCall?: Parameters<T>;
    };
};

export type MockedQueryHandler<TInput, TOutput> = 
  (ShouldInputBeOptional<TInput> extends true
    ? {
        (input?: TInput): Promise<TOutput>;
        mockImplementation(fn: (args: { input?: TInput }) => Promise<TOutput>): MockedQueryHandler<TInput, TOutput>;
        mockImplementationOnce(fn: (args: { input?: TInput }) => Promise<TOutput>): MockedQueryHandler<TInput, TOutput>;
    } : {
        (input: TInput): Promise<TOutput>;
        mockImplementation(fn: (args: { input: TInput }) => Promise<TOutput>): MockedQueryHandler<TInput, TOutput>;
        mockImplementationOnce(fn: (args: { input: TInput }) => Promise<TOutput>): MockedQueryHandler<TInput, TOutput>;
    }) & {
    // Mock methods
    mockReturnValue(value: TOutput): MockedQueryHandler<TInput, TOutput>;
    mockResolvedValue(value: TOutput): MockedQueryHandler<TInput, TOutput>;
    mockRejectedValue(error: any): MockedQueryHandler<TInput, TOutput>;
    mockClear(): void;
    mockReset(): void;
    mockRestore(): void;
    
    // Call tracking
    mock: {
        calls: [TInput?][];
        results: Array<{ type: 'return' | 'throw'; value: any }>;
        instances: any[];
        lastCall?: [TInput?];
    };
};

// Transform registry procedures into mocked versions
export type MockedRegistryRecord<
    TRecord extends ProcedureRecord<TCustomContext>,
    TCustomContext extends Record<string, any> = Record<string, any>
> = {
    [K in keyof TRecord]: TRecord[K] extends Procedure<any, any, infer TInput, infer TOutput, TCustomContext>
        ? MockedQueryHandler<z.infer<TInput>, z.infer<TOutput>>
        : never;
};

// Mock registry type with additional methods
export type MockedRegistry<TRegistryDef extends ProcedureRecord<TCustomContext>, TCustomContext extends Record<string, any> = Record<string, any>> = 
    MockedRegistryRecord<TRegistryDef, TCustomContext> & 
    // Inherit all registry methods and properties
    Omit<Registry<TRegistryDef, TCustomContext>, 'enableMocking'> & {
        // Spy on specific procedures
        spyOn<K extends Extract<keyof TRegistryDef, string>>(procedureName: K): MockedRegistryRecord<TRegistryDef, TCustomContext>[K];
        
        // Get call information for assertions
        getCalls<K extends Extract<keyof TRegistryDef, string>>(procedureName: K): MockedRegistryRecord<TRegistryDef, TCustomContext>[K]['mock'];
        
        // Bulk operations
        clearAllMocks(): void;
        resetAllMocks(): void;
        restoreAllMocks(): void;
        
        // Get original registry for restoration
        getOriginalRegistry(): Registry<TRegistryDef, TCustomContext>;
        
        // Override enableMocking to return the same mocked registry (already mocked)
        enableMocking(ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)): MockedRegistry<TRegistryDef, TCustomContext>;
    };
