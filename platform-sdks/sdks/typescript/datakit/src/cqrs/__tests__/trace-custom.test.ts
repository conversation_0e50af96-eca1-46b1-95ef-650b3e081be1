import { CQRS } from '../cqrs';
import { getTrace, getTraceInfo, runWithTrace, runWithinExistingTrace, getCurrentTrace } from '../trace';
import { z } from 'zod';

const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;

describe('Custom Trace Context', () => {
    // Define custom trace context type
    type CustomTraceContext = ReturnType<typeof createContext>;

    // Application context type
    type Context = {
        userService: {
            create: (data: { name: string; email: string }) => Promise<{ id: string; name: string }>;
            findById: (id: string) => Promise<{ id: string; name: string; email: string }>;
        };
    };
    const { registry: createRegistry, define } = CQRS.create<CustomTraceContext>();

    const mockContext: Context = {
        userService: {
            create: jest.fn().mockImplementation(() => {
                return Promise.resolve({ id: 'user-123', name: '<PERSON>' });
            }),
            findById: jest.fn().mockImplementation(() => {
                return Promise.resolve({ id: 'user-123', name: 'John Doe', email: '<EMAIL>' })
            }),
        },
    };

    // Track log messages for verification
    const logMessages: Array<{ level: string; message: string; meta?: any }> = [];

    // Custom trace context creator
    const createContext = ({ traceId, procedureName, procedureType, input }: {
        traceId: string;
        parentId?: string;
        procedureName: string;
        procedureType: 'command' | 'query';
        input: unknown;
    }) => {
        return {
            ...mockContext,
            logger: {
                info: (message: string, meta?: any) => {
                    logMessages.push({
                        level: 'info',
                        message,
                        meta: {
                            ...meta,
                            traceId,
                            procedure: `${procedureType}:${procedureName}`
                        }
                    });
                },
                error: (message: string, meta?: any) => {
                    logMessages.push({
                        level: 'error',
                        message,
                        meta: {
                            ...meta,
                            traceId,
                            procedure: `${procedureType}:${procedureName}`
                        }
                    });
                },
            },
            serviceName: 'user-service',
            environment: 'test',
            userId: (input as any)?.userId,
            requestMetadata: {
                userAgent: 'test-agent',
                ip: '127.0.0.1',
                correlationId: `corr-${traceId}`,
            },
        };
    };

    const { command, query } = define;

    const createUserCommand = command({
        input: z.object({ name: z.string(), email: z.string(), userId: z.string().optional() }),
        output: z.object({ id: z.string(), name: z.string() }),
        handler: async ({ input, ctx }) => {
            // Access custom trace context through ctx.getTrace()
            const trace = ctx;

            if (trace) {
                trace.logger.info('Starting user creation', {
                    userName: input.name,
                    environment: trace.environment,
                    serviceName: trace.serviceName,
                    correlationId: trace.requestMetadata.correlationId,
                });
            }

            const result = await ctx.userService.create(input);

            if (trace) {
                trace.logger.info('User created successfully', {
                    userId: result.id,
                    correlationId: trace.requestMetadata.correlationId,
                });
            }

            return result;
        },
    });

    const getUserQuery = query({
        input: z.object({ id: z.string(), userId: z.string().optional() }),
        output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
        handler: async ({ input, ctx }) => {
            // Access custom trace context through ctx.getTrace()
            const trace = cqrs.getContext();
            
            if (trace) {
                trace.logger.info('Fetching user', {
                    targetUserId: input.id,
                    requestingUserId: trace.userId,
                    ip: trace.requestMetadata.ip,
                    correlationId: trace.requestMetadata.correlationId,
                });
            }

            return ctx.userService.findById(input.id);
        },
    });

    // Create registry and CQRS instance first
    const testRegistry = createRegistry({
        createUser: createUserCommand,
        getUser: getUserQuery,
    });

    const cqrs = new CQRS({
        registry: testRegistry,
        getContext: createContext,
    });

    const createUserWithDetailsCommand = command({
        input: z.object({ name: z.string(), email: z.string(), userId: z.string().optional() }),
        output: z.object({ user: z.object({ id: z.string(), name: z.string(), email: z.string() }) }),
        handler: async ({ input, ctx }) => {
            // Access custom trace context through ctx.getTrace()
            const trace = cqrs.getContext();
            
            if (trace) {
                trace.logger.info('Creating user with details', {
                    operation: 'complex-user-creation',
                    environment: trace.environment,
                    correlationId: trace.requestMetadata.correlationId,
                });
            }

            // Nested call - should inherit trace context
            const newUser = await cqrs.commands.createUser(input);

            // Another nested call
            const userDetails = await cqrs.queries.getUser({ 
                id: newUser.id, 
                userId: input.userId 
            });

            if (trace) {
                trace.logger.info('Complex user creation completed', {
                    userId: userDetails.id,
                    correlationId: trace.requestMetadata.correlationId,
                });
            }

            return { user: userDetails };
        },
    });

    // Final registry with all commands
    const finalRegistry = createRegistry({
        createUser: createUserCommand,
        getUser: getUserQuery,
        createUserWithDetails: createUserWithDetailsCommand,
    });

    const finalCqrs = new CQRS({
        registry: finalRegistry,
        getContext: createContext,
    });

    beforeEach(() => {
        logMessages.length = 0;
        jest.clearAllMocks();
    });

    it('should provide custom trace context in handlers via ctx.getTrace()', async () => {
        await finalCqrs.commands.createUser({
            name: 'John Doe',
            email: '<EMAIL>',
            userId: 'user-456',
        });

        // Should have logged messages with custom context
        expect(logMessages).toHaveLength(2);

        const startLog = logMessages[0];
        expect(startLog.level).toBe('info');
        expect(startLog.message).toBe('Starting user creation');
        expect(startLog.meta).toMatchObject({
            userName: 'John Doe',
            environment: 'test',
            serviceName: 'user-service',
            procedure: 'command:createUser',
        });
        expect(startLog.meta.traceId).toMatch(uuidPattern);
        expect(startLog.meta.correlationId).toMatch(/^corr-/);

        const endLog = logMessages[1];
        expect(endLog.level).toBe('info');
        expect(endLog.message).toBe('User created successfully');
        expect(endLog.meta.userId).toBe('user-123');
        expect(endLog.meta.traceId).toBe(startLog.meta.traceId); // Same trace ID
    });

    it('should propagate custom trace context through nested calls with ctx.getTrace()', async () => {
        await finalCqrs.commands.createUserWithDetails({
            name: 'Jane Doe',
            email: '<EMAIL>',
            userId: 'user-789',
        });

        // Should have logs from:
        // 1. createUserWithDetails start
        // 2. createUser start (nested)
        // 3. createUser end (nested)
        // 4. getUser (nested)
        // 5. createUserWithDetails end
        expect(logMessages).toHaveLength(5);

        // All logs should have the same trace ID
        const traceIds = logMessages.map(log => log.meta.traceId);
        const uniqueTraceIds = new Set(traceIds);
        expect(uniqueTraceIds.size).toBe(1);

        // Verify the sequence of operations
        expect(logMessages[0].message).toBe('Creating user with details');
        expect(logMessages[1].message).toBe('Starting user creation');
        expect(logMessages[2].message).toBe('User created successfully');
        expect(logMessages[3].message).toBe('Fetching user');
        expect(logMessages[4].message).toBe('Complex user creation completed');

        // All should have the same correlation ID
        const correlationIds = logMessages.map(log => log.meta.correlationId);
        const uniqueCorrelationIds = new Set(correlationIds);
        expect(uniqueCorrelationIds.size).toBe(1);
        expect(correlationIds[0]).toEqual(expect.stringMatching(/^corr-/));
    });

    it('should work with both ctx.getTrace() and backward compatibility', async () => {
        await finalCqrs.commands.createUser({
            name: 'Test User',
            email: '<EMAIL>',
        });

        // Test backward compatibility with global functions
        const customTrace = getTrace<CustomTraceContext>();
        expect(customTrace).toBeUndefined(); // Outside of execution context

        const simpleTrace = getTraceInfo();
        expect(simpleTrace).toBeUndefined(); // Outside of execution context

        // Verify logs were created with custom context
        expect(logMessages).toHaveLength(2);
        expect(logMessages[0].meta.serviceName).toBe('user-service');
        expect(logMessages[0].meta.environment).toBe('test');
    });

    it('should handle different procedures with appropriate context', async () => {
        // Test command
        await finalCqrs.commands.createUser({
            name: 'Command User',
            email: '<EMAIL>',
        });

        const commandLogs = [...logMessages];
        logMessages.length = 0; // Clear for next test

        // Test query
        await finalCqrs.queries.getUser({
            id: 'user-123',
            userId: 'requester-456',
        });

        const queryLogs = [...logMessages];

        // Command logs should indicate command procedure
        expect(commandLogs[0].meta.procedure).toBe('command:createUser');
        
        // Query logs should indicate query procedure
        expect(queryLogs[0].meta.procedure).toBe('query:getUser');

        // Different trace IDs for separate calls
        expect(commandLogs[0].meta.traceId).not.toBe(queryLogs[0].meta.traceId);
    });
});


describe('Trace Edge Cases', () => {
    describe('Existing Trace Context Reuse', () => {
        it('should reuse existing trace context when no traceId is provided', async () => {
            let capturedTraceId: string | undefined;
            let capturedParentId: string | undefined;

            await runWithTrace('parent-trace-123', undefined, 'test', 'command', {}, undefined, async (ctx) => {
                await runWithTrace(undefined, undefined, 'nested', 'command', {}, undefined, async (nestedCtx) => {
                    capturedTraceId = nestedCtx.traceId;
                    capturedParentId = nestedCtx.parentId;
                });
            });

            // Should reuse the existing trace context
            expect(capturedTraceId).toBe('parent-trace-123');
            expect(capturedParentId).toBeUndefined(); // Parent context doesn't have a parent
        });

        it('should create new trace when traceId is explicitly provided even in existing context', async () => {
            let capturedTraceId: string | undefined;
            let capturedParentId: string | undefined;

            await runWithTrace('parent-trace-123', undefined, 'test', 'command', {}, undefined, async (ctx) => {
                await runWithTrace('explicit-trace-456', undefined, 'nested', 'command', {}, undefined, async (nestedCtx) => {
                    capturedTraceId = nestedCtx.traceId;
                    capturedParentId = nestedCtx.parentId;
                });
            });

            // Should create new trace with explicit ID and parent should be the existing trace
            expect(capturedTraceId).toBe('explicit-trace-456');
            expect(capturedParentId).toBe('parent-trace-123');
        });
    });

    describe('runWithinExistingTrace Error Handling', () => {
        it('should throw an error when called outside of trace context', async () => {
            let error: Error | undefined;
            try {
                await runWithinExistingTrace(async () => {});
            } catch (e: any) {
                error = e;
            }
            expect(error).toBeInstanceOf(Error);
            expect(error?.message).toBe('No trace context found. This function should only be called within an existing trace context.');
        });

        it('should work correctly when called within trace context', async () => {
            let capturedTraceId: string | undefined;
            let innerResult: any;

            await runWithTrace('test-trace-789', undefined, 'test', 'command', {}, undefined, async (ctx) => {
                innerResult = await runWithinExistingTrace(async (nestedCtx) => {
                    capturedTraceId = nestedCtx.traceId;
                    return { inner: 'result' };
                });
            });

            expect(capturedTraceId).toBe('test-trace-789');
            expect(innerResult).toEqual({ inner: 'result' });
        });
    });

    describe('getCurrentTrace Edge Cases', () => {
        it('should return undefined when called outside of trace context', () => {
            expect(getCurrentTrace()).toBeUndefined();
        });

        it('should return trace context when called within trace', async () => {
            let capturedTrace: any;

            await runWithTrace('current-trace-test', 'parent-123', 'test', 'command', {}, undefined, async (ctx) => {
                capturedTrace = getCurrentTrace();
            });

            expect(capturedTrace).toMatchObject({
                traceId: 'current-trace-test',
                parentId: 'parent-123',
                startTime: expect.any(Number)
            });
        });
    });

    describe('Custom Context Integration', () => {
        it('should handle custom context creation with adapter context', async () => {
            type CustomContext = {
                userId: string;
                requestId: string;
                userAgent: string;
            };

            let capturedContext: any;
            
            const getContext = (params: any): CustomContext => {
                return {
                    userId: params.input.userId,
                    requestId: params.traceId, // For testing, align request ID with trace ID
                    userAgent: params.rest.headers['user-agent'],
                };
            };

            await runWithTrace(
                'custom-context-test', 
                undefined, 
                'test-proc', 
                'command', 
                { userId: 'user-123' },
                getContext,
                async (ctx) => {
                    capturedContext = ctx;
                },
                { rest: { headers: { 'user-agent': 'test-browser/1.0' } } }
            );

            expect(capturedContext).toMatchObject({
                traceId: 'custom-context-test',
                startTime: expect.any(Number),
                userId: 'user-123',
                requestId: 'custom-context-test',
                userAgent: 'test-browser/1.0'
            });
        });

        it('should handle empty custom context when getContext returns empty object', async () => {
            let capturedContext: any;
            
            const getContext = () => ({});

            await runWithTrace('empty-context-test', undefined, 'test', 'command', {}, getContext, async (ctx) => {
                capturedContext = ctx;
            });

            expect(capturedContext).toMatchObject({
                traceId: 'empty-context-test',
                startTime: expect.any(Number)
            });
        });
    });
});
