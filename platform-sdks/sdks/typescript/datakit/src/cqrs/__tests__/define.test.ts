import { z } from 'zod';
import { expectTypeOf } from 'expect-type';
import { createDefine } from '../define';
import type { Command, Query } from '../types';

type TestContext = {
    userId: string;
};

describe('define', () => {
    const { command, query } = createDefine<TestContext>();

    describe('command', () => {
        it('should create a command with proper types', () => {
            const createUser = command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                handler: async ({ input, ctx }) => {
                    expectTypeOf(input).toEqualTypeOf<{ name: string }>();

                    return { id: 'test' };
                },
            });

            // Runtime checks
            expect(createUser._type).toBe('command');
            expect(createUser._def.input).toBeDefined();
            expect(createUser._def.output).toBeDefined();
            expect(createUser._def.handler).toBeDefined();

            // Type checks
            expectTypeOf(createUser).toExtend<Command<any, any, TestContext>>();
            expectTypeOf(createUser._def.handler)
                .parameter(0)
                .toExtend<{ input: { name: string }; ctx: TestContext }>();
            expectTypeOf(createUser._def.handler)
                .returns
                .toExtend<Promise<{ id: string }>>();
        });
    });

    describe('query', () => {
        it('should create a query with proper types', () => {
            const getUser = query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string() }),
                handler: async ({ input, ctx }) => {
                    // Type checking
                    const id: string = input.id;
                    const userId: string = ctx.userId;

                    return { id: 'test', name: 'Test User' };
                },
            });

            // Runtime checks
            expect(getUser._type).toBe('query');
            expect(getUser._def.input).toBeDefined();
            expect(getUser._def.output).toBeDefined();
            expect(getUser._def.handler).toBeDefined();

            // Type checks
            expectTypeOf(getUser).toExtend<Query<any, any, TestContext>>();
            expectTypeOf(getUser._def.handler)
                .parameter(0)
                .toExtend<{ input: { id: string }; ctx: TestContext }>();
            expectTypeOf(getUser._def.handler)
                .returns
                .toExtend<Promise<{ id: string; name: string }>>();

            // Should not allow invalid output
            query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string() }), // required name
                // @ts-expect-error - output shape mismatch
                handler: async ({ input, ctx }) => {
                    return { id: 'test' }; // missing name
                },
            });
        });
    });

    describe('type inference', () => {
        it('should properly infer handler parameter types', () => {
            const testCommand = command({
                input: z.object({
                    name: z.string(),
                    age: z.number(),
                    tags: z.array(z.string())
                }),
                output: z.object({ success: z.boolean() }),
                handler: async ({ input, ctx }) => {
                    expectTypeOf(input).toEqualTypeOf<{
                        name: string;
                        age: number;
                        tags: string[];
                    }>();
                    expectTypeOf(ctx).toExtend<TestContext>();
                    return { success: true };
                },
            });

            const testQuery = query({
                input: z.object({ id: z.union([z.string(), z.number()]) }),
                output: z.object({ value: z.string() }),
                handler: async ({ input, ctx }) => {
                    expectTypeOf(input).toEqualTypeOf<{ id: string | number }>();
                    expectTypeOf(ctx).toExtend<TestContext>();
                    return { value: String(input.id) };
                },
            });
        });
    });

    describe('middleware', () => {
        it('should chain middleware correctly', async () => {
            const middleware1 = jest.fn((opts) => {
                (opts.ctx as any).mw1 = 'ran';
                return opts.next(opts);
            });
            const middleware2 = jest.fn((opts) => {
                (opts.ctx as any).mw2 = 'ran';
                return opts.next(opts);
            });

            const { command } = createDefine<TestContext & { mw1?: string; mw2?: string }>();

            const commandWithMiddleware = command
                .use(middleware1)
                .use(middleware2);

            let handlerCtx: any;
            const testCommand: Command<z.ZodVoid, z.ZodVoid, any> = commandWithMiddleware({
                input: z.void(),
                output: z.void(),
                handler: async ({ ctx }) => {
                    handlerCtx = ctx;
                },
            });

            const enhancedHandler = (testCommand._def as any)._createEnhancedHandler?.('test');
            await enhancedHandler?.({ input: undefined, ctx: { userId: 'test' } });

            expect(middleware1).toHaveBeenCalled();
            expect(middleware2).toHaveBeenCalled();
            expect(handlerCtx).toHaveProperty('mw1', 'ran');
            expect(handlerCtx).toHaveProperty('mw2', 'ran');
        });

        it('should chain middleware correctly on queries as well', async () => {
            const { query } = createDefine<TestContext & { flag?: boolean }>();
            const qmw1 = jest.fn(async (opts) => {
                (opts.ctx as any).flag = true;
                return opts.next(opts);
            });
            const qmw2 = jest.fn(async (opts) => opts.next(opts));

            const queryWithMiddleware = query
                .use(qmw1)
                .use(qmw2);

            const testQuery = queryWithMiddleware({
                input: z.object({ id: z.string() }),
                output: z.string(),
                handler: async ({ input }) => `hello ${input.id}`,
            });

            const enhanced = (testQuery._def as any)._createEnhancedHandler?.('greet');
            const result = await enhanced?.({ input: { id: 'world' }, ctx: {} });

            expect(result).toBe('hello world');
            expect(qmw1).toHaveBeenCalled();
            expect(qmw2).toHaveBeenCalled();
        });
    });
});
