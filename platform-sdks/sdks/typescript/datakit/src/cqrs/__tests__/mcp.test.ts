import { z } from 'zod';
import { CQRSInternal as CQRS } from '../cqrs';
import type { MCPContext } from '../types';

type TestContext = {
    userId: string;
};

describe('MCP Integration', () => {
    const { define, registry } = CQRS.create<TestContext>();
    const { command, query } = define;

    describe('MCP Metadata', () => {
        it('should create command with MCP metadata', () => {
            const createUser = command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    title: 'Create User',
                    description: 'Creates a new user account',
                    mcp: {
                        streamingHint: true,
                        openWorldHint: true,
                    },
                },
                handler: async ({ input, ctx }) => ({ id: 'test-' + input.name }),
            });

            expect(createUser._def.metadata?.mcp).toBeDefined();
            expect(createUser._def.metadata?.mcp?.streamingHint).toBe(true);
            expect(createUser._def.metadata?.mcp?.openWorldHint).toBe(true);
        });

        it('should create query with MCP metadata', () => {
            const getUser = query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string() }),
                metadata: {
                    mcp: {
                        readOnlyHint: true,
                        timeoutMs: 5000,
                    },
                },
                handler: async ({ input }) => ({ id: input.id, name: 'Test User' }),
            });

            expect(getUser._def.metadata?.mcp).toBeDefined();
            expect(getUser._def.metadata?.mcp?.readOnlyHint).toBe(true);
            expect(getUser._def.metadata?.mcp?.timeoutMs).toBe(5000);
        });

        it('should allow null MCP metadata to disable MCP', () => {
            const internalCommand = command({
                input: z.object({ data: z.string() }),
                output: z.object({ success: z.boolean() }),
                metadata: {
                    mcp: null,
                },
                handler: async ({ input }) => ({ success: true }),
            });

            expect(internalCommand._def.metadata?.mcp).toBeNull();
        });
    });

    describe('MCP Context', () => {
        it('should provide MCP context in handlers when available', async () => {
            let capturedContext: any = null;
            let mcpContext: MCPContext | undefined = undefined;

            const testCommand = command({
                input: z.object({ message: z.string() }),
                output: z.object({ success: z.boolean() }),
                metadata: {
                    mcp: {
                        streamingHint: true,
                    },
                },
                handler: async ({ input, ctx }) => {
                    capturedContext = ctx;
                    mcpContext = ctx.mcp;
                    
                    // Test MCP context usage
                    if (ctx.mcp) {
                        await ctx.mcp.reportProgress({ progress: 50, total: 100 });
                        await ctx.mcp.streamContent({ type: 'text', text: 'Processing...' });
                    }
                    
                    return { success: true };
                },
            });

            const testRegistry = registry({ testCommand });
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });

            // Mock MCP context
            const mockMcpContext: MCPContext = {
                streamContent: jest.fn(),
                reportProgress: jest.fn(),
                log: {
                    debug: jest.fn(),
                    info: jest.fn(),
                    warn: jest.fn(),
                    error: jest.fn(),
                }
            };

            await testCqrs.executeWithContext(
                'testCommand',
                { message: 'test' },
                {
                    traceId: 'test-trace',
                    procedureName: 'testCommand',
                    procedureType: 'command',
                    input: { message: 'test' },
                    mcp: mockMcpContext,
                }
            );

            expect(capturedContext).toBeDefined();
            expect(mcpContext).toBeDefined();
            expect(mockMcpContext.reportProgress).toHaveBeenCalledWith({ progress: 50, total: 100 });
            expect(mockMcpContext.streamContent).toHaveBeenCalledWith({ type: 'text', text: 'Processing...' });
        });

        it('should work without MCP context when not provided', async () => {
            let capturedContext: any = null;

            const testCommand = command({
                input: z.object({ message: z.string() }),
                output: z.object({ success: z.boolean() }),
                handler: async ({ input, ctx }) => {
                    capturedContext = ctx;
                    return { success: true };
                },
            });

            const testRegistry = registry({ testCommand });
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });

            await testCqrs.executeWithContext(
                'testCommand',
                { message: 'test' },
                {
                    traceId: 'test-trace',
                    procedureName: 'testCommand',
                    procedureType: 'command',
                    input: { message: 'test' },
                    // No MCP context provided
                }
            );

            expect(capturedContext).toBeDefined();
            expect(capturedContext.mcp).toBeUndefined();
        });
    });

    describe('Resource Management', () => {
        it('should add and retrieve resources', () => {
            const testCqrs = new CQRS({
                registry: registry({}),
                getContext: () => ({ userId: 'test-user' }),
            });

            const resource = {
                uri: "test://resource",
                name: "Test Resource",
                mimeType: "text/plain",
                load: async () => ({ text: "Test content" }),
            };

            // Should not throw
            testCqrs.addResource(resource);
        });

        it('should add and retrieve resource templates', () => {
            const testCqrs = new CQRS({
                registry: registry({}),
                getContext: () => ({ userId: 'test-user' }),
            });

            const template = {
                uriTemplate: "docs://project/{section}",
                name: "Project Documentation",
                mimeType: "text/markdown",
                arguments: [{ name: "section", required: true }],
                load: async (args: Record<string, string>) => ({
                    text: `Documentation for ${args.section}`
                }),
            };

            // Should not throw
            testCqrs.addResourceTemplate(template);
        });

        it('should embed resources correctly', async () => {
            const testCqrs = new CQRS({
                registry: registry({}),
                getContext: () => ({ userId: 'test-user' }),
            });

            const resource = {
                uri: "system://status",
                name: "System Status",
                mimeType: "text/plain",
                load: async () => ({ text: "System operational" }),
            };

            testCqrs.addResource(resource);

            const embedded = await testCqrs.embedded("system://status");

            expect(embedded).toEqual({
                type: 'resource',
                resource: {
                    uri: "system://status",
                    text: "System operational",
                    mimeType: "text/plain"
                }
            });
        });

        it('should throw error for non-existent resource', async () => {
            const testCqrs = new CQRS({
                registry: registry({}),
                getContext: () => ({ userId: 'test-user' }),
            });

            await expect(testCqrs.embedded("non-existent://resource"))
                .rejects.toThrow("Resource not found: non-existent://resource");
        });
    });

    describe('Route Definitions with MCP', () => {
        it('should include MCP metadata in route definitions', () => {
            const createUser = command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    title: 'Create User',
                    description: 'Creates a new user',
                    mcp: {
                        streamingHint: true,
                        destructiveHint: true,
                    },
                },
                handler: async ({ input }) => ({ id: 'user-123' }),
            });

            const getUser = query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string() }),
                metadata: {
                    mcp: {
                        readOnlyHint: true,
                    },
                },
                handler: async ({ input }) => ({ id: input.id, name: 'Test User' }),
            });

            const disabledCommand = command({
                input: z.object({ data: z.string() }),
                output: z.object({ success: z.boolean() }),
                metadata: {
                    mcp: null, // Explicitly disabled
                },
                handler: async () => ({ success: true }),
            });

            const testRegistry = registry({ createUser, getUser, disabledCommand });
            const routes = testRegistry.getRouteDefinitions();

            const createUserRoute = routes.find(r => r.procedureName === 'createUser');
            const getUserRoute = routes.find(r => r.procedureName === 'getUser');
            const disabledRoute = routes.find(r => r.procedureName === 'disabledCommand');

            expect(createUserRoute?.metadata.mcp).toBeDefined();
            expect(getUserRoute?.metadata.mcp).toBeDefined();
            expect(disabledRoute?.metadata.mcp).toBeNull();
        });
    });
}); 