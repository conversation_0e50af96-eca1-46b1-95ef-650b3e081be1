import { z } from 'zod';
import { CQRS } from '../index';
import { getTraceInfo } from '../trace';

const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;

describe('Trace Propagation', () => {
    type Context = {
        userService: {
            create: (data: { name: string; email: string }) => Promise<{ id: string; name: string }>;
            findById: (id: string) => Promise<{ id: string; name: string; email: string }>;
        };
        orgService: {
            create: (data: { name: string; ownerId: string }) => Promise<{ id: string; name: string }>;
        };
    };

    const mockContext: Context = {
        userService: {
            create: jest.fn().mockImplementation(async (data) => {
                // Add artificial delay to simulate real async work
                await new Promise(resolve => setTimeout(resolve, 10));
                return { id: 'user-123', name: data.name };
            }),
            findById: jest.fn().mockImplementation(async (id) => {
                await new Promise(resolve => setTimeout(resolve, 5));
                return { id, name: '<PERSON> Doe', email: '<EMAIL>' };
            }),
        },
        orgService: {
            create: jest.fn().mockImplementation(async (data) => {
                await new Promise(resolve => setTimeout(resolve, 15));
                return { id: 'org-456', name: data.name };
            }),
        },
    };

    const { registry, define } = CQRS.create<Context>();

    const { command, query } = define;

    // Track trace IDs captured in handlers with request context
    const capturedTraces: Array<{ traceId: string; operation: string; requestId: string }> = [];

    const createUserCommand = command({
        input: z.object({ name: z.string(), email: z.string(), requestId: z.string().optional() }),
        output: z.object({ id: z.string(), name: z.string() }),
        handler: async ({ input, ctx }) => {
            // Capture trace ID for verification
            const traceInfo = getTraceInfo();
            if (traceInfo) {
                capturedTraces.push({
                    traceId: traceInfo.traceId,
                    operation: 'createUser',
                    requestId: input.requestId || 'unknown'
                });
            }

            return ctx.userService.create(input);
        },
    });

    const getUserQuery = query({
        input: z.object({ id: z.string(), requestId: z.string().optional() }),
        output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
        handler: async ({ input, ctx }) => {
            // Capture trace ID for verification
            const traceInfo = getTraceInfo();
            if (traceInfo) {
                capturedTraces.push({
                    traceId: traceInfo.traceId,
                    operation: 'getUser',
                    requestId: input.requestId || 'unknown'
                });
            }

            return ctx.userService.findById(input.id);
        },
    });

    const createOrganizationCommand = command({
        input: z.object({ name: z.string(), adminEmail: z.string(), requestId: z.string().optional() }),
        output: z.object({ id: z.string(), name: z.string(), adminUserId: z.string() }),
        handler: async ({ input, ctx }) => {
            // Capture trace ID for verification
            const traceInfo = getTraceInfo();
            if (traceInfo) {
                capturedTraces.push({
                    traceId: traceInfo.traceId,
                    operation: 'createOrganization',
                    requestId: input.requestId || 'unknown'
                });
            }

            // This is a nested call - should inherit the same trace ID
            const adminUser = await cqrs.commands.createUser({
                name: 'Admin User',
                email: input.adminEmail,
                requestId: input.requestId,
            });

            // Another nested call
            const userDetails = await cqrs.queries.getUser({
                id: adminUser.id,
                requestId: input.requestId,
            });

            const org = await ctx.orgService.create({
                name: input.name,
                ownerId: adminUser.id,
            });

            return {
                id: org.id,
                name: org.name,
                adminUserId: userDetails.id,
            };
        },
    });

    const testRegistry = registry({
        createUser: createUserCommand,
        getUser: getUserQuery,
        createOrganization: createOrganizationCommand,
    });

    const cqrs = new CQRS({
        registry: testRegistry,
        getContext: () => mockContext,
    });

    beforeEach(() => {
        capturedTraces.length = 0;
        jest.clearAllMocks();
    });

    it('should generate trace ID for top-level calls', async () => {
        await cqrs.commands.createUser({
            name: 'John Doe',
            email: '<EMAIL>',
            requestId: 'req-1',
        });

        expect(capturedTraces).toHaveLength(1);
        expect(capturedTraces[0].traceId).toMatch(uuidPattern);
        expect(capturedTraces[0].operation).toBe('createUser');
    });

    it('should propagate the same trace ID through nested calls', async () => {
        await cqrs.commands.createOrganization({
            name: 'ACME Corp',
            adminEmail: '<EMAIL>',
            requestId: 'req-nested',
        });

        // Should have captured trace IDs from:
        // 1. createOrganization handler
        // 2. createUser handler (nested)
        // 3. getUser handler (nested)
        expect(capturedTraces).toHaveLength(3);

        // All trace IDs should be the same
        const traceIds = capturedTraces.map(t => t.traceId);
        const uniqueTraceIds = new Set(traceIds);
        expect(uniqueTraceIds.size).toBe(1);

        // All should belong to the same request
        const requestIds = capturedTraces.map(t => t.requestId);
        expect(requestIds).toEqual(['req-nested', 'req-nested', 'req-nested']);

        // Verify the trace ID format
        expect(capturedTraces[0].traceId).toMatch(uuidPattern);
    });

    it('should generate different trace IDs for separate top-level calls', async () => {
        // First call
        await cqrs.commands.createUser({
            name: 'John Doe',
            email: '<EMAIL>',
            requestId: 'req-1',
        });

        const firstTraceId = capturedTraces[0].traceId;

        // Second call
        await cqrs.commands.createUser({
            name: 'Jane Doe',
            email: '<EMAIL>',
            requestId: 'req-2',
        });

        const secondTraceId = capturedTraces[1].traceId;

        expect(firstTraceId).toBeDefined();
        expect(secondTraceId).toBeDefined();
        expect(firstTraceId).not.toBe(secondTraceId);
        expect(capturedTraces[0].requestId).toBe('req-1');
        expect(capturedTraces[1].requestId).toBe('req-2');
    });

    it('should maintain trace isolation during concurrent execution', async () => {
        // Simulate multiple concurrent requests
        const concurrentRequests = [
            cqrs.commands.createOrganization({
                name: 'Company A',
                adminEmail: '<EMAIL>',
                requestId: 'concurrent-req-1',
            }),
            cqrs.commands.createOrganization({
                name: 'Company B',
                adminEmail: '<EMAIL>',
                requestId: 'concurrent-req-2',
            }),
            cqrs.commands.createOrganization({
                name: 'Company C',
                adminEmail: '<EMAIL>',
                requestId: 'concurrent-req-3',
            }),
        ];

        // Execute all requests concurrently
        await Promise.all(concurrentRequests);

        // Should have 9 total traces (3 operations × 3 requests)
        expect(capturedTraces).toHaveLength(9);

        // Group traces by request ID
        const tracesByRequest = capturedTraces.reduce((acc, trace) => {
            if (!acc[trace.requestId]) acc[trace.requestId] = [];
            acc[trace.requestId].push(trace);
            return acc;
        }, {} as Record<string, typeof capturedTraces>);

        // Should have exactly 3 requests
        expect(Object.keys(tracesByRequest)).toHaveLength(3);

        // Each request should have exactly 3 operations
        Object.values(tracesByRequest).forEach(traces => {
            expect(traces).toHaveLength(3);
            
            // All traces within a request should have the same trace ID
            const traceIds = traces.map(t => t.traceId);
            const uniqueTraceIds = new Set(traceIds);
            expect(uniqueTraceIds.size).toBe(1);
            
            // Should have all three operations
            const operations = traces.map(t => t.operation).sort();
            expect(operations).toEqual(['createOrganization', 'createUser', 'getUser']);
        });

        // All requests should have different trace IDs
        const allTraceIds = Object.values(tracesByRequest).map(traces => traces[0].traceId);
        const uniqueAllTraceIds = new Set(allTraceIds);
        expect(uniqueAllTraceIds.size).toBe(3);
    });

    it('should handle mixed concurrent commands and queries', async () => {
        const mixedRequests = [
            // Simple commands
            cqrs.commands.createUser({
                name: 'User 1',
                email: '<EMAIL>',
                requestId: 'mixed-req-1',
            }),
            cqrs.commands.createUser({
                name: 'User 2',
                email: '<EMAIL>',
                requestId: 'mixed-req-2',
            }),
            // Simple queries
            cqrs.queries.getUser({
                id: 'existing-user-1',
                requestId: 'mixed-req-3',
            }),
            cqrs.queries.getUser({
                id: 'existing-user-2',
                requestId: 'mixed-req-4',
            }),
            // Complex command with nested calls
            cqrs.commands.createOrganization({
                name: 'Mixed Company',
                adminEmail: '<EMAIL>',
                requestId: 'mixed-req-5',
            }),
        ];

        await Promise.all(mixedRequests);

        // Should have 7 total traces:
        // - 2 createUser commands (2 traces)
        // - 2 getUser queries (2 traces)  
        // - 1 createOrganization with nested calls (3 traces)
        expect(capturedTraces).toHaveLength(7);

        // Group by request ID
        const tracesByRequest = capturedTraces.reduce((acc, trace) => {
            if (!acc[trace.requestId]) acc[trace.requestId] = [];
            acc[trace.requestId].push(trace);
            return acc;
        }, {} as Record<string, typeof capturedTraces>);

        // Should have 5 different requests
        expect(Object.keys(tracesByRequest)).toHaveLength(5);

        // Verify each request has correct number of operations
        expect(tracesByRequest['mixed-req-1']).toHaveLength(1); // createUser
        expect(tracesByRequest['mixed-req-2']).toHaveLength(1); // createUser
        expect(tracesByRequest['mixed-req-3']).toHaveLength(1); // getUser
        expect(tracesByRequest['mixed-req-4']).toHaveLength(1); // getUser
        expect(tracesByRequest['mixed-req-5']).toHaveLength(3); // createOrganization + nested

        // All requests should have different trace IDs
        const allTraceIds = Object.values(tracesByRequest).map(traces => traces[0].traceId);
        const uniqueAllTraceIds = new Set(allTraceIds);
        expect(uniqueAllTraceIds.size).toBe(5);
    });

    it('should handle queries with trace propagation', async () => {
        await cqrs.queries.getUser({
            id: 'user-123',
            requestId: 'query-req',
        });

        expect(capturedTraces).toHaveLength(1);
        expect(capturedTraces[0].traceId).toEqual(expect.any(String));
        expect(capturedTraces[0].operation).toBe('getUser');
        expect(capturedTraces[0].requestId).toBe('query-req');
    });

    it('should return undefined trace info when not in trace context', () => {
        // This should be called outside of any CQRS execution
        const traceInfo = getTraceInfo();
        expect(traceInfo).toBeUndefined();
    });

    it('should handle rapid sequential calls correctly', async () => {
        // Execute many calls in rapid succession (not concurrent)
        for (let i = 0; i < 10; i++) {
            await cqrs.commands.createUser({
                name: `User ${i}`,
                email: `user${i}@example.com`,
                requestId: `rapid-req-${i}`,
            });
        }

        expect(capturedTraces).toHaveLength(10);

        // Each call should have a unique trace ID
        const traceIds = capturedTraces.map(t => t.traceId);
        const uniqueTraceIds = new Set(traceIds);
        expect(uniqueTraceIds.size).toBe(10);

        // Each call should have the correct request ID
        capturedTraces.forEach((trace, index) => {
            expect(trace.requestId).toBe(`rapid-req-${index}`);
        });
    });
});
