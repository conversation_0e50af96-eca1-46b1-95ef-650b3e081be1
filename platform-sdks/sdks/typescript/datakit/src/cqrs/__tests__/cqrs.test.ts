import { z } from 'zod';
import { CQRSInternal as CQRS } from '../cqrs';
import type { AdapterStatus } from '../types';

type TestContext = {
    userId: string;
};

// Mock server adapter for testing
class MockServerAdapter {
    public name = 'mock-server';
    public status: AdapterStatus = 'stopped';
    public configureCallCount = 0;
    public registerRoutesCallCount = 0;
    public registerTRPCRouterCallCount = 0;
    public startCallCount = 0;
    public stopCallCount = 0;
    public lastConfig: any = null;
    public lastRoutes: any[] = [];
    public lastTRPCRouter: any = null;

    async configure(config: any): Promise<void> {
        this.configureCallCount++;
        this.lastConfig = config;
        this.status = 'configured';
    }

    async registerRoutes(routes: any[]): Promise<void> {
        this.registerRoutesCallCount++;
        this.lastRoutes = routes;
    }

    async registerTRPCRouter(router: any): Promise<void> {
        this.registerTRPCRouterCallCount++;
        this.lastTRPCRouter = router;
    }

    supportsProtocol(protocol: string): boolean {
        return protocol === 'rest' || protocol === 'trpc';
    }

    async start(): Promise<void> {
        this.startCallCount++;
        this.status = 'running';
    }

    async stop(): Promise<void> {
        this.stopCallCount++;
        this.status = 'stopped';
    }

    async getHealth(): Promise<any> {
        return {
            status: this.status === 'running' ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
        };
    }
}

// Rate limiting middleware (simplified)
const rateLimitMiddleware = async (opts: any) => {
    const { ctx } = opts;

    // In a real app, you'd use Redis or similar
    const rateLimitKey = `rate_limit_${ctx.userId || 'anonymous'}`;

    // Simulate rate limiting (always pass in this example)
    return opts.next();
};

describe('CQRS Extended Functionality', () => {
    const { define, registry } = CQRS.create<TestContext>();

    const createUser = define.command.use(rateLimitMiddleware)({
        input: z.object({ name: z.string() }),
        output: z.object({ id: z.string() }),
        handler: async ({ input, ctx }) => ({ id: 'test-' + input.name }),
    });

    const getUser = define.query({
        input: z.object({ id: z.string() }),
        output: z.object({ id: z.string(), name: z.string() }),
        handler: async ({ input, ctx }) => ({ id: input.id, name: 'Test User' }),
    });

    const testRegistry = registry({
        createUser,
        getUser,
    });

    describe('executeWithContext', () => {
        it('should execute procedures with enhanced context', async () => {
            const { define, registry } = CQRS.create<{
                userId: string;
                requestId: string;
            }>();

            let capturedContext: any = null;

            const testCommand = define.command.use(rateLimitMiddleware)({
                input: z.object({ message: z.string() }),
                output: z.object({ success: z.boolean(), context: z.any() }),
                handler: async ({ input, ctx }) => {
                    capturedContext = ctx;
                    return { success: true, context: ctx };
                }
            });

            const testRegistry = registry({ testCommand });

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: (params) => ({
                    userId: params.rest?.userId || 'default-user',
                    requestId: params.traceId || 'default-request'
                })
            });

            const result = await testCqrs.executeWithContext(
                'testCommand',
                { message: 'test' },
                {
                    traceId: 'custom-trace-123',
                    parentId: 'parent-456',
                    procedureName: 'testCommand',
                    procedureType: 'command',
                    input: { message: 'test' },
                    rest: {
                        userId: 'enhanced-user'
                    }
                }
            );

            expect(result.success).toBe(true);
            expect(capturedContext).toMatchObject({
                userId: 'enhanced-user',
                requestId: 'custom-trace-123',
                traceId: 'custom-trace-123',
                parentId: 'parent-456'
            });
        });

        it('should throw error for non-existent procedure', async () => {
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });

            await expect(
                cqrs.executeWithContext(
                    // @ts-expect-error - non-existent procedure
                    'nonExistentProcedure',
                    { test: 'input' },
                    {
                        traceId: 'test-trace',
                        procedureName: 'nonExistentProcedure',
                        procedureType: 'command',
                        input: { test: 'input' }
                    }
                )
            ).rejects.toThrow("Procedure 'nonExistentProcedure' not found");
        });

        it('should handle procedures with enhanced handlers', async () => {
            const { define, registry } = CQRS.create<TestContext>();

            const enhancedCommand = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                handler: async ({ input, ctx }) => ({ id: `original-${input.name}` }),
            });

            // Simulate enhanced handler
            (enhancedCommand._def as any)._createEnhancedHandler = (procedureName: string) => {
                return async ({ input, ctx }: any) => {
                    return { id: `enhanced-${procedureName}-${input.name}` };
                };
            };

            const testRegistry = registry({ enhancedCommand });

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            const result = await testCqrs.executeWithContext(
                'enhancedCommand',
                { name: 'test' },
                {
                    traceId: 'test-trace',
                    procedureName: 'enhancedCommand',
                    procedureType: 'command',
                    input: { name: 'test' }
                }
            ) as { id: string };

            expect(result).toEqual({ id: 'enhanced-enhancedCommand-test' });
        });

        it('should handle procedures without input/output validation', async () => {
            const { define, registry } = CQRS.create<TestContext>();

            const flexibleCommand = define.command({
                input: z.any(),
                output: z.any(),
                handler: async ({ input, ctx }) => ({ result: `processed-${input}` }),
            });

            // Remove schemas to test fallback
            (flexibleCommand._def as any).input = null;
            (flexibleCommand._def as any).output = null;

            const testRegistry = registry({ flexibleCommand });

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            const result = await testCqrs.executeWithContext(
                'flexibleCommand',
                'raw-input',
                {
                    traceId: 'test-trace',
                    procedureName: 'flexibleCommand',
                    procedureType: 'command',
                    input: 'raw-input'
                }
            );

            expect(result).toEqual({ result: 'processed-raw-input' });
        });

        it('should handle procedures without getContext', async () => {
            const testCqrs = new CQRS({
                registry: testRegistry,
                // No getContext provided
            });

            const result = await testCqrs.executeWithContext(
                'createUser',
                { name: 'test' },
                {
                    traceId: 'test-trace',
                    procedureName: 'createUser',
                    procedureType: 'command',
                    input: { name: 'test' }
                }
            );

            expect(result).toEqual({ id: 'test-test' });
        });
    });

    describe('Server Lifecycle and Adapters', () => {
        it('should start and stop HTTP adapter', async () => {
            const mockAdapter = new MockServerAdapter();

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            await testCqrs.run({
                http: {
                    adapter: mockAdapter,
                    config: {
                        port: 3000,
                        protocols: {
                            rest: { enabled: true },
                            trpc: { enabled: true }
                        }
                    }
                }
            });

            // Verify adapter was configured and started
            expect(mockAdapter.configureCallCount).toBe(1);
            expect(mockAdapter.registerRoutesCallCount).toBe(1);
            expect(mockAdapter.registerTRPCRouterCallCount).toBe(1);
            expect(mockAdapter.startCallCount).toBe(1);
            expect(mockAdapter.status).toBe('running');

            // Verify configuration was passed
            expect(mockAdapter.lastConfig).toMatchObject({
                port: 3000,
                protocols: {
                    rest: { enabled: true },
                    trpc: { enabled: true }
                }
            });

            // Stop the server
            await testCqrs.stop();
            expect(mockAdapter.stopCallCount).toBe(1);
            expect(mockAdapter.status).toBe('stopped');
        });

        it('should handle built-in fastify adapter', async () => {
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            // Mock the fastify adapter import
            const mockFastifyAdapter = new MockServerAdapter();
            jest.doMock('../../adapters/fastify/fastify', () => ({
                FastifyServerAdapter: jest.fn(() => mockFastifyAdapter)
            }));

            await testCqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port: 3001,
                        protocols: {
                            rest: { enabled: true },
                            trpc: {
                                enabled: true,
                                prefix: '/trpc',
                                ui: { enabled: true }
                            }
                        }
                    }
                }
            });

            expect(mockFastifyAdapter.configureCallCount).toBe(1);
            expect(mockFastifyAdapter.startCallCount).toBe(1);

            await testCqrs.stop();
            expect(mockFastifyAdapter.stopCallCount).toBe(1);

            jest.dontMock('../../adapters/fastify/fastify');
        });

        it('should throw error for unknown built-in adapter', async () => {
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            await expect(testCqrs.run({
                http: {
                    adapter: 'unknown-adapter' as any,
                    config: {}
                }
            })).rejects.toThrow('Unknown server adapter: unknown-adapter');
        });

        it('should throw error for invalid adapter instance', async () => {
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            const invalidAdapter = {};

            await expect(testCqrs.run({
                http: {
                    adapter: invalidAdapter as any,
                    config: {}
                }
            })).rejects.toThrow('Invalid server adapter: must implement ServerAdapter interface');
        });

        it('should handle adapter without REST support', async () => {
            const mockAdapter = new MockServerAdapter();
            mockAdapter.supportsProtocol = (protocol: string) => protocol === 'trpc';

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            await testCqrs.run({
                http: {
                    adapter: mockAdapter,
                    config: {
                        protocols: {
                            rest: { enabled: true },
                            trpc: { enabled: true }
                        }
                    }
                }
            });

            // Should not register routes for REST
            expect(mockAdapter.registerRoutesCallCount).toBe(0);
            // Should register tRPC router
            expect(mockAdapter.registerTRPCRouterCallCount).toBe(1);

            await testCqrs.stop();
        });

        it('should handle adapter without tRPC support', async () => {
            const mockAdapter = new MockServerAdapter();
            mockAdapter.supportsProtocol = (protocol: string) => protocol === 'rest';

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            await testCqrs.run({
                http: {
                    adapter: mockAdapter,
                    config: {
                        protocols: {
                            rest: { enabled: true },
                            trpc: { enabled: true }
                        }
                    }
                }
            });

            // Should register routes for REST
            expect(mockAdapter.registerRoutesCallCount).toBe(1);
            // Should not register tRPC router
            expect(mockAdapter.registerTRPCRouterCallCount).toBe(0);

            await testCqrs.stop();
        });

        it('should get adapter status', async () => {
            const mockAdapter = new MockServerAdapter();
            mockAdapter.status = 'running';

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            // Set adapter manually for testing
            (testCqrs as any)._adapters.set('http', mockAdapter);

            const server = await testCqrs.run();

            const status = await server.getStatus();
            expect(status).toEqual({
                http: {
                    name: 'mock-server',
                    status: 'running'
                }
            });
        });

        it('should get route definitions', () => {
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            const routes = cqrs.getRouteDefinitions();
            expect(Array.isArray(routes)).toBe(true);
            expect(routes.length).toBeGreaterThan(0);

            const createUserRoute = routes.find(r => r.procedureName === 'createUser');
            expect(createUserRoute).toBeDefined();
            expect(createUserRoute?.procedureType).toBe('command');
        });

        it('should get health status from adapters', async () => {
            const mockAdapter = new MockServerAdapter();
            mockAdapter.status = 'running';

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            // Set adapter manually for testing
            (testCqrs as any)._adapters.set('http', mockAdapter);

            const server = await testCqrs.run();

            const health = await server.getHealth();
            expect(health).toEqual({
                http: {
                    status: 'healthy',
                    timestamp: expect.any(String)
                }
            });
        });

        it('should get tRPC router', () => {
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            const router = cqrs.getTRPCRouter();
            expect(router).toBeDefined();
            expect(typeof router).toBe('object');
        });

        it('should run with runtime config from constructor', async () => {
            const mockAdapter = new MockServerAdapter();

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
                runtimes: {
                    http: {
                        adapter: mockAdapter,
                        config: {
                            port: 4000
                        }
                    }
                }
            });

            await testCqrs.run();

            expect(mockAdapter.configureCallCount).toBe(1);
            expect(mockAdapter.startCallCount).toBe(1);
            expect(mockAdapter.lastConfig).toMatchObject({
                port: 4000
            });

            await testCqrs.stop();
        });

        it('should override runtime config when passed to run', async () => {
            const mockAdapter1 = new MockServerAdapter();
            const mockAdapter2 = new MockServerAdapter();

            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
                runtimes: {
                    http: {
                        adapter: mockAdapter1,
                        config: { port: 4000 }
                    }
                }
            });

            // Override config in run()
            await testCqrs.run({
                http: {
                    adapter: mockAdapter2,
                    config: { port: 5000 }
                }
            });

            // Should use the overridden config
            expect(mockAdapter1.configureCallCount).toBe(0);
            expect(mockAdapter2.configureCallCount).toBe(1);
            expect(mockAdapter2.lastConfig).toMatchObject({
                port: 5000
            });

            await testCqrs.stop();
        });
    });

    describe('Context Management', () => {
        it('should provide getContext method', () => {
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' })
            });

            const context = cqrs.getContext();
            // Context should be undefined when not in trace context
            expect(context).toBeUndefined();
        });

        it('should provide getContextFunction method', () => {
            const contextFn = () => ({ userId: 'test-user' });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: contextFn
            });

            const retrievedContextFn = cqrs.getContextFunction();
            expect(retrievedContextFn).toBe(contextFn);
        });

        it('should handle missing getContext in constructor', () => {
            const testCqrs = new CQRS({
                registry: testRegistry,
                // No getContext provided
            });

            const contextFn = testCqrs.getContextFunction();
            expect(contextFn).toBeUndefined();
        });
    });

    describe('Static Methods', () => {
        it('should create define and registry through static method', () => {
            const { define, registry } = CQRS.create<TestContext>();

            expect(define).toBeDefined();
            expect(define.command).toBeDefined();
            expect(define.query).toBeDefined();
            expect(registry).toBeDefined();
            expect(typeof registry).toBe('function');
        });

        it('should create without generic type parameter', () => {
            const { define, registry } = CQRS.create();

            expect(define).toBeDefined();
            expect(registry).toBeDefined();
        });
    });

    describe('Mocking Integration', () => {
        it('should enable mocking with custom context', () => {
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });

            const customContext = { userId: 'mock-user' };
            testCqrs.enableMocking(null as any);
            const mockRegistry = testCqrs.enableMocking(customContext);

            expect(testCqrs.isMocked()).toBe(true);
            expect(mockRegistry).toBeDefined();
        });

        it('should enable mocking with context function', () => {
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });

            const contextFn = () => ({ userId: 'mock-user-fn' });
            const mockRegistry = testCqrs.enableMocking(contextFn);

            expect(testCqrs.isMocked()).toBe(true);
            expect(mockRegistry).toBeDefined();
        });
    });

    describe('CQRS.run() and adapters', () => {
        let cqrs: CQRS<any, TestContext>;

        beforeEach(() => {
            const { define, registry } = CQRS.create<TestContext>();

            const createUser = define.command({
                input: z.object({ name: z.string(), email: z.string().email() }),
                output: z.object({ id: z.string(), name: z.string() }),
                handler: async ({ input }) => ({ id: 'user-123', name: input.name }),
            });

            const getUser = define.query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
                handler: async () => ({ id: 'user-123', name: 'John Doe', email: '<EMAIL>' }),
            });

            const testRegistry = registry({ createUser, getUser });
            cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });
        });

        afterEach(async () => {
            if (cqrs) {
                await cqrs.stop();
            }
        });

        it('should run with HTTP adapter', async () => {
            const serverAdapter = new MockServerAdapter();

            await cqrs.run({
                http: {
                    adapter: serverAdapter,
                    config: { port: 3000 }
                }
            });

            expect(serverAdapter.configureCallCount).toBe(1);
            expect(serverAdapter.startCallCount).toBe(1);
            expect(serverAdapter.lastConfig).toEqual({ port: 3000 });
        });

        it('should run with built-in fastify adapter', async () => {
            // Test the built-in string adapter
            const server = await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: { port: 12345, fastifyOptions: { logger: false } }
                }
            });

            expect(server).toBeDefined();
            expect(typeof server.stop).toBe('function');

            await server.stop();
        });

        it('should run with MCP adapter', async () => {
            const mcpAdapter = {
                name: 'test-mcp',
                status: 'stopped' as const,
                configure: jest.fn(),
                registerRoutes: jest.fn(),
                registerResources: jest.fn(),
                registerResourceTemplates: jest.fn(),
                supportsProtocol: jest.fn().mockReturnValue(true),
                start: jest.fn(),
                stop: jest.fn(),
                getHealth: jest.fn().mockResolvedValue({ status: 'healthy', timestamp: Date.now() })
            };

            await cqrs.run({
                mcp: {
                    adapter: mcpAdapter,
                    config: { name: 'test-server' }
                }
            });

            expect(mcpAdapter.configure).toHaveBeenCalledWith({ name: 'test-server' });
            expect(mcpAdapter.start).toHaveBeenCalled();
        });

        it('should run with built-in fastmcp adapter', async () => {
            jest.doMock('fastmcp', () => ({
                FastMCP: jest.fn().mockImplementation(() => ({
                    start: jest.fn(),
                    stop: jest.fn(),
                    getHealth: jest.fn().mockResolvedValue({ status: 'healthy', timestamp: Date.now() })
                }))
            }));

            const server = await cqrs.run({
                mcp: {
                    adapter: 'fastmcp',
                    config: { name: 'test-mcp-server' }
                }
            });

            expect(server).toBeDefined();
            await server.stop();
        });

        it('should handle unknown adapter errors', async () => {
            await expect(cqrs.run({
                http: {
                    adapter: 'unknown-adapter' as any,
                    config: {}
                }
            })).rejects.toThrow('Unknown server adapter: unknown-adapter');
        });

        it('should validate adapter interfaces for HTTP', async () => {
            const invalidAdapter = { name: 'invalid' }; // Missing required methods

            await expect(cqrs.run({
                http: {
                    adapter: invalidAdapter as any,
                    config: {}
                }
            })).rejects.toThrow('Invalid server adapter: must implement ServerAdapter interface');
        });

        it('should validate adapter interfaces for MCP', async () => {
            const invalidMCPAdapter = { name: 'invalid-mcp' }; // Missing required methods

            await expect(cqrs.run({
                mcp: {
                    adapter: invalidMCPAdapter as any,
                    config: {}
                }
            })).rejects.toThrow('Invalid MCP adapter: must implement MCPAdapter interface');
        });

        it('should get health status from all adapters', async () => {
            const httpAdapter = new MockServerAdapter();
            const mcpAdapter = {
                name: 'test-mcp',
                status: 'running' as const,
                configure: jest.fn(),
                registerRoutes: jest.fn(),
                registerResources: jest.fn(),
                registerResourceTemplates: jest.fn(),
                supportsProtocol: jest.fn().mockReturnValue(true),
                start: jest.fn(),
                stop: jest.fn(),
                getHealth: jest.fn().mockResolvedValue({
                    status: 'healthy',
                    timestamp: Date.now(),
                    details: { transport: 'stdio' }
                })
            };

            const server = await cqrs.run({
                http: { adapter: httpAdapter, config: {} },
                mcp: { adapter: mcpAdapter, config: {} }
            });

            const health = await server.getHealth();

            expect(health).toHaveProperty('http');
            expect(health).toHaveProperty('mcp');
            expect(health.http.status).toBe('healthy');
            expect(health.mcp.status).toBe('healthy');

            await server.stop();
        });

        it('should get status from all adapters', async () => {
            const httpAdapter = new MockServerAdapter();
            httpAdapter.status = 'running';

            const server = await cqrs.run({
                http: { adapter: httpAdapter, config: {} }
            });

            const status = await server.getStatus();

            expect(status).toHaveProperty('http');
            expect(status.http.name).toBe('mock-server');
            expect(status.http.status).toBe('running');

            await server.stop();
        });

        it('should handle adapter health check errors', async () => {
            const faultyAdapter = new MockServerAdapter();
            faultyAdapter.getHealth = jest.fn().mockRejectedValue(new Error('Health check failed'));

            const server = await cqrs.run({
                http: { adapter: faultyAdapter, config: {} }
            });

            const health = await server.getHealth();
            expect(health.http.status).toBe('error');
            expect(health.http).toHaveProperty('message', 'Health check failed');

            await server.stop();
        });
    });

    describe('MCP Resource Management', () => {
        let cqrs: CQRS<any, TestContext>;

        beforeEach(() => {
            const { define, registry } = CQRS.create<TestContext>();
            const testRegistry = registry({});
            cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });
        });

        it('should add and retrieve MCP resources', () => {
            const resource = {
                uri: 'test://resource1',
                name: 'Test Resource',
                description: 'A test resource',
                mimeType: 'text/plain',
                load: () => ({ text: 'Hello World' })
            };

            // Should not throw when adding resources
            expect(() => cqrs.addResource(resource)).not.toThrow();
        });

        it('should add and retrieve MCP resource templates', () => {
            const template = {
                uriTemplate: 'test://users/{id}',
                name: 'User Template',
                description: 'Get user by ID',
                arguments: [{ name: 'id', description: 'User ID', required: true }],
                load: (args: any) => ({ text: `User ${args.id}` })
            };

            // Should not throw when adding resource templates
            expect(() => cqrs.addResourceTemplate(template)).not.toThrow();
        });

        it('should embed resources for tool responses', async () => {
            const resource = {
                uri: 'test://data',
                name: 'Test Data',
                mimeType: 'application/json',
                load: async () => ({
                    text: '{"key": "value"}',
                    mimeType: 'application/json'
                })
            };

            cqrs.addResource(resource);

            const embedded = await cqrs.embedded('test://data');

            expect(embedded).toEqual({
                type: 'resource',
                resource: {
                    uri: 'test://data',
                    text: '{"key": "value"}',
                    mimeType: 'application/json'
                }
            });
        });

        it('should throw error for unknown resource', async () => {
            await expect(cqrs.embedded('test://unknown')).rejects.toThrow('Resource not found: test://unknown');
        });

        it('should handle resource loading with fallback mimeType', async () => {
            const resource = {
                uri: 'test://fallback',
                name: 'Fallback Resource',
                mimeType: 'text/plain',
                load: () => ({ text: 'content' }) // No mimeType in result
            };

            cqrs.addResource(resource);

            const embedded = await cqrs.embedded('test://fallback');

            expect(embedded.resource.mimeType).toBe('text/plain'); // Should use resource's mimeType
        });
    });

    describe('CQRS instance methods', () => {
        let cqrs: CQRS<any, TestContext>;

        beforeEach(() => {
            const { define, registry } = CQRS.create<TestContext>();

            const createUser = define.command({
                input: z.object({ name: z.string(), email: z.string().email() }),
                output: z.object({ id: z.string(), name: z.string() }),
                handler: async ({ input }) => ({ id: 'user-123', name: input.name }),
            });

            const getUser = define.query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
                handler: async () => ({ id: 'user-123', name: 'John Doe', email: '<EMAIL>' }),
            });

            const testRegistry = registry({ createUser, getUser });
            cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });
        });

        it('should have CQRS instance methods', () => {
            expect(cqrs).toBeDefined();
            expect(typeof cqrs.addResource).toBe('function');
            expect(typeof cqrs.addResourceTemplate).toBe('function');
            expect(typeof cqrs.embedded).toBe('function');
        });
    });

    describe('getMetadata method', () => {
        let cqrs: CQRS<any, TestContext>;

        beforeEach(() => {
            const { define, registry } = CQRS.create<TestContext>();
            const testRegistry = registry({});
            cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test-user' }),
            });
        });

        it('should return CQRS metadata when provided', () => {
            const metadata = {
                serviceName: 'test-service',
                serviceVersion: '1.0.0',
                serviceType: 'api' as const
            };

            const { define, registry } = CQRS.create<TestContext>();
            const testRegistry = registry({});
            const cqrsWithMetadata = new CQRS({
                registry: testRegistry,
                metadata,
                getContext: () => ({ userId: 'test' }),
            });

            expect(cqrsWithMetadata.getMetadata()).toEqual(metadata);
        });

        it('should return undefined when no metadata provided', () => {
            expect(cqrs.getMetadata()).toBeUndefined();
        });
    });

    describe('CQRS Service Metadata', () => {
        it('should handle different serviceType enum values', () => {
            const testCases = [
                { serviceType: 'api' as const, expected: 'api' },
                { serviceType: 'etl' as const, expected: 'etl' },
                { serviceType: 'transform' as const, expected: 'transform' },
                { serviceType: 'analytics' as const, expected: 'analytics' },
                { serviceType: 'web' as const, expected: 'web' },
            ];

            testCases.forEach(({ serviceType, expected }) => {
                const cqrs = new CQRS({
                    registry: testRegistry,
                    metadata: { serviceType },
                    getContext: () => ({ userId: 'test' }),
                });

                const metadata = cqrs.getMetadata();
                expect(metadata?.serviceType).toBe(expected);
            });
        });

        it('should handle custom string values for serviceType', () => {
            const customTypes = ['custom-worker', 'batch-processor', 'data-ingestion'];

            customTypes.forEach(serviceType => {
                const cqrs = new CQRS({
                    registry: testRegistry,
                    metadata: { serviceType },
                    getContext: () => ({ userId: 'test' }),
                });

                const metadata = cqrs.getMetadata();
                expect(metadata?.serviceType).toBe(serviceType);
            });
        });

        it('should handle complete service metadata', () => {
            const metadata = {
                serviceName: 'test-service',
                serviceVersion: '1.2.3',
                serviceType: 'analytics' as const,
            };

            const cqrs = new CQRS({
                registry: testRegistry,
                metadata,
                getContext: () => ({ userId: 'test' }),
            });

            const result = cqrs.getMetadata();
            expect(result).toEqual(metadata);
        });

        it('should handle missing metadata gracefully', () => {
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userId: 'test' }),
            });

            const metadata = cqrs.getMetadata();
            expect(metadata).toBeUndefined();
        });
    });
});
