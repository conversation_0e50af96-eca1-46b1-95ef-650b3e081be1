import { z } from 'zod';
import { expectTypeOf } from 'expect-type';
import { CQRS } from '../cqrs';

describe('Middleware', () => {
    type BaseContext = {
        userId?: string;
        requestId: string;
    };

    type AuthenticatedContext = BaseContext & {
        userId: string;
        user: {
            id: string;
            role: 'admin' | 'user';
        };
    };

    const { define, registry } = CQRS.create<BaseContext>();
    const { command, query } = define;

    describe('Basic Middleware Usage', () => {
        it('should allow adding middleware with .use()', async () => {
            const loggingMiddleware = jest.fn().mockImplementation(async (opts) => {
                console.log(`Executing ${opts.path} with input:`, opts.input);
                return opts.next();
            });

            const testCommand = command.use(loggingMiddleware)({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                handler: async ({ input, ctx }) => {
                    return { id: `user-${input.name}` };
                },
            });

            const testRegistry = registry({ testCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-123' }),
            });

            const result = await cqrs.commands.testCommand({ name: 'John' });

            expect(result).toEqual({ id: 'user-John' });
            expect(loggingMiddleware).toHaveBeenCalledWith(
                expect.objectContaining({
                    path: 'testCommand',
                    type: 'command',
                    input: { name: 'John' },
                    ctx: expect.objectContaining({ requestId: 'test-123' }),
                    next: expect.any(Function),
                })
            );
        });

        it('should allow chaining multiple middlewares', async () => {
            const middleware1 = jest.fn().mockImplementation(async (opts) => {
                return opts.next({ ctx: { ...opts.ctx, step1: true } });
            });

            const middleware2 = jest.fn().mockImplementation(async (opts) => {
                return opts.next({ ctx: { ...opts.ctx, step2: true } });
            });

            const testQuery = query.use(middleware1).use(middleware2)({
                input: z.object({ id: z.string() }),
                output: z.object({ result: z.string() }),
                handler: async ({ input, ctx }) => {
                    return {
                        result: `${input.id}-${(ctx as any).step1}-${(ctx as any).step2}`
                    };
                },
            });

            const testRegistry = registry({ testQuery });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-456' }),
            });

            const result = await cqrs.queries.testQuery({ id: 'test' });

            expect(result).toEqual({ result: 'test-true-true' });
            expect(middleware1).toHaveBeenCalled();
            expect(middleware2).toHaveBeenCalled();
        });
    });

    describe('Authorization Middleware', () => {
        it('should implement authentication middleware', async () => {
            const authMiddleware = async (opts: any) => {
                const { ctx } = opts;
                if (!ctx.userId) {
                    throw new Error('UNAUTHORIZED');
                }

                // Simulate user lookup
                const user = { id: ctx.userId, role: 'user' as const };

                return opts.next({
                    ctx: {
                        ...ctx,
                        user,
                    },
                });
            };

            const protectedCommand = command.use(authMiddleware)({
                input: z.object({ data: z.string() }),
                output: z.object({ success: z.boolean(), userId: z.string() }),
                handler: async ({ input, ctx }) => {
                    const authenticatedCtx = ctx as AuthenticatedContext;
                    return {
                        success: true,
                        userId: authenticatedCtx.user.id
                    };
                },
            });

            const testRegistry = registry({ protectedCommand });

            // Test with authenticated context
            const authenticatedCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-789', userId: 'user-123' }),
            });

            const result = await authenticatedCqrs.commands.protectedCommand({ data: 'test' });
            expect(result).toEqual({ success: true, userId: 'user-123' });

            // Test with unauthenticated context
            const unauthenticatedCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-789' }), // No userId
            });

            await expect(
                unauthenticatedCqrs.commands.protectedCommand({ data: 'test' })
            ).rejects.toThrow('UNAUTHORIZED');
        });

        it('should implement role-based authorization', async () => {
            const adminMiddleware = async (opts: any) => {
                const { ctx } = opts;
                if (!ctx.userId) {
                    throw new Error('UNAUTHORIZED');
                }

                const user = {
                    id: ctx.userId,
                    role: ctx.userId === 'admin-123' ? 'admin' as const : 'user' as const
                };

                if (user.role !== 'admin') {
                    throw new Error('FORBIDDEN');
                }

                return opts.next({
                    ctx: {
                        ...ctx,
                        user,
                    },
                });
            };

            const adminCommand = command.use(adminMiddleware)({
                input: z.object({ action: z.string() }),
                output: z.object({ executed: z.boolean() }),
                handler: async ({ input, ctx }) => {
                    return { executed: true };
                },
            });

            const testRegistry = registry({ adminCommand });

            // Test with admin user
            const adminCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-admin', userId: 'admin-123' }),
            });

            const adminResult = await adminCqrs.commands.adminCommand({ action: 'delete' });
            expect(adminResult).toEqual({ executed: true });

            // Test with regular user
            const userCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-user', userId: 'user-123' }),
            });

            await expect(
                userCqrs.commands.adminCommand({ action: 'delete' })
            ).rejects.toThrow('FORBIDDEN');
        });
    });

    describe('Logging Middleware', () => {
        it('should measure execution time', async () => {
            const timingMiddleware = async (opts: any) => {
                const start = Date.now();
                const result = await opts.next();
                const duration = Date.now() - start;

                // In a real implementation, you'd log this
                expect(duration).toBeGreaterThanOrEqual(0);

                return result;
            };

            const timedQuery = query.use(timingMiddleware)({
                input: z.object({ id: z.string() }),
                output: z.object({ data: z.string() }),
                handler: async ({ input }) => {
                    // Simulate some work
                    await new Promise(resolve => setTimeout(resolve, 10));
                    return { data: input.id };
                },
            });

            const testRegistry = registry({ timedQuery });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-timing' }),
            });

            const result = await cqrs.queries.timedQuery({ id: 'test' });
            expect(result).toEqual({ data: 'test' });
        });

        it('should log requests', async () => {
            const logs: string[] = [];

            const requestLoggingMiddleware = async (opts: any) => {
                const { path, type, input } = opts;
                logs.push(`${type.toUpperCase()}: ${path} - ${JSON.stringify(input)}`);

                const result = await opts.next();

                logs.push(`${type.toUpperCase()}: ${path} - completed`);

                return result;
            };

            const loggedCommand = command.use(requestLoggingMiddleware)({
                input: z.object({ shouldFail: z.boolean() }),
                output: z.object({ success: z.boolean() }),
                handler: async ({ input }) => {
                    if (input.shouldFail) {
                        throw new Error('Intentional failure');
                    }
                    return { success: true };
                },
            });

            const testRegistry = registry({ loggedCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-logging' }),
            });

            await cqrs.commands.loggedCommand({ shouldFail: false });

            expect(logs).toEqual([
                'COMMAND: loggedCommand - {"shouldFail":false}',
                'COMMAND: loggedCommand - completed'
            ]);
        });
    });

    describe('Context Extension', () => {
        it('should allow middleware to extend context', async () => {
            const contextQuery = query.use(async (opts) => {
                const { ctx } = opts;

                return opts.next({
                    ctx: {
                        ...ctx,
                        timestamp: Date.now(),
                        isAuthenticated: true,
                    } as const,
                });
            })({
                input: z.object({ id: z.string() }),
                output: z.object({
                    result: z.string(),
                    isAuthenticated: z.boolean(),
                    timestamp: z.number()
                }),
                handler: async ({ input, ctx }) => {
                    const extendedCtx = ctx as BaseContext & {
                        timestamp: number;
                        isAuthenticated: boolean;
                    };

                    return {
                        result: input.id,
                        isAuthenticated: extendedCtx.isAuthenticated,
                        timestamp: extendedCtx.timestamp,
                    };
                },
            });

            const testRegistry = registry({ contextQuery });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-context' }),
            });

            const result = await cqrs.queries.contextQuery({ id: 'test' });
            expect(result.result).toBe('test');
            expect(result.isAuthenticated).toBe(true);
            expect(typeof result.timestamp).toBe('number');
        });

        it('should properly type context extensions', async () => {
            // This test demonstrates TypeScript type safety with middleware
            const typedMiddleware = async (opts: any) => {
                return opts.next({
                    ctx: {
                        ...opts.ctx,
                        enhanced: true,
                    },
                });
            };

            const typedCommand = command.use(typedMiddleware)({
                input: z.object({ test: z.string() }),
                output: z.object({ result: z.string() }),
                handler: async ({ input, ctx }) => {
                    // TypeScript should know about the enhanced context
                    const enhancedCtx = ctx as BaseContext & { enhanced: boolean };
                    return { result: `${input.test}-${enhancedCtx.enhanced}` };
                },
            });

            const testRegistry = registry({ typedCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-typed' }),
            });

            const result = await cqrs.commands.typedCommand({ test: 'typed' });
            expect(result).toEqual({ result: 'typed-true' });
        });

        it('should pass context through to multiples middlewares by default', async () => {
            const typedMiddleware = async (opts: any) => {
                return opts.next();
            };
            const cutoffChain = async (opts: any) => {
                return opts.next({ ctx: {} });
            };

            const typedCommand = command.use(cutoffChain).use(typedMiddleware).use(cutoffChain)({
                input: z.object({ test: z.string() }),
                output: z.object({ result: z.string() }),
                handler: async ({ input, ctx }) => {
                    expect(ctx.requestId).toBeDefined();
                    return { result: `${input.test}-${ctx.requestId}` };
                },
            });

            const testRegistry = registry({ typedCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-typed' }),
            });

            const result = await cqrs.commands.typedCommand({ test: 'typed' });
            expect(result).toEqual({ result: 'typed-test-typed' });
        });
    });

    describe('Error Handling', () => {
        it('should allow middleware to catch and transform errors', async () => {
            const errorTransformMiddleware = async (opts: any) => {
                try {
                    return await opts.next();
                } catch (error) {
                    if (error instanceof Error && error.message === 'Intentional failure') {
                        throw new Error('TRANSFORMED_ERROR');
                    }
                    throw error;
                }
            };

            const errorCommand = command.use(errorTransformMiddleware)({
                input: z.object({ test: z.string() }),
                output: z.object({ result: z.string() }),
                handler: async ({ input }) => {
                    throw new Error('Intentional failure');
                },
            });

            const testRegistry = registry({ errorCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-error' }),
            });

            await expect(
                cqrs.commands.errorCommand({ test: 'fail' })
            ).rejects.toThrow('TRANSFORMED_ERROR');
        });

        it('should handle middleware errors gracefully', async () => {
            const faultyMiddleware = async (opts: any) => {
                const { input } = opts;
                if (input.triggerMiddlewareError) {
                    throw new Error('MIDDLEWARE_ERROR');
                }
                return opts.next();
            };

            const testCommand = command.use(faultyMiddleware)({
                input: z.object({
                    data: z.string(),
                    triggerMiddlewareError: z.boolean().optional()
                }),
                output: z.object({ success: z.boolean() }),
                handler: async ({ input }) => {
                    return { success: true };
                },
            });

            const testRegistry = registry({ testCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-middleware-error' }),
            });

            // Should work when middleware doesn't error
            const successResult = await cqrs.commands.testCommand({
                data: 'test',
                triggerMiddlewareError: false
            });
            expect(successResult).toEqual({ success: true });

            // Should throw when middleware errors
            await expect(
                cqrs.commands.testCommand({
                    data: 'test',
                    triggerMiddlewareError: true
                })
            ).rejects.toThrow('MIDDLEWARE_ERROR');
        });
    });

    describe('Middleware Execution Order', () => {
        it('should execute middleware in the correct order', async () => {
            const executionOrder: string[] = [];

            const middleware1 = async (opts: any) => {
                executionOrder.push('middleware1-before');
                const result = await opts.next();
                executionOrder.push('middleware1-after');
                return result;
            };

            const middleware2 = async (opts: any) => {
                executionOrder.push('middleware2-before');
                const result = await opts.next();
                executionOrder.push('middleware2-after');
                return result;
            };

            const middleware3 = async (opts: any) => {
                executionOrder.push('middleware3-before');
                const result = await opts.next();
                executionOrder.push('middleware3-after');
                return result;
            };

            const orderedCommand = command
                .use(middleware1)
                .use(middleware2)
                .use(middleware3)({
                    input: z.object({ test: z.string() }),
                    output: z.object({ result: z.string() }),
                    handler: async ({ input }) => {
                        executionOrder.push('handler');
                        return { result: input.test };
                    },
                });

            const testRegistry = registry({ orderedCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-order' }),
            });

            const result = await cqrs.commands.orderedCommand({ test: 'order-test' });

            expect(result).toEqual({ result: 'order-test' });
            expect(executionOrder).toEqual([
                'middleware1-before',
                'middleware2-before',
                'middleware3-before',
                'handler',
                'middleware3-after',
                'middleware2-after',
                'middleware1-after'
            ]);
        });
    });

    describe('Integration with tRPC', () => {
        it('should work with tRPC client calls', async () => {
            const authMiddleware = async (opts: any) => {
                const { ctx } = opts;
                if (!ctx.userId) {
                    throw new Error('UNAUTHORIZED');
                }

                return opts.next({
                    ctx: {
                        ...ctx,
                        user: { id: ctx.userId, role: 'user' as const },
                    },
                });
            };

            const authenticatedQuery = query.use(authMiddleware)({
                input: z.object({ id: z.string() }),
                output: z.object({ data: z.string(), authenticated: z.boolean() }),
                handler: async ({ input, ctx }) => {
                    const authenticatedCtx = ctx as AuthenticatedContext;
                    return {
                        data: input.id,
                        authenticated: true
                    };
                },
            });

            const testRegistry = registry({ authenticatedQuery });

            // Test with authenticated context
            const authenticatedCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-trpc', userId: 'user-123' }),
            });

            const result = await authenticatedCqrs.queries.authenticatedQuery({ id: 'test-data' });
            expect(result).toEqual({ data: 'test-data', authenticated: true });

            // Test with unauthenticated context should fail
            const unauthenticatedCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ requestId: 'test-trpc' }), // No userId
            });

            await expect(
                unauthenticatedCqrs.queries.authenticatedQuery({ id: 'test-data' })
            ).rejects.toThrow('UNAUTHORIZED');
        });
    });
});
