import { z } from 'zod';
import { expectTypeOf } from 'expect-type';
import { CQRS } from '../cqrs';

describe('CQRS Mocking API', () => {
    type TestContext = {
        userId: string;
        userService: {
            create: jest.Mock;
            findById: jest.Mock;
        };
    };

    const mockContext: TestContext = {
        userId: 'test-user',
        userService: {
            create: jest.fn().mockImplementation(async (data) => ({
                id: 'user-123',
                name: data.name
            })),
            findById: jest.fn().mockImplementation(async (id) => ({
                id,
                name: '<PERSON>',
                email: '<EMAIL>'
            })),
        },
    };

    const { define, registry } = CQRS.create<TestContext>();
    const { command, query } = define;

    // Define test procedures
    const createUser = command({
        input: z.object({
            name: z.string().min(1),
            email: z.string().email()
        }),
        output: z.object({ id: z.string(), name: z.string() }),
        handler: async ({ input, ctx }) => ctx.userService.create(input),
    });

    const getUser = query({
        input: z.object({ id: z.string().min(1) }),
        output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
        handler: async ({ input, ctx }) => ctx.userService.findById(input.id),
    });

    const deleteUser = command({
        input: z.object({ id: z.string().min(1) }),
        output: z.object({ success: z.boolean() }),
        handler: async ({ input, ctx }) => {
            // Simulate deletion
            return { success: true };
        },
    });

    const testRegistry = registry({
        createUser,
        getUser,
        deleteUser,
    });

    beforeEach(() => {
        jest.clearAllMocks();
    });

    afterEach(() => {
        // Clear any mock registries that might have been created
        jest.clearAllMocks();
    });

    describe('Registry enableMocking method', () => {
        it('should have enableMocking method on registry', () => {
            expect(typeof testRegistry.enableMocking).toBe('function');
        });

        it('should return a mocked registry with correct structure', () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);

            // Should have all original procedures
            expect(mockRegistry.createUser).toBeDefined();
            expect(mockRegistry.getUser).toBeDefined();
            expect(mockRegistry.deleteUser).toBeDefined();

            // Should have registry-level mock methods
            expect(typeof mockRegistry.spyOn).toBe('function');
            expect(typeof mockRegistry.getCalls).toBe('function');
            expect(typeof mockRegistry.clearAllMocks).toBe('function');
            expect(typeof mockRegistry.resetAllMocks).toBe('function');
            expect(typeof mockRegistry.restoreAllMocks).toBe('function');
            expect(typeof mockRegistry.getOriginalRegistry).toBe('function');
        });
    });

    describe('Type Safety', () => {
        it('should maintain type safety for mocked procedures', () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);

            // Commands should have CommandHandler structure with mock methods
            expectTypeOf(mockRegistry.createUser).toBeCallableWith({
                name: 'John',
                email: '<EMAIL>'
            });

            // Mock methods should be present
            expectTypeOf(mockRegistry.createUser.mockResolvedValue).toBeCallableWith({
                id: 'test',
                name: 'test'
            });
            expectTypeOf(mockRegistry.createUser.mockImplementation).toBeCallableWith(
                expect.any(Function)
            );

            // Queries should have simple function structure with mock methods
            expectTypeOf(mockRegistry.getUser).toBeCallableWith({ id: 'user-123' });
            expectTypeOf(mockRegistry.getUser.mockResolvedValue).toBeCallableWith({
                id: 'test',
                name: 'test',
                email: '<EMAIL>'
            });

            // spyOn should return the correct type
            expectTypeOf(mockRegistry.spyOn('createUser')).toEqualTypeOf(mockRegistry.createUser);
            expectTypeOf(mockRegistry.spyOn('getUser')).toEqualTypeOf(mockRegistry.getUser);
        });
    });

    describe('Mock Functionality', () => {
        let mockRegistry: ReturnType<typeof testRegistry.enableMocking>;

        beforeEach(() => {
            mockRegistry = testRegistry.enableMocking(mockContext);
        });

        it('should track calls to mocked procedures', async () => {
            // Execute some calls
            await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });
            await mockRegistry.getUser({ id: 'user-123' });
            await mockRegistry.createUser({ name: 'Jane', email: '<EMAIL>' });

            // Check call tracking
            const createUserCalls = mockRegistry.getCalls('createUser');
            const getUserCalls = mockRegistry.getCalls('getUser');

            expect(createUserCalls.calls).toHaveLength(2);
            expect(createUserCalls.calls[0]).toEqual([{ name: 'John', email: '<EMAIL>' }]);
            expect(createUserCalls.calls[1]).toEqual([{ name: 'Jane', email: '<EMAIL>' }]);
            expect(createUserCalls.lastCall).toEqual([{ name: 'Jane', email: '<EMAIL>' }]);

            expect(getUserCalls.calls).toHaveLength(1);
            expect(getUserCalls.calls[0]).toEqual([{ id: 'user-123' }]);
            expect(getUserCalls.lastCall).toEqual([{ id: 'user-123' }]);
        });

        it('should support mockResolvedValue', async () => {
            const mockValue = { id: 'mocked-id', name: 'Mocked User' };
            mockRegistry.spyOn('createUser').mockResolvedValue(mockValue);

            const result = await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });

            expect(result).toEqual(mockValue);
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(1);
        });

        it('should support mockRejectedValue', async () => {
            const mockError = new Error('Mocked error');
            mockRegistry.spyOn('getUser').mockRejectedValue(mockError);

            await expect(mockRegistry.getUser({ id: 'user-123' })).rejects.toThrow('Mocked error');
            expect(mockRegistry.getCalls('getUser').calls).toHaveLength(1);
        });

        it('should support mockImplementation', async () => {
            const mockImpl = jest.fn().mockResolvedValue({ id: 'custom-id', name: 'Custom User' });
            mockRegistry.spyOn('createUser').mockImplementation(mockImpl);

            const result = await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });

            expect(result).toEqual({ id: 'custom-id', name: 'Custom User' });
            expect(mockImpl).toHaveBeenCalledWith({ input: { name: 'John', email: '<EMAIL>' } });
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(1);
        });

        it('should support mockImplementationOnce', async () => {
            const firstImpl = jest.fn().mockResolvedValue({ id: 'first', name: 'First' });
            const secondImpl = jest.fn().mockResolvedValue({ id: 'second', name: 'Second' });

            mockRegistry.spyOn('createUser')
                .mockImplementationOnce(firstImpl)
                .mockImplementationOnce(secondImpl)
                .mockResolvedValue({ id: 'default', name: 'Default' });

            const result1 = await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });
            const result2 = await mockRegistry.createUser({ name: 'Jane', email: '<EMAIL>' });
            const result3 = await mockRegistry.createUser({ name: 'Bob', email: '<EMAIL>' });

            expect(result1).toEqual({ id: 'first', name: 'First' });
            expect(result2).toEqual({ id: 'second', name: 'Second' });
            expect(result3).toEqual({ id: 'default', name: 'Default' });

            expect(firstImpl).toHaveBeenCalledTimes(1);
            expect(secondImpl).toHaveBeenCalledTimes(1);
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(3);
        });

        it('should support clearing mocks', async () => {
            // Make some calls first
            await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });
            await mockRegistry.getUser({ id: 'user-123' });

            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(1);
            expect(mockRegistry.getCalls('getUser').calls).toHaveLength(1);

            // Clear individual mock
            mockRegistry.spyOn('createUser').mockClear();
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(0);
            expect(mockRegistry.getCalls('getUser').calls).toHaveLength(1);

            // Clear all mocks
            mockRegistry.clearAllMocks();
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(0);
            expect(mockRegistry.getCalls('getUser').calls).toHaveLength(0);
        });

        it('should support resetting mocks', async () => {
            // Set up mocks
            mockRegistry.spyOn('createUser').mockResolvedValue({ id: 'mocked', name: 'Mocked' });
            mockRegistry.spyOn('getUser').mockImplementation(async () => ({ id: 'impl', name: 'Impl', email: '<EMAIL>' }));

            // Make calls
            await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });
            await mockRegistry.getUser({ id: 'user-123' });

            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(1);
            expect(mockRegistry.getCalls('getUser').calls).toHaveLength(1);

            // Reset individual mock
            mockRegistry.spyOn('createUser').mockReset();
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(0);

            // Reset all mocks
            mockRegistry.resetAllMocks();
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(0);
            expect(mockRegistry.getCalls('getUser').calls).toHaveLength(0);
        });

        it('should fall back to original implementation when no mock is set', async () => {
            // No mocks set, should use original implementation
            const result = await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });

            // Should call the original handler which uses mockContext.userService.create
            expect(result).toEqual({ id: 'user-123', name: 'John' });
            expect(mockContext.userService.create).toHaveBeenCalledWith({ name: 'John', email: '<EMAIL>' });
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(1);
        });

        it('should allow using mocked commands like functions', async () => {
            const mockedCreateUser = mockRegistry.spyOn('createUser');

            // Use mockImplementationOnce as a more robust way to test one-off mocks
            mockedCreateUser.mockImplementationOnce(async () => ({ id: 'once', name: 'Once' }));

            const result = await mockedCreateUser({ name: 'Test', email: '<EMAIL>' });
            expect(result).toEqual({ id: 'once', name: 'Once' });

            // The original mock implementation should be restored after "once"
            const secondResult = await mockedCreateUser({ name: 'Second', email: '<EMAIL>' });
            // This will call the original handler since we used mockImplementationOnce
            expect(secondResult).toEqual({ id: 'user-123', name: 'Second' });
            expect(mockRegistry.getCalls('createUser').calls).toHaveLength(2);
        });
    });

    describe('Jest/Vitest Compatibility', () => {
        it('should work with Jest expect assertions', async () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);

            await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });
            await mockRegistry.createUser({ name: 'Jane', email: '<EMAIL>' });
            await mockRegistry.getUser({ id: 'user-123' });

            // Jest-style assertions should work
            const createUserMock = mockRegistry.spyOn('createUser');
            const getUserMock = mockRegistry.spyOn('getUser');

            expect(createUserMock.mock.calls).toHaveLength(2);
            expect(createUserMock.mock.calls[0]).toEqual([{ name: 'John', email: '<EMAIL>' }]);
            expect(createUserMock.mock.calls[1]).toEqual([{ name: 'Jane', email: '<EMAIL>' }]);

            expect(getUserMock.mock.calls).toHaveLength(1);
            expect(getUserMock.mock.calls[0]).toEqual([{ id: 'user-123' }]);

            // Should work with toHaveBeenCalledWith
            expect(createUserMock).toHaveBeenCalledWith({ name: 'John', email: '<EMAIL>' });
            expect(createUserMock).toHaveBeenCalledWith({ name: 'Jane', email: '<EMAIL>' });
            expect(getUserMock).toHaveBeenCalledWith({ id: 'user-123' });

            // Should work with toHaveBeenCalledTimes
            expect(createUserMock).toHaveBeenCalledTimes(2);
            expect(getUserMock).toHaveBeenCalledTimes(1);
        });

        it('should provide mock object compatible with Jest matchers', () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);
            const createUserMock = mockRegistry.spyOn('createUser');

            // Mock object should have the expected structure
            expect(createUserMock.mock).toHaveProperty('calls');
            expect(createUserMock.mock).toHaveProperty('results');
            expect(createUserMock.mock).toHaveProperty('instances');
            expect(createUserMock.mock).toHaveProperty('lastCall');

            expect(Array.isArray(createUserMock.mock.calls)).toBe(true);
            expect(Array.isArray(createUserMock.mock.results)).toBe(true);
            expect(Array.isArray(createUserMock.mock.instances)).toBe(true);
        });
    });

    describe('Mocking Edge Cases', () => {
        let mockRegistry: ReturnType<typeof testRegistry.enableMocking>;

        beforeEach(() => {
            mockRegistry = testRegistry.enableMocking(mockContext);
        });

        it('should throw when spying on a non-existent procedure', () => {
            expect(() => {
                // @ts-expect-error - procedure does not exist
                mockRegistry.spyOn('nonExistentProcedure');
            }).toThrow("Procedure 'nonExistentProcedure' not found in registry");
        });

        it('should handle mockImplementation returning a Promise', async () => {
            mockRegistry.spyOn('createUser').mockImplementation(async (opts) => {
                await new Promise(res => setTimeout(res, 10));
                return { id: 'promise-id', name: opts.input.name };
            });
            const result = await mockRegistry.createUser({ name: 'Promise', email: '<EMAIL>' });
            expect(result).toEqual({ id: 'promise-id', name: 'Promise' });
        });

        it('should return empty calls for a procedure that has not been called', () => {
            const calls = mockRegistry.getCalls('deleteUser');
            expect(calls.calls).toEqual([]);
            expect(calls.lastCall).toBeUndefined();
        });

        it('should reset mock implementation to the original', async () => {
            mockRegistry.spyOn('createUser').mockResolvedValue({ id: 'mocked', name: 'Mocked' });
            await mockRegistry.createUser({ name: 'test', email: '<EMAIL>' });

            mockRegistry.spyOn('createUser').mockReset();

            const result = await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });
            expect(result).toEqual({ id: 'user-123', name: 'John' }); // Original implementation
        });

        it('should restore the original implementation', async () => {
            mockRegistry.spyOn('createUser').mockResolvedValue({ id: 'mocked', name: 'Mocked' });

            mockRegistry.spyOn('createUser').mockRestore();

            const result = await mockRegistry.createUser({ name: 'John', email: '<EMAIL>' });
            expect(result).toEqual({ id: 'user-123', name: 'John' }); // Original implementation
        });
    });

    describe('Error Recovery and Edge Cases', () => {
        it('should handle mocking when no results are set', async () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);
            const createUserSpy = mockRegistry.spyOn('createUser');

            // Call without setting any mock behavior - should fall back to original
            const result = await createUserSpy({ name: 'Test', email: '<EMAIL>' });

            expect(result).toEqual({ id: 'user-123', name: 'Test' });
            expect(createUserSpy.mock.calls).toHaveLength(1);
        });

        it('should handle multiple implementations with mockImplementationOnce', async () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);
            const createUserSpy = mockRegistry.spyOn('createUser');

            createUserSpy
                .mockImplementationOnce(async ({ input }) => ({ id: '1', name: input.name }))
                .mockImplementationOnce(async ({ input }) => ({ id: '2', name: input.name }))
                .mockImplementation(async ({ input }) => ({ id: 'default', name: input.name }));

            // First call uses first implementation
            const result1 = await createUserSpy({ name: 'User1', email: '<EMAIL>' });
            expect(result1).toEqual({ id: '1', name: 'User1' });

            // Second call uses second implementation
            const result2 = await createUserSpy({ name: 'User2', email: '<EMAIL>' });
            expect(result2).toEqual({ id: '2', name: 'User2' });

            // Third call uses default implementation
            const result3 = await createUserSpy({ name: 'User3', email: '<EMAIL>' });
            expect(result3).toEqual({ id: 'default', name: 'User3' });
        });

        it('should handle errors thrown by mock implementations', async () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);
            const createUserSpy = mockRegistry.spyOn('createUser');

            createUserSpy.mockImplementation(async () => {
                throw new Error('Mock implementation failed');
            });

            await expect(createUserSpy({ name: 'Test', email: '<EMAIL>' }))
                .rejects.toThrow('Mock implementation failed');

            expect(createUserSpy.mock.calls).toHaveLength(1);
            expect(createUserSpy.mock.results).toHaveLength(1);
            expect(createUserSpy.mock.results[0].type).toBe('throw');
            expect(createUserSpy.mock.results[0].value.message).toBe('Mock implementation failed');
        });

        it('should handle async context callbacks', async () => {
            const asyncContext = async () => {
                // Simulate async work
                await new Promise(resolve => setTimeout(resolve, 1));
                return {
                    userId: 'async-user',
                    userService: mockContext.userService
                };
            };

            const mockRegistry = testRegistry.enableMocking(asyncContext);
            const createUserSpy = mockRegistry.spyOn('createUser');

            createUserSpy.mockResolvedValue({ id: 'async-result', name: 'Async User' });

            const result = await createUserSpy({ name: 'Test', email: '<EMAIL>' });
            expect(result).toEqual({ id: 'async-result', name: 'Async User' });
        });

        it('should maintain separate mock states for different instances', async () => {
            const mockRegistry1 = testRegistry.enableMocking(mockContext);
            const mockRegistry2 = testRegistry.enableMocking(mockContext);

            const spy1 = mockRegistry1.spyOn('createUser');
            const spy2 = mockRegistry2.spyOn('createUser');

            spy1.mockResolvedValue({ id: 'registry1', name: 'User 1' });
            spy2.mockResolvedValue({ id: 'registry2', name: 'User 2' });

            const result1 = await spy1({ name: 'Test', email: '<EMAIL>' });
            const result2 = await spy2({ name: 'Test', email: '<EMAIL>' });

            expect(result1).toEqual({ id: 'registry1', name: 'User 1' });
            expect(result2).toEqual({ id: 'registry2', name: 'User 2' });

            // Each should have their own call tracking
            expect(spy1.mock.calls).toHaveLength(1);
            expect(spy2.mock.calls).toHaveLength(1);
        });

        it('should handle clearing mocks during test execution', async () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);
            const createUserSpy = mockRegistry.spyOn('createUser');

            // Make some calls
            await createUserSpy({ name: 'User1', email: '<EMAIL>' });
            await createUserSpy({ name: 'User2', email: '<EMAIL>' });

            expect(createUserSpy.mock.calls).toHaveLength(2);

            // Clear and make another call
            createUserSpy.mockClear();
            await createUserSpy({ name: 'User3', email: '<EMAIL>' });

            expect(createUserSpy.mock.calls).toHaveLength(1);
            expect(createUserSpy.mock.lastCall).toEqual([{ name: 'User3', email: '<EMAIL>' }]);
        });

        it('should handle resetting mocks and falling back to original implementation', async () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);
            const createUserSpy = mockRegistry.spyOn('createUser');

            createUserSpy.mockResolvedValue({ id: 'mocked', name: 'Mocked User' });

            // Call with mock
            const mockedResult = await createUserSpy({ name: 'Test', email: '<EMAIL>' });
            expect(mockedResult).toEqual({ id: 'mocked', name: 'Mocked User' });

            // Reset and call again
            createUserSpy.mockReset();
            const originalResult = await createUserSpy({ name: 'Test', email: '<EMAIL>' });
            expect(originalResult).toEqual({ id: 'user-123', name: 'Test' });

            // Calls should be cleared but original behavior restored
            expect(createUserSpy.mock.calls).toHaveLength(1);
        });

        it('should handle spying on non-existent procedures', () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);

            expect(() => {
                mockRegistry.spyOn('nonExistentProcedure' as any);
            }).toThrow("Procedure 'nonExistentProcedure' not found in registry");
        });

        it('should handle getCalls for non-existent procedures', () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);

            expect(() => {
                mockRegistry.getCalls('nonExistentProcedure' as any);
            }).toThrow("Procedure 'nonExistentProcedure' not found or not mocked");
        });

        it('should preserve all registry methods on mocked registry', () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);

            // Check that all original registry methods are preserved
            expect(typeof mockRegistry.getRouteDefinitions).toBe('function');
            expect(typeof mockRegistry.getCommandNames).toBe('function');
            expect(typeof mockRegistry.getQueryNames).toBe('function');
            expect(typeof mockRegistry.getAllProcedureNames).toBe('function');
            expect(typeof mockRegistry.getProcedureMetadata).toBe('function');
            expect(typeof mockRegistry.createCaller).toBe('function');

            // Check that mock-specific methods are available
            expect(typeof mockRegistry.spyOn).toBe('function');
            expect(typeof mockRegistry.getCalls).toBe('function');
            expect(typeof mockRegistry.clearAllMocks).toBe('function');
            expect(typeof mockRegistry.resetAllMocks).toBe('function');
            expect(typeof mockRegistry.restoreAllMocks).toBe('function');
            expect(typeof mockRegistry.getOriginalRegistry).toBe('function');

            // enableMocking should return the same instance
            expect(mockRegistry.enableMocking()).toBe(mockRegistry);
        });

        it('should handle Jest spy compatibility methods', () => {
            const mockRegistry = testRegistry.enableMocking(mockContext);
            const createUserSpy = mockRegistry.spyOn('createUser');

            // Check Jest compatibility properties
            expect((createUserSpy as any)._isMockFunction).toBe(true);
            expect(typeof (createUserSpy as any).getMockName).toBe('function');
            expect(typeof (createUserSpy as any).mockName).toBe('function');

            // getMockName should return procedure name
            expect((createUserSpy as any).getMockName()).toBe('createUser');

            // mockName should return the spy (for chaining)
            expect((createUserSpy as any).mockName()).toBe(createUserSpy);
        });
    });
});
