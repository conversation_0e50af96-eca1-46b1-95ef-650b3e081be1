import { z } from 'zod';
import { expectTypeOf } from 'expect-type';
import { createTRPCClient, experimental_localLink } from '@trpc/client';
import type { inferRouterInputs, inferRouterOutputs } from '@trpc/server';
import { CQRS } from '../cqrs';

describe('tRPC Client Integration', () => {
    type TestContext = {
        userId: string;
        userService: {
            create: (data: { name: string; email: string }) => Promise<{ id: string; name: string }>;
            findById: (id: string) => Promise<{ id: string; name: string; email: string }>;
        };
    };

    const mockContext: TestContext = {
        userId: 'test-user',
        userService: {
            create: jest.fn().mockImplementation(async (data) => ({
                id: 'user-123',
                name: data.name
            })),
            findById: jest.fn().mockImplementation(async (id) => ({
                id,
                name: '<PERSON>',
                email: '<EMAIL>'
            })),
        },
    };

    const { define, registry } = CQRS.create<TestContext>();
    const { command, query } = define;

    // Define CQRS procedures
    const createUser = command({
        input: z.object({
            name: z.string().min(1, 'Name cannot be empty'),
            email: z.string().email('Invalid email format')
        }),
        metadata: { trpc: {} },
        output: z.object({ id: z.string(), name: z.string() }),
        handler: async ({ input, ctx }) => ctx.userService.create(input),
    });

    const getUser = query({
        input: z.object({ id: z.string().min(1, 'ID cannot be empty') }),
        output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
        metadata: { trpc: {} },
        handler: async ({ input, ctx }) => ctx.userService.findById(input.id),
    });

    const getUserByEmail = query({
        input: z.object({ email: z.string().email('Invalid email format') }),
        output: z.object({ id: z.string(), name: z.string(), email: z.string() }).nullable(),
        metadata: { trpc: {} },
        handler: async ({ input, ctx }) => {
            // Mock implementation
            if (input.email === '<EMAIL>') {
                return { id: 'user-123', name: 'John Doe', email: input.email };
            }
            return null;
        },
    });

    const testRegistry = registry({
        createUser,
        getUser,
        getUserByEmail,
    });

    const cqrs = new CQRS({
        registry: testRegistry,
        getContext: () => mockContext,
    });

    describe('tRPC Router Creation', () => {
        it('should create a tRPC router from CQRS registry', () => {
            // This method should exist on the CQRS instance
            const router = cqrs.getTRPCRouter();

            // The router should be a valid tRPC router
            expectTypeOf(router).toHaveProperty('_def');
            expectTypeOf(router).toHaveProperty('createCaller');
        });

        it('should have correct procedure types in the router', () => {
            const router = cqrs.getTRPCRouter();

            // Commands should be mutations
            expectTypeOf(router.createUser).toHaveProperty('_def');
            expect(router.createUser._def.type).toBe('mutation');

            // Queries should be queries
            expectTypeOf(router.getUser).toHaveProperty('_def');
            expect(router.getUser._def.type).toBe('query');

            expectTypeOf(router.getUserByEmail).toHaveProperty('_def');
            expect(router.getUserByEmail._def.type).toBe('query');
        });
    });

    describe('Type Inference', () => {
        it('should properly infer router input and output types', () => {
            const router = cqrs.getTRPCRouter();
            type AppRouter = typeof router;

            // Test input type inference
            type RouterInputs = inferRouterInputs<AppRouter>;
            expectTypeOf<RouterInputs['createUser']>().toEqualTypeOf<{ name: string; email: string }>();
            expectTypeOf<RouterInputs['getUser']>().toEqualTypeOf<{ id: string }>();
            expectTypeOf<RouterInputs['getUserByEmail']>().toEqualTypeOf<{ email: string }>();

            // Test output type inference
            type RouterOutputs = inferRouterOutputs<AppRouter>;
            expectTypeOf<RouterOutputs['createUser']>().toEqualTypeOf<{ id: string; name: string }>();
            expectTypeOf<RouterOutputs['getUser']>().toEqualTypeOf<{ id: string; name: string; email: string }>();
            expectTypeOf<RouterOutputs['getUserByEmail']>().toEqualTypeOf<{ id: string; name: string; email: string } | null>();
        });
    });

    describe('tRPC Caller Integration', () => {
        it('should execute commands and queries through tRPC caller', async () => {
            const router = cqrs.getTRPCRouter();
            const caller = router.createCaller(mockContext);

            // Test command execution
            const createResult = await caller.createUser({
                name: 'John Doe',
                email: '<EMAIL>'
            });

            expect(createResult).toEqual({ id: 'user-123', name: 'John Doe' });
            expect(mockContext.userService.create).toHaveBeenCalledWith({
                name: 'John Doe',
                email: '<EMAIL>'
            });

            // Test query execution
            const getResult = await caller.getUser({ id: 'user-123' });

            expect(getResult).toEqual({ id: 'user-123', name: 'John Doe', email: '<EMAIL>' });
            expect(mockContext.userService.findById).toHaveBeenCalledWith('user-123');

            // Test nullable query
            const getUserByEmailResult = await caller.getUserByEmail({ email: '<EMAIL>' });
            expect(getUserByEmailResult).toEqual({ id: 'user-123', name: 'John Doe', email: '<EMAIL>' });

            const getUserByEmailNullResult = await caller.getUserByEmail({ email: '<EMAIL>' });
            expect(getUserByEmailNullResult).toBeNull();
        });

        it('should provide type safety for caller methods', () => {
            const router = cqrs.getTRPCRouter();
            const caller = router.createCaller(mockContext);

            // These should be type-safe
            expectTypeOf(caller.createUser).parameter(0).toEqualTypeOf<{ name: string; email: string }>();
            expectTypeOf(caller.getUser).parameter(0).toEqualTypeOf<{ id: string }>();
            expectTypeOf(caller.getUserByEmail).parameter(0).toEqualTypeOf<{ email: string }>();

            // Return types should be properly inferred
            expectTypeOf(caller.createUser).returns.toEqualTypeOf<Promise<{ id: string; name: string }>>();
            expectTypeOf(caller.getUser).returns.toEqualTypeOf<Promise<{ id: string; name: string; email: string }>>();
            expectTypeOf(caller.getUserByEmail).returns.toEqualTypeOf<Promise<{ id: string; name: string; email: string } | null>>();
        });
    });

    describe('Client Creation Type Safety', () => {
        it('should provide type safety for client calls', () => {
            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // These should be type-safe
            expectTypeOf(client.createUser.mutate).parameter(0).toEqualTypeOf<{ name: string; email: string }>();
            expectTypeOf(client.getUser.query).parameter(0).toEqualTypeOf<{ id: string }>();
            expectTypeOf(client.getUserByEmail.query).parameter(0).toEqualTypeOf<{ email: string }>();

            // Return types should be properly inferred
            expectTypeOf(client.createUser.mutate).returns.toEqualTypeOf<Promise<{ id: string; name: string }>>();
            expectTypeOf(client.getUser.query).returns.toEqualTypeOf<Promise<{ id: string; name: string; email: string }>>();
            expectTypeOf(client.getUserByEmail.query).returns.toEqualTypeOf<Promise<{ id: string; name: string; email: string } | null>>();

            // @ts-expect-error The input type is wrong
            expect(client.createUser.mutate({ wrongField: 'value' })).rejects.toThrow();

            // @ts-expect-error The input type is wrong, it should be an object with id
            expect(client.getUser.query({ userID: 'user-123' })).rejects.toThrow();

            // @ts-expect-error A mutation cannot be called as a query
            client.createUser.query;

            // @ts-expect-error A query cannot be called as a mutation
            client.getUser.mutate;
        });
    });

    describe('Input Validation', () => {
        let client: ReturnType<typeof createTRPCClient<ReturnType<typeof cqrs.getTRPCRouter>>>;

        beforeEach(() => {
            const router = cqrs.getTRPCRouter();
            client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });
        });

        it('should validate command input schemas', async () => {
            // Valid input should work
            const validResult = await client.createUser.mutate({
                name: 'John Doe',
                email: '<EMAIL>'
            });
            expect(validResult).toEqual({ id: 'user-123', name: 'John Doe' });

            // Invalid input should throw validation errors
            await expect(client.createUser.mutate({
                name: '',  // Empty string should fail
                email: '<EMAIL>'
            } as any)).rejects.toThrow();

            await expect(client.createUser.mutate({
                name: 'John Doe',
                email: 'invalid-email'  // Invalid email format
            } as any)).rejects.toThrow();

            await expect(client.createUser.mutate({
                name: 'John Doe'
                // Missing email field
            } as any)).rejects.toThrow();

            await expect(client.createUser.mutate({
                name: 123,  // Wrong type
                email: '<EMAIL>'
            } as any)).rejects.toThrow();
        });

        it('should validate query input schemas', async () => {
            // Valid string input should work
            const validResult = await client.getUser.query({ id: 'user-123' });
            expect(validResult).toEqual({ id: 'user-123', name: 'John Doe', email: '<EMAIL>' });

            // Invalid input types should throw
            await expect(client.getUser.query(123 as any)).rejects.toThrow();
            await expect(client.getUser.query({} as any)).rejects.toThrow();
            await expect(client.getUser.query(null as any)).rejects.toThrow();
            await expect(client.getUser.query(undefined as any)).rejects.toThrow();

            // Valid object input for getUserByEmail should work
            const validEmailResult = await client.getUserByEmail.query({ email: '<EMAIL>' });
            expect(validEmailResult).toEqual({ id: 'user-123', name: 'John Doe', email: '<EMAIL>' });

            // Invalid email object should throw
            await expect(client.getUserByEmail.query({
                email: 'invalid-email'
            } as any)).rejects.toThrow();

            await expect(client.getUserByEmail.query({
                wrongField: 'value'
            } as any)).rejects.toThrow();

            await expect(client.getUserByEmail.query('string-instead-of-object' as any)).rejects.toThrow();
        });

        it('should handle edge cases in validation', async () => {
            // Empty strings where not allowed
            await expect(client.createUser.mutate({
                name: '',
                email: '<EMAIL>'
            } as any)).rejects.toThrow();

            // Very long strings (if we had length limits)
            const longName = 'a'.repeat(1000);
            const longEmailResult = await client.createUser.mutate({
                name: longName,
                email: '<EMAIL>'
            });
            expect(longEmailResult.name).toBe(longName);

            // Special characters in strings
            const specialResult = await client.createUser.mutate({
                name: 'John "Special" O\'Doe',
                email: '<EMAIL>'
            });
            expect(specialResult.name).toBe('John "Special" O\'Doe');
        });
    });

    describe('Output Validation', () => {
        it('should validate command output schemas', async () => {
            // Create a command that returns invalid output
            const { define: badDefine, registry: badRegistry } = CQRS.create<TestContext>();

            const badCommand = badDefine.command({
                input: z.object({ test: z.string() }),
                output: z.object({ success: z.boolean(), id: z.string() }),
                handler: async ({ input, ctx }) => {
                    // Return invalid output (missing required fields)
                    return { wrongField: 'value' } as any;
                },
            });

            const badTestRegistry = badRegistry({ badCommand });
            const badCqrs = new CQRS({
                registry: badTestRegistry,
                getContext: () => mockContext,
            });

            const badRouter = badCqrs.getTRPCRouter();
            const badClient = createTRPCClient<typeof badRouter>({
                links: [experimental_localLink({
                    router: badRouter,
                    createContext: async () => mockContext,
                })],
            });

            // This should throw due to output validation failure
            await expect(badClient.badCommand.mutate({ test: 'input' })).rejects.toThrow();
        });

        it("should remove non-trpc queries from the router", async () => {
            const { define: badDefine, registry: badRegistry } = CQRS.create<TestContext>();

            const badQuery = badDefine.query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
                handler: async ({ input, ctx }) => {
                    // Return invalid output (missing required fields)
                    return { id: input } as any;
                },
            });

            const badTestRegistry = badRegistry({ badQuery });
            const badCqrs = new CQRS({
                registry: badTestRegistry,
                getContext: () => mockContext,
            });

            const badRouter = badCqrs.getTRPCRouter();
            const badClient = createTRPCClient<typeof badRouter>({
                links: [experimental_localLink({
                    router: badRouter,
                    createContext: async () => mockContext,
                })],
            });

            // This should throw due to missing trpc metadata
            await expect(badClient.badQuery.query({ id: 'test-id' })).rejects.toThrow(/No "query"-procedure on path/);
        });

        it('should allow setting default trpc metadata via options', async () => {
            const { define, registry } = CQRS.create<TestContext>({
                defaultMetadata: { trpc: {} }
            });

            const testCommand = define.command({
                input: z.object({ test: z.string() }),
                output: z.object({ success: z.boolean() }),
                handler: async ({ input }) => {
                    return { success: true };
                },
            });

            const testRegistry = registry({ testCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => mockContext,
            });

            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router: router,
                    createContext: async () => mockContext,
                })],
            });

            // Should succeed since default trpc metadata is set
            await expect(client.testCommand.mutate({ test: 'input' })).resolves.toEqual({ success: true });
        });

        it('should validate query output schemas', async () => {
            // Create a query that returns invalid output
            const { define: badDefine, registry: badRegistry } = CQRS.create<TestContext>();

            const badQuery = badDefine.query({
                input: z.object({ id: z.string() }),
                output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
                metadata: { trpc: {} },
                handler: async ({ input, ctx }) => {
                    // Return invalid output (missing required fields)
                    return { id: input } as any;
                },
            });

            const badTestRegistry = badRegistry({ badQuery });
            const badCqrs = new CQRS({
                registry: badTestRegistry,
                getContext: () => mockContext,
            });

            const badRouter = badCqrs.getTRPCRouter();
            const badClient = createTRPCClient<typeof badRouter>({
                links: [experimental_localLink({
                    router: badRouter,
                    createContext: async () => mockContext,
                })],
            });

            // This should throw due to output validation failure
            await expect(badClient.badQuery.query({ id: 'test-id' })).rejects.toThrow(/invalid_type/);
        });
    });

    describe('Error Handling', () => {
        it('should handle command errors properly', async () => {
            // Create procedures that can throw errors
            const { define: errorDefine, registry: errorRegistry } = CQRS.create<TestContext>();

            const errorCommand = errorDefine.command({
                input: z.object({ shouldFail: z.boolean(), errorType: z.string().optional() }),
                output: z.object({ success: z.boolean() }),
                metadata: { trpc: {} },
                handler: async ({ input, ctx }) => {
                    if (input.shouldFail) {
                        switch (input.errorType) {
                            case 'validation':
                                throw new Error('Validation failed');
                            case 'authorization':
                                throw new Error('Unauthorized access');
                            case 'notfound':
                                throw new Error('Resource not found');
                            default:
                                throw new Error('Generic error');
                        }
                    }
                    return { success: true };
                },
            });

            const errorTestRegistry = errorRegistry({ errorCommand });
            const errorCqrs = new CQRS({
                registry: errorTestRegistry,
                getContext: () => mockContext,
            });

            const router = errorCqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // Successful execution
            const successResult = await client.errorCommand.mutate({ shouldFail: false });
            expect(successResult).toEqual({ success: true });

            // Generic error
            await expect(client.errorCommand.mutate({
                shouldFail: true
            })).rejects.toThrow('Generic error');

            // Specific error types
            await expect(client.errorCommand.mutate({
                shouldFail: true,
                errorType: 'validation'
            })).rejects.toThrow('Validation failed');

            await expect(client.errorCommand.mutate({
                shouldFail: true,
                errorType: 'authorization'
            })).rejects.toThrow('Unauthorized access');

            await expect(client.errorCommand.mutate({
                shouldFail: true,
                errorType: 'notfound'
            })).rejects.toThrow('Resource not found');
        });

        it('should handle query errors properly', async () => {
            // Create procedures that can throw errors
            const { define: errorDefine, registry: errorRegistry } = CQRS.create<TestContext>();

            const errorQuery = errorDefine.query({
                input: z.object({ id: z.string(), shouldFail: z.boolean().optional() }),
                output: z.object({ id: z.string(), data: z.string() }),
                metadata: { trpc: {} },
                handler: async ({ input, ctx }) => {
                    if (input.shouldFail) {
                        throw new Error('Query failed');
                    }
                    return { id: input.id, data: 'test data' };
                },
            });

            const errorTestRegistry = errorRegistry({ errorQuery });
            const errorCqrs = new CQRS({
                registry: errorTestRegistry,
                getContext: () => mockContext,
            });

            const router = errorCqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // Successful execution
            const successResult = await client.errorQuery.query({ id: 'test-123' });
            expect(successResult).toEqual({ id: 'test-123', data: 'test data' });

            // Error execution
            await expect(client.errorQuery.query({
                id: 'test-123',
                shouldFail: true
            })).rejects.toThrow('Query failed');
        });

        it('should preserve error details and stack traces', async () => {
            // Create procedures that can throw errors
            const { define: errorDefine, registry: errorRegistry } = CQRS.create<TestContext>();

            const errorCommand = errorDefine.command({
                input: z.object({ shouldFail: z.boolean(), errorType: z.string().optional() }),
                output: z.object({ success: z.boolean() }),
                metadata: { trpc: {} },
                handler: async ({ input, ctx }) => {
                    if (input.shouldFail) {
                        switch (input.errorType) {
                            case 'validation':
                                throw new Error('Validation failed');
                            default:
                                throw new Error('Generic error');
                        }
                    }
                    return { success: true };
                },
            });

            const errorTestRegistry = errorRegistry({ errorCommand });
            const errorCqrs = new CQRS({
                registry: errorTestRegistry,
                getContext: () => mockContext,
            });

            const router = errorCqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            try {
                await client.errorCommand.mutate({ shouldFail: true, errorType: 'validation' });
                fail('Expected error to be thrown');
            } catch (error: any) {
                expect(error.message).toContain('Validation failed');
                expect(error).toBeInstanceOf(Error);
                // tRPC should preserve the original error structure
            }
        });
    });

    describe('Context Handling', () => {
        it('should pass context correctly to handlers', async () => {
            let capturedContext: any = null;

            const { define: contextDefine, registry: contextRegistry } = CQRS.create<TestContext>();

            const contextCommand = contextDefine.command({
                input: z.object({ message: z.string() }),
                output: z.object({ userId: z.string(), message: z.string() }),
                metadata: { trpc: {} },
                handler: async ({ input, ctx }) => {
                    capturedContext = ctx;
                    return { userId: ctx.userId, message: input.message };
                },
            });

            const contextTestRegistry = contextRegistry({ contextCommand });
            const contextCqrs = new CQRS({
                registry: contextTestRegistry,
                getContext: () => ({ ...mockContext, userId: 'context-test-user' }),
            });

            const router = contextCqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => ({ ...mockContext, userId: 'context-test-user' }),
                })],
            });

            const result = await client.contextCommand.mutate({ message: 'test message' });

            expect(result).toEqual({
                userId: 'context-test-user',
                message: 'test message'
            });
            expect(capturedContext).toMatchObject({
                traceId: expect.any(String),
                userId: 'context-test-user',
                userService: expect.any(Object)
            });
        });

        it('should handle dynamic context creation', async () => {
            let contextCallCount = 0;

            const { define: dynamicDefine, registry: dynamicRegistry } = CQRS.create<{ requestId: string; userId: string }>();

            const dynamicQuery = dynamicDefine.query({
                input: z.object({ id: z.string() }),
                output: z.object({ requestId: z.string(), userId: z.string(), input: z.object({ id: z.string() }) }),
                metadata: { trpc: {} },
                handler: async ({ input, ctx }) => {
                    return {
                        requestId: ctx.requestId,
                        userId: ctx.userId,
                        input
                    };
                },
            });

            const dynamicTestRegistry = dynamicRegistry({ dynamicQuery });
            const dynamicCqrs = new CQRS({
                registry: dynamicTestRegistry,
                getContext: () => ({ requestId: `req-${++contextCallCount}`, userId: 'dynamic-user' }),
            });

            const router = dynamicCqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => ({
                        requestId: `req-${++contextCallCount}`,
                        userId: 'dynamic-user'
                    }),
                })],
            });

            // Make multiple calls to verify context is created fresh each time
            const result1 = await client.dynamicQuery.query({ id: 'test1' });
            const result2 = await client.dynamicQuery.query({ id: 'test2' });

            expect(result1.requestId).toMatch(/^req-\d+$/);
            expect(result2.requestId).toMatch(/^req-\d+$/);
            expect(result1.requestId).not.toBe(result2.requestId);
            expect(result1.userId).toBe('dynamic-user');
            expect(result2.userId).toBe('dynamic-user');
        });
    });

    describe('End-to-End Functionality', () => {
        it('should work with complex nested data structures', async () => {
            const { define: complexDefine, registry: complexRegistry } = CQRS.create<TestContext>();

            const complexCommand = complexDefine.command({
                input: z.object({
                    user: z.object({
                        profile: z.object({
                            name: z.string(),
                            email: z.string().email(),
                            age: z.number().min(0).max(150),
                            preferences: z.object({
                                theme: z.enum(['light', 'dark']),
                                notifications: z.boolean(),
                                tags: z.array(z.string())
                            })
                        }),
                        metadata: z.record(z.string(), z.any()).optional()
                    })
                }),
                metadata: { trpc: {} },
                output: z.object({
                    id: z.string(),
                    profile: z.object({
                        name: z.string(),
                        email: z.string(),
                        age: z.number(),
                        preferences: z.object({
                            theme: z.string(),
                            notifications: z.boolean(),
                            tags: z.array(z.string())
                        })
                    }),
                    createdAt: z.string()
                }),
                handler: async ({ input, ctx }) => {
                    return {
                        id: 'complex-user-123',
                        profile: input.user.profile,
                        createdAt: new Date().toISOString()
                    };
                },
            });

            const complexTestRegistry = complexRegistry({ complexCommand });
            const complexCqrs = new CQRS({
                registry: complexTestRegistry,
                getContext: () => mockContext,
            });

            const router = complexCqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            const complexInput = {
                user: {
                    profile: {
                        name: 'John Complex',
                        email: '<EMAIL>',
                        age: 30,
                        preferences: {
                            theme: 'dark' as const,
                            notifications: true,
                            tags: ['developer', 'typescript', 'testing']
                        }
                    },
                    metadata: {
                        source: 'api',
                        version: '1.0'
                    }
                }
            };

            const result = await client.complexCommand.mutate(complexInput);

            expect(result).toMatchObject({
                id: 'complex-user-123',
                profile: {
                    name: 'John Complex',
                    email: '<EMAIL>',
                    age: 30,
                    preferences: {
                        theme: 'dark',
                        notifications: true,
                        tags: ['developer', 'typescript', 'testing']
                    }
                },
                createdAt: expect.stringMatching(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
            });
        });

        it('should handle concurrent requests properly', async () => {
            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // Make multiple concurrent requests
            const promises = Array.from({ length: 10 }, (_, i) =>
                client.createUser.mutate({
                    name: `User ${i}`,
                    email: `user${i}@example.com`
                })
            );

            const results = await Promise.all(promises);

            // All requests should succeed
            expect(results).toHaveLength(10);
            results.forEach((result, i) => {
                expect(result).toEqual({
                    id: 'user-123',
                    name: `User ${i}`
                });
            });
        });

        it('should maintain type safety throughout the entire call chain', async () => {
            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // Type inference should work end-to-end
            const createResult = await client.createUser.mutate({
                name: 'Type Safe User',
                email: '<EMAIL>'
            });

            // TypeScript should infer the correct return type
            expectTypeOf(createResult).toEqualTypeOf<{ id: string; name: string }>();

            const queryResult = await client.getUser.query({ id: 'user-123' });
            expectTypeOf(queryResult).toEqualTypeOf<{ id: string; name: string; email: string }>();

            const nullableResult = await client.getUserByEmail.query({ email: '<EMAIL>' });
            expectTypeOf(nullableResult).toEqualTypeOf<{ id: string; name: string; email: string } | null>();

            // Runtime values should match type expectations
            expect(typeof createResult.id).toBe('string');
            expect(typeof createResult.name).toBe('string');
            expect(typeof queryResult.id).toBe('string');
            expect(typeof queryResult.name).toBe('string');
            expect(typeof queryResult.email).toBe('string');
        });
    });

    describe('tRPC Smart Defaults and Metadata', () => {
        it('should apply default metadata to all procedures', async () => {
            const { define, registry } = CQRS.create<TestContext>({
                defaultMetadata: {
                    tags: ['default-tag'],
                    description: 'Default description',
                    trpc: {}
                }
            });

            const commandWithDefaults = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                handler: async () => ({ id: 'test-id' })
            });

            const queryWithDefaults = define.query({
                input: z.object({ id: z.string() }),
                output: z.object({ name: z.string() }),
                handler: async () => ({ name: 'test-name' })
            });

            const testRegistry = registry({
                commandWithDefaults,
                queryWithDefaults
            });

            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => mockContext,
            });

            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // Both procedures should work with tRPC because of default metadata
            const commandResult = await client.commandWithDefaults.mutate({ name: 'test' });
            const queryResult = await client.queryWithDefaults.query({ id: 'test-id' });

            expect(commandResult).toEqual({ id: 'test-id' });
            expect(queryResult).toEqual({ name: 'test-name' });
        });

        it('should allow procedures to override default metadata', async () => {
            const { define, registry } = CQRS.create<TestContext>({
                defaultMetadata: {
                    tags: ['default-tag'],
                    description: 'Default description',
                    trpc: {}
                }
            });

            const overriddenCommand = define.command({
                input: z.object({ value: z.string() }),
                output: z.object({ result: z.string() }),
                metadata: {
                    tags: ['custom-tag'], // Override default
                    description: 'Custom description', // Override default
                    title: 'Custom Title', // Add new field
                    trpc: {} // Keep tRPC enabled
                },
                handler: async ({ input }) => ({ result: input.value })
            });

            const testRegistry = registry({ overriddenCommand });
            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => mockContext,
            });

            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            const result = await client.overriddenCommand.mutate({ value: 'test-value' });
            expect(result).toEqual({ result: 'test-value' });
        });

        it('should handle mixed default and explicit tRPC metadata', async () => {
            const { define, registry } = CQRS.create<TestContext>({
                defaultMetadata: {
                    description: 'Default for all procedures',
                    trpc: {} // Default tRPC enabled
                }
            });

            const defaultTrpcCommand = define.command({
                input: z.object({ data: z.string() }),
                output: z.object({ success: z.boolean() }),
                handler: async () => ({ success: true })
            });

            const explicitTrpcQuery = define.query({
                input: z.object({ filter: z.string() }),
                output: z.object({ items: z.array(z.string()) }),
                metadata: {
                    title: 'Explicit Query',
                    trpc: {} // Explicitly enable tRPC
                },
                handler: async () => ({ items: ['item1', 'item2'] })
            });

            const disabledTrpcQuery = define.query({
                input: z.object({ search: z.string() }),
                output: z.object({ count: z.number() }),
                metadata: {
                    trpc: null // Explicitly disable tRPC
                },
                handler: async () => ({ count: 5 })
            });

            const testRegistry = registry({
                defaultTrpcCommand,
                explicitTrpcQuery,
                disabledTrpcQuery
            });

            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => mockContext,
            });

            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // Default and explicit tRPC should work
            await expect(client.defaultTrpcCommand.mutate({ data: 'test' }))
                .resolves.toEqual({ success: true });
            await expect(client.explicitTrpcQuery.query({ filter: 'test' }))
                .resolves.toEqual({ items: ['item1', 'item2'] });

            // Disabled tRPC should not be available
            await expect(client.disabledTrpcQuery.query({ search: 'test' }))
                .rejects.toThrow(/No "query"-procedure on path/);
        });

        it('should preserve procedure types with default metadata', async () => {
            const { define, registry } = CQRS.create<TestContext>({
                defaultMetadata: { trpc: {} }
            });

            const smartCommand = define.command({
                input: z.object({ action: z.string() }),
                output: z.object({ executed: z.boolean() }),
                handler: async () => ({ executed: true })
            });

            const smartQuery = define.query({
                input: z.object({ lookup: z.string() }),
                output: z.object({ found: z.boolean() }),
                handler: async () => ({ found: true })
            });

            const testRegistry = registry({ smartCommand, smartQuery });
            const cqrs = new CQRS({ registry: testRegistry, getContext: () => mockContext });
            const router = cqrs.getTRPCRouter();

            // Verify procedure types are preserved
            expect(router.smartCommand._def.type).toBe('mutation');
            expect(router.smartQuery._def.type).toBe('query');

            // Verify they work correctly
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            await expect(client.smartCommand.mutate({ action: 'test' }))
                .resolves.toEqual({ executed: true });
            await expect(client.smartQuery.query({ lookup: 'test' }))
                .resolves.toEqual({ found: true });
        });

        it('should handle metadata inheritance in complex scenarios', async () => {
            const { define, registry } = CQRS.create<TestContext>({
                defaultMetadata: {
                    tags: ['service-tag'],
                    deprecated: false,
                    trpc: {}
                }
            });

            const partialOverrideCommand = define.command({
                input: z.object({ partial: z.string() }),
                output: z.object({ result: z.string() }),
                metadata: {
                    title: 'Partial Override',
                    // tags and deprecated should inherit from defaults
                    // trpc should inherit from defaults
                },
                handler: async ({ input }) => ({ result: `processed-${input.partial}` })
            });

            const fullOverrideQuery = define.query({
                input: z.object({ full: z.string() }),
                output: z.object({ data: z.string() }),
                metadata: {
                    title: 'Full Override',
                    tags: ['custom-tag'], // Override default
                    deprecated: true, // Override default
                    description: 'Fully customized query',
                    trpc: {} // Keep tRPC
                },
                handler: async ({ input }) => ({ data: input.full })
            });

            const testRegistry = registry({
                partialOverrideCommand,
                fullOverrideQuery
            });

            const cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => mockContext,
            });

            const router = cqrs.getTRPCRouter();
            const client = createTRPCClient<typeof router>({
                links: [experimental_localLink({
                    router,
                    createContext: async () => mockContext,
                })],
            });

            // Both should work due to tRPC inheritance/configuration
            await expect(client.partialOverrideCommand.mutate({ partial: 'test' }))
                .resolves.toEqual({ result: 'processed-test' });
            await expect(client.fullOverrideQuery.query({ full: 'data' }))
                .resolves.toEqual({ data: 'data' });
        });
    });
});
