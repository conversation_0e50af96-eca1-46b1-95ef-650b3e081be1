#!/usr/bin/env tsx

import { z } from 'zod';
import { CQRS } from '../cqrs';

type AppContext = {
    userId: string;
};

type User = {
    id: string;
    name: string;
    email: string;
};

// Simple in-memory store for testing
const userStore: Map<string, User> = new Map();

async function createMCPTestServer(port: number) {
    const { define, registry } = CQRS.create<AppContext>();
    const { command, query } = define;

    const createUser = command({
        input: z.object({ 
            name: z.string().min(1, "Name is required"), 
            email: z.string().email("Valid email is required"), 
            role: z.enum(['admin', 'user']).default('user') 
        }),
        output: z.object({ 
            id: z.string(), 
            name: z.string(), 
            email: z.string() 
        }),
        metadata: {
            title: 'Create User',
            mcp: {
                streamingHint: false,
            },
        },
        handler: async ({ input }) => {
            const user: User = { 
                id: `user_${Date.now()}`, 
                name: input.name, 
                email: input.email 
            };
            
            // Store in memory
            userStore.set(user.id, user);
            
            return user;
        },
    });

    const getUser = query({
        input: z.object({
            id: z.string().min(1, "User ID is required"),
        }),
        output: z.object({ 
            id: z.string(), 
            name: z.string(), 
            email: z.string() 
        }),
        metadata: {
            title: 'Get User',
            mcp: {
                readOnlyHint: true,
            },
        },
        handler: async ({ input }) => {
            const user = userStore.get(input.id);
            if (!user) {
                throw new Error(`User with ID ${input.id} not found`);
            }
            return user;
        },
    });

    const appRegistry = registry({ createUser, getUser });

    const cqrs = new CQRS({
        registry: appRegistry,
        metadata: {
            serviceName: 'test-service',
            serviceVersion: '0.0.1'
        },
        getContext: () => ({ userId: 'tester' }),
    });

    // Start CQRS with MCP (HTTP Stream transport)
    const server = await cqrs.run({
        mcp: {
            adapter: 'fastmcp',
            config: {
                transportType: 'httpStream',
                httpStream: {
                    port,
                    endpoint: '/mcp',
                },
            },
        },
    });

    return server;
}

async function main() {
    const randomPort = Math.floor(Math.random() * 3200) + 7000;
    const port = parseInt(process.argv[2] || randomPort.toString());
    
    console.log(`Starting MCP test server on port ${port}...`);
    
    try {
        const server = await createMCPTestServer(port);
        
        console.log(`MCP test server started successfully on port ${port}`);
        
        // Handle graceful shutdown
        process.on('SIGTERM', async () => {
            console.log('Received SIGTERM, shutting down gracefully...');
            await server.stop();
            process.exit(0);
        });
        
        process.on('SIGINT', async () => {
            console.log('Received SIGINT, shutting down gracefully...');
            await server.stop();
            process.exit(0);
        });
        
        // Keep the process alive
        process.on('exit', async () => {
            await server.stop();
        });
        
    } catch (error) {
        console.error('Failed to start MCP test server:', error);
        process.exit(1);
    }
}

// Only run main if this file is executed directly
if (require.main === module) {
    main().catch(console.error);
}

export { createMCPTestServer }; 