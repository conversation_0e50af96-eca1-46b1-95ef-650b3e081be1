import { z } from 'zod';
import { CQRSInternal as CQRS } from '../cqrs';
import { FastifyServerAdapter } from '../../adapters/fastify';

describe('REST Context Data', () => {
    it('should pass REST headers to getContext function', async () => {
        // Arrange: Create a simple command that captures context
        const { define, registry } = CQRS.create<{ 
            userId: string; 
            headers: Record<string, string>;
        }>();

        let capturedContext: any = null;

        const testCommand = define.command({
            input: z.object({ message: z.string() }),
            output: z.object({ success: z.boolean() }),
            metadata: {
                rest: {
                    method: 'POST',
                    path: '/test'
                }
            },
            handler: async ({ input, ctx }) => {
                capturedContext = ctx;
                return { success: true };
            }
        });

        const testRegistry = registry({ testCommand });

        const cqrs = new CQRS({
            registry: testRegistry,
            getContext: ({ traceId, procedureName, rest }) => {
                return {
                    userId: 'test-user',
                    headers: rest?.headers || {}
                };
            }
        });

        // Act: Execute the command directly (this should work without REST adapter)
        await cqrs.commands.testCommand({ message: 'hello' });

        // Assert: Context should be created even without REST data
        expect(capturedContext).toMatchObject({
            userId: 'test-user',
            headers: {},
            traceId: expect.any(String),
            startTime: expect.any(Number)
        });
    });

    it('should pass REST headers when called through REST adapter', async () => {
        // Arrange: Create a command that captures context with headers
        const { define, registry } = CQRS.create<{ 
            userId: string; 
            userAgent: string;
            authorization: string;
        }>();

        let capturedContext: any = null;

        const testCommand = define.command({
            input: z.object({ message: z.string() }),
            output: z.object({ success: z.boolean() }),
            metadata: {
                rest: {
                    method: 'POST',
                    path: '/test-headers'
                }
            },
            handler: async ({ input, ctx }) => {
                capturedContext = ctx;
                return { success: true };
            }
        });

        const testRegistry = registry({ testCommand });

        const cqrs = new CQRS({
            registry: testRegistry,
            getContext: ({ traceId, procedureName, rest }) => {
                return {
                    userId: 'test-user',
                    userAgent: rest?.headers?.['user-agent'] || 'unknown',
                    authorization: rest?.headers?.['authorization'] || 'none'
                };
            }
        });

        // Create and configure the adapter
        const adapter = new FastifyServerAdapter({ cqrs });
        const port = 3000 + Math.floor(Math.random() * 1000);

        await adapter.configure({ port, protocols: { rest: { enabled: true, prefix: '/api' } } });
        await adapter.registerRoutes(cqrs.getRouteDefinitions());
        await adapter.start();

        try {
            // Act: Make a real HTTP request with headers
            const response = await fetch(`http://localhost:${port}/api/test-headers`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'test-browser/1.0',
                    'Authorization': 'Bearer test-token'
                },
                body: JSON.stringify({ message: 'hello from REST' })
            });

            expect(response.status).toBe(200);
            const result = await response.json();
            expect(result.success).toBe(true);

            // Assert: Context should include the headers from the REST request
            expect(capturedContext).toMatchObject({
                userId: 'test-user',
                userAgent: 'test-browser/1.0',
                authorization: 'Bearer test-token',
                traceId: expect.any(String),
                startTime: expect.any(Number)
            });
        } finally {
            await adapter.stop();
        }
    });
});
