import {
    runInContext,
    run,
    getCurrentContext,
    addToCurrentContext,
    generateTraceId,
    BaseExecutionContext
} from '../context';

const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;

describe('Context Management', () => {
    describe('generateTraceId', () => {
        it('should generate a valid UUID v4', () => {
            const traceId = generateTraceId();
            expect(traceId).toMatch(uuidPattern);
        });

        it('should generate unique trace IDs', () => {
            const id1 = generateTraceId();
            const id2 = generateTraceId();
            expect(id1).not.toBe(id2);
        });
    });

    describe('runInContext', () => {
        it('should create a new context with generated trace ID', async () => {
            let capturedContext: BaseExecutionContext | undefined;

            await runInContext({}, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext).toBeDefined();
            expect(capturedContext!.traceId).toMatch(uuidPattern);
            expect(capturedContext!.startTime).toBeGreaterThan(0);
            expect(capturedContext!.parentId).toBeUndefined();
        });

        it('should use explicit trace ID when provided', async () => {
            let capturedContext: BaseExecutionContext | undefined;
            const explicitTraceId = 'explicit-trace-123';

            await runInContext({ traceId: explicitTraceId }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.traceId).toBe(explicitTraceId);
        });

        it('should use explicit parent ID when provided', async () => {
            let capturedContext: BaseExecutionContext | undefined;
            const explicitParentId = 'parent-123';

            await runInContext({ parentId: explicitParentId }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.parentId).toBe(explicitParentId);
        });

        it('should inherit trace ID from parent context when inheritTraceId is true', async () => {
            const parentTraceId = 'parent-trace-456';
            let childContext: BaseExecutionContext | undefined;

            await runInContext({ traceId: parentTraceId }, async () => {
                await runInContext({ inheritTraceId: true }, async (context) => {
                    childContext = context;
                });
            });

            expect(childContext!.traceId).toBe(parentTraceId);
        });

        it('should generate new trace ID when inheritTraceId is false', async () => {
            const parentTraceId = 'parent-trace-789';
            let childContext: BaseExecutionContext | undefined;

            await runInContext({ traceId: parentTraceId }, async () => {
                await runInContext({ inheritTraceId: false }, async (context) => {
                    childContext = context;
                });
            });

            expect(childContext!.traceId).not.toBe(parentTraceId);
            expect(childContext!.traceId).toMatch(uuidPattern);
            expect(childContext!.parentId).toBe(parentTraceId);
        });

        it('should handle custom context as object', async () => {
            type CustomContext = { userId: string; requestId: string };
            let capturedContext: BaseExecutionContext & CustomContext | undefined;

            const customData: CustomContext = {
                userId: 'user-123',
                requestId: 'req-456'
            };

            await runInContext({ initialContext: customData }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.userId).toBe('user-123');
            expect(capturedContext!.requestId).toBe('req-456');
            expect(capturedContext!.traceId).toMatch(uuidPattern);
        });

        it('should handle custom context as function', async () => {
            type CustomContext = { computedValue: string };
            let capturedContext: BaseExecutionContext & CustomContext | undefined;

            await runInContext({
                traceId: 'test-trace-123',
                initialContext: ({ traceId, parentId }) => ({
                    computedValue: `computed-${traceId}-${parentId || 'none'}`
                })
            }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.computedValue).toBe('computed-test-trace-123-none');
            expect(capturedContext!.traceId).toBe('test-trace-123');
        });

        it('should prioritize base properties over custom context', async () => {
            type CustomContext = { traceId: string; customProp: string };
            let capturedContext: BaseExecutionContext & CustomContext | undefined;

            const customData: CustomContext = {
                traceId: 'custom-trace-id', // This should be overridden
                customProp: 'custom-value'
            };

            await runInContext({
                traceId: 'explicit-trace-id',
                initialContext: customData
            }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.traceId).toBe('explicit-trace-id'); // Base property wins
            expect(capturedContext!.customProp).toBe('custom-value'); // Custom property preserved
        });

        it('should handle nested contexts with different inheritance settings', async () => {
            const contexts: Array<{ level: number; traceId: string; parentId?: string }> = [];

            await runInContext({ traceId: 'root-trace' }, async (rootContext) => {
                contexts.push({ level: 1, traceId: rootContext.traceId, parentId: rootContext.parentId });

                // Child inherits trace ID
                await runInContext({ inheritTraceId: true }, async (childContext) => {
                    contexts.push({ level: 2, traceId: childContext.traceId, parentId: childContext.parentId });

                    // Grandchild creates new trace
                    await runInContext({ inheritTraceId: false }, async (grandchildContext) => {
                        contexts.push({ level: 3, traceId: grandchildContext.traceId, parentId: grandchildContext.parentId });
                    });
                });
            });

            expect(contexts[0].traceId).toBe('root-trace');
            expect(contexts[0].parentId).toBeUndefined();

            expect(contexts[1].traceId).toBe('root-trace'); // Inherited
            expect(contexts[1].parentId).toBeUndefined(); // Inherited from parent

            expect(contexts[2].traceId).not.toBe('root-trace'); // New trace
            expect(contexts[2].parentId).toBe('root-trace'); // Parent is root
        });

        it('should handle async operations correctly', async () => {
            let capturedContext: BaseExecutionContext | undefined;

            await runInContext({ traceId: 'async-test' }, async (context) => {
                // Simulate async work
                await new Promise(resolve => setTimeout(resolve, 10));

                // Context should still be available after async operation
                capturedContext = getCurrentContext();
            });

            expect(capturedContext!.traceId).toBe('async-test');
        });
    });

    describe('run', () => {
        it('should create context with default inheritance behavior', async () => {
            let capturedContext: BaseExecutionContext | undefined;

            await run(undefined, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.traceId).toMatch(uuidPattern);
            expect(capturedContext!.parentId).toBeUndefined();
        });

        it('should accept custom context as object', async () => {
            type CustomContext = { service: string };
            let capturedContext: BaseExecutionContext & CustomContext | undefined;

            await run({ service: 'user-service' }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.service).toBe('user-service');
            expect(capturedContext!.traceId).toMatch(uuidPattern);
        });

        it('should accept custom context as function', async () => {
            type CustomContext = { parentInfo: string };
            let capturedContext: BaseExecutionContext & CustomContext | undefined;

            await run(undefined, async (outerContext) => {
                await run((parentContext) => ({
                    parentInfo: parentContext ? `parent-${parentContext.traceId}` : 'no-parent'
                }), async (context) => {
                    capturedContext = context;
                });
            });

            expect(capturedContext!.parentInfo).toMatch(/^parent-[0-9a-f-]+$/);
        });

        it('should inherit trace ID from parent context', async () => {
            const contexts: string[] = [];

            await run(undefined, async (outerContext) => {
                contexts.push(outerContext.traceId);

                await run(undefined, async (innerContext) => {
                    contexts.push(innerContext.traceId);
                });
            });

            expect(contexts[0]).toMatch(uuidPattern);
            expect(contexts[1]).toBe(contexts[0]); // Should inherit
        });
    });

    describe('getCurrentContext', () => {
        it('should return undefined when no context is active', () => {
            const context = getCurrentContext();
            expect(context).toBeUndefined();
        });

        it('should return current context when active', async () => {
            let capturedFromInside: BaseExecutionContext | undefined;
            let capturedFromOutside: BaseExecutionContext | undefined;

            await runInContext({ traceId: 'current-test' }, async (context) => {
                capturedFromInside = getCurrentContext();
            });

            capturedFromOutside = getCurrentContext();

            expect(capturedFromInside!.traceId).toBe('current-test');
            expect(capturedFromOutside).toBeUndefined();
        });

        it('should return correct context in nested scenarios', async () => {
            const capturedContexts: Array<BaseExecutionContext | undefined> = [];

            await runInContext({ traceId: 'outer-context' }, async () => {
                capturedContexts.push(getCurrentContext());

                await runInContext({ traceId: 'inner-context' }, async () => {
                    capturedContexts.push(getCurrentContext());
                });

                capturedContexts.push(getCurrentContext());
            });

            expect(capturedContexts[0]!.traceId).toBe('outer-context');
            expect(capturedContexts[1]!.traceId).toBe('inner-context');
            expect(capturedContexts[2]!.traceId).toBe('outer-context'); // Back to outer
        });

        it('should handle custom context types', async () => {
            type CustomContext = { userId: string };
            let capturedContext: BaseExecutionContext & CustomContext | undefined;

            await runInContext({
                initialContext: { userId: 'test-user' }
            }, async () => {
                capturedContext = getCurrentContext<CustomContext>();
            });

            expect(capturedContext!.userId).toBe('test-user');
        });
    });

    describe('addToCurrentContext', () => {
        it('should do nothing when no context is active', () => {
            // Should not throw
            addToCurrentContext({ newProp: 'value' });

            const context = getCurrentContext();
            expect(context).toBeUndefined();
        });

        it('should add data to current context', async () => {
            let capturedContext: any;

            await runInContext({}, async () => {
                addToCurrentContext({
                    userId: 'added-user',
                    requestId: 'added-request'
                });

                capturedContext = getCurrentContext();
            });

            expect(capturedContext.userId).toBe('added-user');
            expect(capturedContext.requestId).toBe('added-request');
            expect(capturedContext.traceId).toMatch(uuidPattern);
        });

        it('should overwrite existing properties', async () => {
            let capturedContext: any;

            await runInContext({
                initialContext: { userId: 'original-user', service: 'original-service' }
            }, async () => {
                addToCurrentContext({
                    userId: 'updated-user',
                    newProp: 'new-value'
                });

                capturedContext = getCurrentContext();
            });

            expect(capturedContext.userId).toBe('updated-user'); // Overwritten
            expect(capturedContext.service).toBe('original-service'); // Preserved
            expect(capturedContext.newProp).toBe('new-value'); // Added
        });

        it('should allow modifying all properties including base ones', async () => {
            let capturedContext: any;
            const originalTraceId = 'protected-trace-id';

            await runInContext({ traceId: originalTraceId }, async () => {
                const originalStartTime = getCurrentContext()!.startTime;

                addToCurrentContext({
                    traceId: 'modified-trace-id',
                    startTime: 999999,
                    customProp: 'added'
                });

                capturedContext = getCurrentContext();

                // addToCurrentContext modifies all properties, including base ones
                expect(capturedContext.traceId).toBe('modified-trace-id');
                expect(capturedContext.startTime).toBe(999999);
                expect(capturedContext.customProp).toBe('added');
            });
        });

        it('should work with nested contexts', async () => {
            const capturedContexts: any[] = [];

            await runInContext({ traceId: 'outer' }, async () => {
                addToCurrentContext({ level: 'outer' });
                capturedContexts.push({ ...getCurrentContext() });

                await runInContext({ traceId: 'inner' }, async () => {
                    addToCurrentContext({ level: 'inner' });
                    capturedContexts.push({ ...getCurrentContext() });
                });

                capturedContexts.push({ ...getCurrentContext() });
            });

            expect(capturedContexts[0].level).toBe('outer');
            expect(capturedContexts[0].traceId).toBe('outer');

            expect(capturedContexts[1].level).toBe('inner');
            expect(capturedContexts[1].traceId).toBe('inner');

            expect(capturedContexts[2].level).toBe('outer'); // Back to outer context
            expect(capturedContexts[2].traceId).toBe('outer');
        });
    });

    describe('Edge Cases and Error Handling', () => {
        it('should handle empty custom context gracefully', async () => {
            let capturedContext: BaseExecutionContext | undefined;

            await runInContext({ initialContext: {} }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.traceId).toMatch(uuidPattern);
            expect(capturedContext!.startTime).toBeGreaterThan(0);
        });

        it('should handle null/undefined custom context', async () => {
            let capturedContext: BaseExecutionContext | undefined;

            await runInContext({ initialContext: undefined }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.traceId).toMatch(uuidPattern);
        });

        it('should handle function that returns empty object', async () => {
            let capturedContext: BaseExecutionContext | undefined;

            await runInContext({
                initialContext: () => ({})
            }, async (context) => {
                capturedContext = context;
            });

            expect(capturedContext!.traceId).toMatch(uuidPattern);
        });

        it('should handle concurrent contexts correctly', async () => {
            const results: Array<{ traceId: string; order: number }> = [];

            const promises = Array.from({ length: 5 }, (_, i) =>
                runInContext({ traceId: `concurrent-${i}` }, async (context) => {
                    // Simulate some async work with random delays
                    await new Promise(resolve => setTimeout(resolve, Math.random() * 50));

                    const currentContext = getCurrentContext();
                    results.push({
                        traceId: currentContext!.traceId,
                        order: i
                    });
                })
            );

            await Promise.all(promises);

            // Each context should maintain its own trace ID
            results.forEach((result, index) => {
                expect(result.traceId).toBe(`concurrent-${result.order}`);
            });

            // All contexts should be unique
            const uniqueTraceIds = new Set(results.map(r => r.traceId));
            expect(uniqueTraceIds.size).toBe(5);
        });

        it('should handle deeply nested contexts', async () => {
            const maxDepth = 10;
            const contexts: string[] = [];

            const createNestedContext = async (depth: number): Promise<void> => {
                if (depth >= maxDepth) return;

                await runInContext({
                    traceId: `level-${depth}`,
                    inheritTraceId: false
                }, async (context) => {
                    contexts.push(context.traceId);
                    await createNestedContext(depth + 1);
                });
            };

            await createNestedContext(0);

            expect(contexts).toHaveLength(maxDepth);
            contexts.forEach((traceId, index) => {
                expect(traceId).toBe(`level-${index}`);
            });
        });
    });
});

describe('Complex Context Propagation', () => {
    it('should handle concurrent context isolation', async () => {
        const results: any[] = [];

        const createIsolatedTask = async (taskId: string, delay: number) => {
            await runInContext(
                {
                    traceId: `task-${taskId}`,
                    initialContext: {
                        taskId,
                        startTime: Date.now(),
                        data: `task-${taskId}-data`
                    }
                },
                async (ctx) => {
                    // Simulate async work
                    await new Promise(resolve => setTimeout(resolve, delay));

                    const currentCtx = getCurrentContext();
                    results.push({
                        taskId: ctx.taskId,
                        traceId: ctx.traceId,
                        data: ctx.data,
                        isCurrentContext: currentCtx?.taskId === taskId
                    });
                }
            );
        };

        // Run multiple concurrent tasks
        await Promise.all([
            createIsolatedTask('A', 10),
            createIsolatedTask('B', 5),
            createIsolatedTask('C', 15),
            createIsolatedTask('D', 1)
        ]);

        // All tasks should have maintained their isolated contexts
        expect(results).toHaveLength(4);

        const taskA = results.find(r => r.taskId === 'A');
        const taskB = results.find(r => r.taskId === 'B');
        const taskC = results.find(r => r.taskId === 'C');
        const taskD = results.find(r => r.taskId === 'D');

        expect(taskA).toEqual({
            taskId: 'A',
            traceId: 'task-A',
            data: 'task-A-data',
            isCurrentContext: true
        });

        expect(taskB).toEqual({
            taskId: 'B',
            traceId: 'task-B',
            data: 'task-B-data',
            isCurrentContext: true
        });

        expect(taskC).toEqual({
            taskId: 'C',
            traceId: 'task-C',
            data: 'task-C-data',
            isCurrentContext: true
        });

        expect(taskD).toEqual({
            taskId: 'D',
            traceId: 'task-D',
            data: 'task-D-data',
            isCurrentContext: true
        });
    });

    it('should handle edge cases with undefined and null values', async () => {
        await runInContext(
            {
                traceId: 'edge-case',
                initialContext: {
                    nullValue: null,
                    undefinedValue: undefined,
                    emptyString: '',
                    emptyArray: [],
                    emptyObject: {},
                    falseValue: false,
                    zeroValue: 0
                }
            },
            async (ctx) => {
                expect(ctx.nullValue).toBeNull();
                expect(ctx.undefinedValue).toBeUndefined();
                expect(ctx.emptyString).toBe('');
                expect(ctx.emptyArray).toEqual([]);
                expect(ctx.emptyObject).toEqual({});
                expect(ctx.falseValue).toBe(false);
                expect(ctx.zeroValue).toBe(0);
            }
        );
    });
});
