import { spawn, ChildProcess } from 'child_process';
import portfinder from 'portfinder';
import path from 'path';
import { promisify } from 'util';
import { Client } from "@modelcontextprotocol/sdk/client/index.js";

// Polyfill Web Crypto API for pkce-challenge in Jest environment
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
global.crypto = require('crypto').webcrypto;

jest.setTimeout(30_000); // Allow extra time for server startup

async function getMCPClient(port: number): Promise<Client> {
    let client = new Client({
        name: "integration-test",
        version: "1.0.0"
    }, {
        capabilities: {},
    });
    const baseUrl = new URL(`http://localhost:${port}/mcp`);
    try {
        const { StreamableHTTPClientTransport } = await import("@modelcontextprotocol/sdk/client/streamableHttp.js");
        const transport = new StreamableHTTPClientTransport(baseUrl);

        await client.connect(transport);
        console.log("Connected using Streamable HTTP transport");
    } catch (error) {
        console.log("Streamable HTTP connection failed, falling back to SSE transport");
        client = new Client({
            name: 'sse-client',
            version: '1.0.0',
        }, {
            capabilities: {},
        });
        const { SSEClientTransport } = await import("@modelcontextprotocol/sdk/client/sse.js");
        const sseTransport = new SSEClientTransport(baseUrl);
        await client.connect(sseTransport);
        console.log("Connected using SSE transport");
    }
    return client;
}
const getURL = (port: number) => new URL(`http://localhost:${port}/mcp`);

async function startTestServer(port: number): Promise<ChildProcess> {
    // Spawn the server using tsx
    const serverPath = path.join(__dirname, 'mcp-test-server.ts');
    const spawnedProcess = spawn('npx', ['tsx', serverPath, port.toString()], {
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env }
    });

    // Wait for the server to start
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            spawnedProcess.kill('SIGTERM');
            reject(new Error('Server startup timeout'));
        }, 10000);

        spawnedProcess.stdout?.on('data', (data) => {
            const output = data.toString();
            console.log('Server output:', output);

            if (output.includes('MCP test server started successfully')) {
                clearTimeout(timeout);
                resolve(spawnedProcess);
            }
        });

        spawnedProcess.stderr?.on('data', (data) => {
            console.error('Server error:', data.toString());
        });

        spawnedProcess.on('exit', (code) => {
            clearTimeout(timeout);
            reject(new Error(`Server exited with code ${code}`));
        });
    });
}

describe('MCP End-to-End Integration', () => {
    let serverProcess: ChildProcess | null = null;
    let port: number;
    let client: Client;

    afterAll(async () => {
        if (serverProcess) {
            serverProcess.kill('SIGTERM');
            serverProcess = null;
        }
        if (client) {
            await client.close();
        }
    });

    beforeAll(async () => {
        port = await getUniquePort();
        serverProcess = await startTestServer(port);
        client = await getMCPClient(port);
    });

    const getUniquePort = async () => {
        return await portfinder.getPortPromise({ port: 12500 });
    };

    it('should start CQRS MCP server in separate process with ES modules support', async () => {
        const response = await fetch(getURL(port), {
            method: 'GET',
        });

        expect(response).toBeDefined();
        expect(typeof response.status).toBe('number');

        expect(serverProcess).toBeTruthy();
        expect(serverProcess?.pid).toBeDefined();
    });

    it('should list MCP tools correctly', async () => {
        const result = await client.listTools().catch(e => {
            console.error(e);
            throw e;
        });
        // 5. Verify we have the expected tools
        const toolNames = result.tools.map((tool: any) => tool.name);
        expect(toolNames).toEqual(expect.arrayContaining(['createUser', 'getUser']));

        // Verify tool metadata
        const createUserTool = result.tools.find((tool: any) => tool.name === 'createUser');
        const getUserTool = result.tools.find((tool: any) => tool.name === 'getUser');

        expect(createUserTool).toBeDefined();
        expect(getUserTool).toBeDefined();

        // Verify smart defaults for command vs query
        expect(createUserTool?.annotations?.readOnlyHint).toBe(false); // Commands are not read-only
        expect(createUserTool?.annotations?.idempotentHint).toBe(false); // Commands are not idempotent by default
        expect(getUserTool?.annotations?.readOnlyHint).toBe(true); // Queries are read-only
        expect(getUserTool?.annotations?.idempotentHint).toBe(true); // Queries are idempotent
    });

    it('should execute createUser command via MCP tools', async () => {
        const result = await client.callTool({
            name: 'createUser',
            arguments: {
                name: 'John Doe',
                email: '<EMAIL>'
            }
        });

        expect(result).toBeDefined();
        expect(result.content).toBeDefined();
        expect(Array.isArray(result.content)).toBe(true);
        expect((result.content as any[]).length).toBeGreaterThan(0);

        // Verify the response contains the created user data
        const textContent = (result.content as any[]).find((c: any) => c.type === 'text');
        expect(textContent).toBeDefined();
        expect(textContent.text).toBeDefined();

        // Parse the JSON response to verify structure
        const userData = JSON.parse(textContent.text);
        expect(userData.id).toBeDefined();
        expect(userData.name).toBe('John Doe');
        expect(userData.email).toBe('<EMAIL>');
    });

    it('should execute getUser query via MCP tools', async () => {
        // First create a user to query
        const createResult = await client.callTool({
            name: 'createUser',
            arguments: {
                name: 'Jane Smith',
                email: '<EMAIL>'
            }
        });

        const createTextContent = (createResult.content as any[]).find((c: any) => c.type === 'text');
        const createdUser = JSON.parse(createTextContent.text);

        // Now query for the user
        const result = await client.callTool({
            name: 'getUser',
            arguments: {
                id: createdUser.id
            }
        });

        expect(result).toBeDefined();
        expect(result.content).toBeDefined();
        expect(Array.isArray(result.content)).toBe(true);

        const textContent = (result.content as any[]).find((c: any) => c.type === 'text');
        expect(textContent).toBeDefined();

        const userData = JSON.parse(textContent.text);
        expect(userData.id).toBe(createdUser.id);
        expect(userData.name).toBe('Jane Smith');
        expect(userData.email).toBe('<EMAIL>');
    });

    it('should handle tool execution errors gracefully', async () => {
        // Test with invalid tool name
        await expect(client.callTool({
            name: 'nonExistentTool',
            arguments: {}
        })).rejects.toThrow();
    });

    it('should handle input validation errors', async () => {
        // Test createUser with missing required fields
        await expect(client.callTool({
            name: 'createUser',
            arguments: {
                name: 'Missing Email'
                // email is required but not provided
            }
        })).rejects.toThrow();

        // Test getUser with invalid ID format
        await expect(client.callTool({
            name: 'getUser',
            arguments: {
                id: '' // empty ID should fail validation
            }
        })).rejects.toThrow();
    });
});
