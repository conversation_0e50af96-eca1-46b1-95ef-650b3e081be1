import { AdapterRuntimes, AdapterStatus, CQRSRunConfig, HealthResponse, InferProceduresFromRecord, MaybePromise, MockedRegistry, Registry, RegistryToTRPC } from "./types";

/**
 * Metadata that adapters can provide to enhance execution context.
 * This is what adapters can contribute (REST headers, tRPC info, etc.).
 */
export interface ExecutionMetadata {
  /** REST-specific metadata */
  rest?: {
    headers: Record<string, any>;
    url: string;
    method: string;
    query: Record<string, any>;
    body: any;
    ip: string;
    userAgent?: string;
  };
  
  /** tRPC-specific metadata */
  trpc?: {
    type: 'query' | 'mutation' | 'subscription';
    path: string;
    [key: string]: any;
  };

  /** WebSocket-specific metadata */
  websocket?: {
    connectionId: string;
    [key: string]: any;
  };

  /** Custom metadata from other adapters */
  [key: string]: any;
}

// ==================== CQRS SIMPLIFIED INTERFACE ====================

/**
 * The minimal CQRS interface that end users see.
 * Most complexity is hidden behind the run() method.
 */
export interface CQRSInterface<TRegistry extends Registry<any, any>, TCustomContext extends Record<string, any> = Record<string, any>> {
  queries: InferProceduresFromRecord<TRegistry['_def'], 'query', TCustomContext>;
  commands: InferProceduresFromRecord<TRegistry['_def'], 'command', TCustomContext>;
  /**
   * Start the CQRS runtime with specified adapters
   */
  run(config?: CQRSRunConfig): Promise<CQRSServer>;

  stop(): Promise<void>;

  /**
   * Get the tRPC router for use in other contexts
   */
  getTRPCRouter(): RegistryToTRPC<TRegistry['_def']>;

  enableMocking(ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)): MockedRegistry<TRegistry['_def'], TCustomContext>;
}

export interface CQRSServer {
  /**
   * Stop all running adapters
   */
  stop(): Promise<void>;

  /**
   * Get health status from all adapters
   */
  getHealth(): Promise<Record<AdapterRuntimes, HealthResponse>>;

  /**
   * Get status of all adapters
   */
  getStatus(): Promise<Record<AdapterRuntimes, { name: string; status: AdapterStatus }>>;
}
