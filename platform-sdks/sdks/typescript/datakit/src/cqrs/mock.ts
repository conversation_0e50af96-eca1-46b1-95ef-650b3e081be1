import type {
    Registry,
    RegistryDef,
    <PERSON>ckedReg<PERSON>ry,
    MockedQueryHandler,
    CommandObject,
    MaybePromise,
    DecorateRegistryRecord
} from './types';

// Mock state for a single procedure
type MockState<TInput, TOutput> = {
    calls: [TInput][];
    results: Array<{ type: 'return' | 'throw'; value: any }>;
    instances: any[];
    lastCall?: [TInput];

    // Mock implementations
    mockImplementation?: (args: { input: TInput }) => Promise<TOutput>;
    mockImplementationOnce?: Array<(args: { input: TInput }) => Promise<TOutput>>;
    mockReturnValue?: TOutput;
    mockResolvedValue?: TOutput;
    mockRejectedValue?: any;

    // Original caller for restoration
    originalCaller: (input: TInput) => Promise<TOutput>;
};

// Global mock state storage - use a unique key for each mock registry instance
const mockStates = new Map<string, Map<string, MockState<any, any>>>();

// Generate unique ID for each mock registry instance
let mockRegistryIdCounter = 0;
function generateMockRegistryId(): string {
    return `mock-registry-${++mockRegistryIdCounter}-${Date.now()}`;
}

// Get or create mock state for a procedure
function getMockState<TInput, TOutput>(
    mockRegistryId: string,
    procedureName: string,
    originalCaller: (input: TInput) => Promise<TOutput>
): MockState<TInput, TOutput> {
    let registryMocks = mockStates.get(mockRegistryId);
    if (!registryMocks) {
        registryMocks = new Map();
        mockStates.set(mockRegistryId, registryMocks);
    }

    let mockState = registryMocks.get(procedureName);
    if (!mockState) {
        mockState = {
            calls: [],
            results: [],
            instances: [],
            originalCaller,
        };
        registryMocks.set(procedureName, mockState);
    }

    return mockState as MockState<TInput, TOutput>;
}

// Create a mocked procedure that wraps the original caller
function createMockedProcedure<TInput, TOutput>(
    mockRegistryId: string,
    procedureName: string,
    originalCaller: any,
    isCommand: boolean = false
): MockedQueryHandler<TInput, TOutput> {

    const mockState = getMockState<TInput, TOutput>(mockRegistryId, procedureName, originalCaller);

    // Main execution function that handles mocking
    const executeWithMocking = async (input: TInput): Promise<TOutput> => {
        // Record the call
        mockState.calls.push([input]);
        mockState.lastCall = [input];

        try {
            let result: TOutput;

            // Determine which implementation to use
            if (mockState.mockImplementationOnce && mockState.mockImplementationOnce.length > 0) {
                // Use one-time implementation
                const onceImpl = mockState.mockImplementationOnce.shift()!;
                result = await onceImpl({ input });
            } else if (mockState.mockImplementation) {
                // Use persistent mock implementation
                result = await mockState.mockImplementation({ input });
            } else if (mockState.mockRejectedValue !== undefined) {
                // Throw mocked error
                throw mockState.mockRejectedValue;
            } else if (mockState.mockResolvedValue !== undefined) {
                // Return mocked resolved value
                result = mockState.mockResolvedValue;
            } else if (mockState.mockReturnValue !== undefined) {
                // Return mocked value (sync)
                result = mockState.mockReturnValue;
            } else {
                // Fall back to original implementation
                result = await mockState.originalCaller(input);
            }

            // Record successful result
            mockState.results.push({ type: 'return', value: result });
            return result;

        } catch (error) {
            // Record error result
            mockState.results.push({ type: 'throw', value: error });
            throw error;
        }
    };

    // Create mock methods
    const createMockMethods = <T extends MockedQueryHandler<TInput, TOutput>>(handler: T): T => {
        handler.mockReturnValue = (value: TOutput) => {
            mockState.mockReturnValue = value;
            mockState.mockResolvedValue = undefined;
            mockState.mockRejectedValue = undefined;
            mockState.mockImplementation = undefined;
            return handler;
        };

        handler.mockResolvedValue = (value: TOutput) => {
            mockState.mockResolvedValue = value;
            mockState.mockReturnValue = undefined;
            mockState.mockRejectedValue = undefined;
            mockState.mockImplementation = undefined;
            return handler;
        };

        handler.mockRejectedValue = (error: any) => {
            mockState.mockRejectedValue = error;
            mockState.mockReturnValue = undefined;
            mockState.mockResolvedValue = undefined;
            mockState.mockImplementation = undefined;
            return handler;
        };

        handler.mockImplementation = (fn: (args: { input: TInput }) => Promise<TOutput>) => {
            mockState.mockImplementation = fn;
            mockState.mockReturnValue = undefined;
            mockState.mockResolvedValue = undefined;
            mockState.mockRejectedValue = undefined;
            return handler;
        };

        handler.mockImplementationOnce = (fn: (args: { input: TInput }) => Promise<TOutput>) => {
            if (!mockState.mockImplementationOnce) {
                mockState.mockImplementationOnce = [];
            }
            mockState.mockImplementationOnce.push(fn);
            return handler;
        };

        handler.mockClear = () => {
            mockState.calls = [];
            mockState.results = [];
            mockState.instances = [];
            mockState.lastCall = undefined;
        };

        handler.mockReset = () => {
            handler.mockClear();
            mockState.mockImplementation = undefined;
            mockState.mockImplementationOnce = undefined;
            mockState.mockReturnValue = undefined;
            mockState.mockResolvedValue = undefined;
            mockState.mockRejectedValue = undefined;
        };

        handler.mockRestore = () => {
            handler.mockReset();
            // Note: Full restoration would require registry-level coordination
        };

        // Add mock property for call tracking
        Object.defineProperty(handler, 'mock', {
            get: () => ({
                calls: mockState.calls,
                results: mockState.results,
                instances: mockState.instances,
                lastCall: mockState.lastCall,
            }),
            enumerable: true,
            configurable: false,
        });

        // Add Jest spy compatibility properties
        Object.defineProperty(handler, '_isMockFunction', {
            value: true,
            writable: false,
            enumerable: false,
            configurable: false,
        });

        // Add Jest spy methods for compatibility
        (handler as any).getMockName = () => procedureName;
        (handler as any).mockName = () => {
            // Jest compatibility - doesn't affect our implementation
            return handler;
        };

        return handler;
    };

    if (isCommand) {
        // Create mocked command handler
        const commandHandler = executeWithMocking as any;

        // Add command-specific methods if they exist on the original
        if (originalCaller.handle) {
            commandHandler.handle = executeWithMocking;
        }

        if (originalCaller.make) {
            commandHandler.make = (input: TInput): CommandObject<TInput, TOutput> => {
                const commandObj = {
                    input,
                    _type: 'command' as const,
                    commandName: procedureName,
                    timestamp: Date.now(),
                    execute: () => executeWithMocking(input),
                };

                // Make properties readonly
                Object.defineProperty(commandObj, 'input', { writable: false, configurable: false });
                Object.defineProperty(commandObj, 'commandName', { writable: false, configurable: false });
                Object.defineProperty(commandObj, 'timestamp', { writable: false, configurable: false });

                return commandObj;
            };
        }

        return createMockMethods(commandHandler);
    } else {
        // Create mocked query handler
        const queryHandler = executeWithMocking as MockedQueryHandler<TInput, TOutput>;
        return createMockMethods(queryHandler);
    }
}

// Create a mocked registry
export function createMockedRegistry<TRegistryDef extends RegistryDef, TCustomContext extends Record<string, any> = Record<string, any>>(
    originalRegistry: Registry<TRegistryDef, TCustomContext>,
    ctx?: TCustomContext | (() => MaybePromise<TCustomContext>)
): MockedRegistry<TRegistryDef, TCustomContext> {
    const mockedRegistry = {} as MockedRegistry<TRegistryDef, TCustomContext>;

    // Generate unique ID for this mock registry instance
    const mockRegistryId = generateMockRegistryId();

    // Create caller from original registry to get the actual callable procedures
    const originalCaller = originalRegistry.createCaller({
        getContext: typeof ctx === 'function' ? ctx : () => ctx || ({} as any),
    });

    // Wrap each procedure with mocking functionality
    for (const [procedureName, procedure] of Object.entries(originalRegistry._def)) {
        const originalProcedureCaller = (originalCaller as any)[procedureName];
        const isCommand = procedure._type === 'command';

        const mockedProcedure = createMockedProcedure(
            mockRegistryId,
            procedureName,
            originalProcedureCaller,
            isCommand
        );

        (mockedRegistry as any)[procedureName] = mockedProcedure;
    }

    // Copy all registry properties and methods
    mockedRegistry._def = originalRegistry._def;
    mockedRegistry._ctx = originalRegistry._ctx;
    mockedRegistry._trpc = originalRegistry._trpc;

    // Copy registry methods
    mockedRegistry.getRouteDefinitions = originalRegistry.getRouteDefinitions.bind(originalRegistry);
    mockedRegistry.getCommandNames = originalRegistry.getCommandNames.bind(originalRegistry);
    mockedRegistry.getQueryNames = originalRegistry.getQueryNames.bind(originalRegistry);
    mockedRegistry.getAllProcedureNames = originalRegistry.getAllProcedureNames.bind(originalRegistry);
    mockedRegistry.getProcedureMetadata = originalRegistry.getProcedureMetadata.bind(originalRegistry);

    // Create a mocked createCaller that returns mocked procedures
    mockedRegistry.createCaller = () => {
        // Return the mocked procedures as the caller result
        const result = {} as DecorateRegistryRecord<TRegistryDef, TCustomContext>;
        for (const [procedureName] of Object.entries(originalRegistry._def)) {
            (result as any)[procedureName] = (mockedRegistry as any)[procedureName];
        }
        return result;
    };

    // Add utility methods
    mockedRegistry.spyOn = <K extends Extract<keyof TRegistryDef, string>>(procedureName: K) => {
        const mockedProcedure = (mockedRegistry as any)[procedureName];
        if (!mockedProcedure) {
            throw new Error(`Procedure '${String(procedureName)}' not found in registry`);
        }
        return mockedProcedure;
    };

    mockedRegistry.getCalls = <K extends Extract<keyof TRegistryDef, string>>(procedureName: K) => {
        const mockedProcedure = (mockedRegistry as any)[procedureName];
        if (!mockedProcedure || !mockedProcedure.mock) {
            throw new Error(`Procedure '${String(procedureName)}' not found or not mocked`);
        }
        return mockedProcedure.mock;
    };

    mockedRegistry.clearAllMocks = () => {
        const registryMocks = mockStates.get(mockRegistryId);
        if (registryMocks) {
            for (const mockState of registryMocks.values()) {
                mockState.calls = [];
                mockState.results = [];
                mockState.instances = [];
                mockState.lastCall = undefined;
            }
        }
    };

    mockedRegistry.resetAllMocks = () => {
        const registryMocks = mockStates.get(mockRegistryId);
        if (registryMocks) {
            for (const mockState of registryMocks.values()) {
                mockState.calls = [];
                mockState.results = [];
                mockState.instances = [];
                mockState.lastCall = undefined;
                mockState.mockImplementation = undefined;
                mockState.mockImplementationOnce = undefined;
                mockState.mockReturnValue = undefined;
                mockState.mockResolvedValue = undefined;
                mockState.mockRejectedValue = undefined;
            }
        }
    };

    mockedRegistry.restoreAllMocks = () => {
        mockedRegistry.resetAllMocks();
        // Full restoration would require replacing the registry instance
    };

    mockedRegistry.getOriginalRegistry = () => originalRegistry;

    // enableMocking returns itself since it's already mocked
    mockedRegistry.enableMocking = () => {
        return mockedRegistry;
    };

    return mockedRegistry;
}
