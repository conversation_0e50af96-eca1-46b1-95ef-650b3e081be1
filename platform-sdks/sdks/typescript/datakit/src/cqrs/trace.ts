import { generateTraceId, runInContext, getCurrentContext } from './context';
import type { TraceContext, TraceInfo, CreateContext } from './types';


export { generateTraceId };

// Internal function to run code within a trace context
export const runWithTrace = <TCustomTrace extends Record<string, any> = Record<string, any>, T = any>(
    traceId: string | undefined,
    parentId: string | undefined,
    procedureName: string,
    procedureType: 'command' | 'query',
    input: unknown,
    getContext: CreateContext<TCustomTrace> | undefined,
    fn: (traceContext: TraceContext<TCustomTrace>) => Promise<T>,
    adapterContext?: {
        rest?: Record<string, any>;
        [key: string]: any;
    }
): Promise<T> => {
    return runInContext<T, TCustomTrace>(
        {
            traceId,
            parentId,
            initialContext: ({ traceId: determinedTraceId, parentId: determinedParentId }) => {
                return getContext ? getContext({
                    ...adapterContext,
                    traceId: determinedTraceId,
                    parentId: determinedParentId,
                    procedureName,
                    procedureType,
                    input,
                }) : {} as TCustomTrace
            }
        },
        (context) => fn(context as TraceContext<TCustomTrace>)
    );
};

// Internal function to get current trace context
export const getCurrentTrace = <TCustomTrace extends Record<string, any> = Record<string, any>>(): TraceContext<TCustomTrace> | undefined => {
    return getCurrentContext<TCustomTrace>() as TraceContext<TCustomTrace> | undefined;
};

// Public utility function for users to access trace info in handlers
export const getTrace = <TCustomTrace extends Record<string, any> = Record<string, any>>(): TraceInfo<TCustomTrace> | undefined => {
    const context = getCurrentContext<TCustomTrace>() as TraceContext<TCustomTrace> | undefined;
    if (!context) return undefined;

    // Return everything except internal fields
    const { startTime: _, ...traceInfo } = context;
    return traceInfo as TraceInfo<TCustomTrace>;
};

// Backward compatibility function for simple trace access
export const getTraceInfo = (): { traceId: string; parentId?: string } | undefined => {
    const context = getCurrentContext();
    if (!context) return undefined;

    return {
        traceId: context.traceId,
        parentId: context.parentId,
    };
};

// Internal function to check if we're already in a trace context
export const isInTraceContext = (): boolean => {
    return getCurrentContext() !== undefined;
};

// Internal function to run code within the existing trace context (for nested calls)
export const runWithinExistingTrace = <T = any>(fn: (traceContext: TraceContext<any>) => Promise<T>): Promise<T> => {
    const currentContext = getCurrentContext();
    if (!currentContext) {
        throw new Error('No trace context found. This function should only be called within an existing trace context.');
    }

    // Re-enter the context to ensure it propagates through any async boundaries within the function.
    return runInContext({
        traceId: currentContext.traceId,
        parentId: currentContext.parentId,
        inheritTraceId: true, // This will ensure we continue the same trace
        initialContext: currentContext, // Pass the whole context to preserve all data
    }, (context) => fn(context as TraceContext<any>));
};
