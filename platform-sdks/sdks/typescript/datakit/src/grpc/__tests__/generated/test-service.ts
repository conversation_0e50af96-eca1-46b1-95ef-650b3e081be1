// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.5
//   protoc               v6.31.1
// source: test-service.proto

/* eslint-disable */
/** Mock Classes for testing */
export class BinaryWriter {
  uint32(num: number): BinaryWriter {
    return this as any;
  }

  string(str: string): BinaryWriter {
    return this as any;
  }

  int32(num: number): BinaryWriter {
    return this as any;
  }

  finish(): Uint8Array {
    return new Uint8Array();
  }

  len: number = 0;
  pos: number = 0;
}

/** Mock Classes for testing */
export class BinaryReader {
  constructor(input: Uint8Array) {
    this.len = input.length;
  }

  uint32(): number {
    return 0 as any;
  }

  string(): string {
    return "" as any;
  }

  int32(): number {
    return 0 as any;
  }

  skip(num: number): void {
    return;
  }

  len: number = 0;
  pos: number = 0;
}


export const protobufPackage = "test";

export interface CreatePatientCommand {
  name: string;
  age: number;
}

export interface CreatePatientCommandResult {
  id: string;
  name: string;
  age: number;
}

export interface GetPatientQuery {
  id: string;
}

export interface GetPatientQueryResult {
  id: string;
  name: string;
  age: number;
}

function createBaseCreatePatientCommand(): CreatePatientCommand {
  return { name: "", age: 0 };
}

export const CreatePatientCommand: MessageFns<CreatePatientCommand> = {
  encode(message: CreatePatientCommand, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.age !== 0) {
      writer.uint32(16).int32(message.age);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreatePatientCommand {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreatePatientCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 16) {
            break;
          }

          message.age = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreatePatientCommand {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      age: isSet(object.age) ? globalThis.Number(object.age) : 0,
    };
  },

  toJSON(message: CreatePatientCommand): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.age !== 0) {
      obj.age = Math.round(message.age);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreatePatientCommand>, I>>(base?: I): CreatePatientCommand {
    return CreatePatientCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePatientCommand>, I>>(object: I): CreatePatientCommand {
    const message = createBaseCreatePatientCommand();
    message.name = object.name ?? "";
    message.age = object.age ?? 0;
    return message;
  },
};

function createBaseCreatePatientCommandResult(): CreatePatientCommandResult {
  return { id: "", name: "", age: 0 };
}

export const CreatePatientCommandResult: MessageFns<CreatePatientCommandResult> = {
  encode(message: CreatePatientCommandResult, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.age !== 0) {
      writer.uint32(24).int32(message.age);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): CreatePatientCommandResult {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreatePatientCommandResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.age = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreatePatientCommandResult {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      age: isSet(object.age) ? globalThis.Number(object.age) : 0,
    };
  },

  toJSON(message: CreatePatientCommandResult): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.age !== 0) {
      obj.age = Math.round(message.age);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreatePatientCommandResult>, I>>(base?: I): CreatePatientCommandResult {
    return CreatePatientCommandResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePatientCommandResult>, I>>(object: I): CreatePatientCommandResult {
    const message = createBaseCreatePatientCommandResult();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.age = object.age ?? 0;
    return message;
  },
};

function createBaseGetPatientQuery(): GetPatientQuery {
  return { id: "" };
}

export const GetPatientQuery: MessageFns<GetPatientQuery> = {
  encode(message: GetPatientQuery, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetPatientQuery {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPatientQuery();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPatientQuery {
    return { id: isSet(object.id) ? globalThis.String(object.id) : "" };
  },

  toJSON(message: GetPatientQuery): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPatientQuery>, I>>(base?: I): GetPatientQuery {
    return GetPatientQuery.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPatientQuery>, I>>(object: I): GetPatientQuery {
    const message = createBaseGetPatientQuery();
    message.id = object.id ?? "";
    return message;
  },
};

function createBaseGetPatientQueryResult(): GetPatientQueryResult {
  return { id: "", name: "", age: 0 };
}

export const GetPatientQueryResult: MessageFns<GetPatientQueryResult> = {
  encode(message: GetPatientQueryResult, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.age !== 0) {
      writer.uint32(24).int32(message.age);
    }
    return writer;
  },

  decode(input: BinaryReader | Uint8Array, length?: number): GetPatientQueryResult {
    const reader = input instanceof BinaryReader ? input : new BinaryReader(input);
    const end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGetPatientQueryResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1: {
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        }
        case 2: {
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        }
        case 3: {
          if (tag !== 24) {
            break;
          }

          message.age = reader.int32();
          continue;
        }
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skip(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): GetPatientQueryResult {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      age: isSet(object.age) ? globalThis.Number(object.age) : 0,
    };
  },

  toJSON(message: GetPatientQueryResult): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.age !== 0) {
      obj.age = Math.round(message.age);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<GetPatientQueryResult>, I>>(base?: I): GetPatientQueryResult {
    return GetPatientQueryResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPatientQueryResult>, I>>(object: I): GetPatientQueryResult {
    const message = createBaseGetPatientQueryResult();
    message.id = object.id ?? "";
    message.name = object.name ?? "";
    message.age = object.age ?? 0;
    return message;
  },
};

/** Patient Command/Query service */
export interface PatientService {
  CreatePatient(request: CreatePatientCommand): Promise<CreatePatientCommandResult>;
  GetPatient(request: GetPatientQuery): Promise<GetPatientQueryResult>;
}

export const PatientServiceServiceName = "test.PatientService";
export class PatientServiceClientImpl implements PatientService {
  private readonly rpc: Rpc;
  private readonly service: string;
  constructor(rpc: Rpc, opts?: { service?: string }) {
    this.service = opts?.service || PatientServiceServiceName;
    this.rpc = rpc;
    this.CreatePatient = this.CreatePatient.bind(this);
    this.GetPatient = this.GetPatient.bind(this);
  }
  CreatePatient(request: CreatePatientCommand): Promise<CreatePatientCommandResult> {
    const data = CreatePatientCommand.encode(request).finish();
    const promise = this.rpc.request(this.service, "CreatePatient", data);
    return promise.then((data) => CreatePatientCommandResult.decode(new BinaryReader(data)));
  }

  GetPatient(request: GetPatientQuery): Promise<GetPatientQueryResult> {
    const data = GetPatientQuery.encode(request).finish();
    const promise = this.rpc.request(this.service, "GetPatient", data);
    return promise.then((data) => GetPatientQueryResult.decode(new BinaryReader(data)));
  }
}

interface Rpc {
  request(service: string, method: string, data: Uint8Array): Promise<Uint8Array>;
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  encode(message: T, writer?: BinaryWriter): BinaryWriter;
  decode(input: BinaryReader | Uint8Array, length?: number): T;
  fromJSON(object: any): T;
  toJSON(message: T): unknown;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
