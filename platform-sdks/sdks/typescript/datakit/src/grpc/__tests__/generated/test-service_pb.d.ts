// package: test
// file: test-service.proto

import * as jspb from "google-protobuf";

export class CreatePatientCommand extends jspb.Message {
  getName(): string;
  setName(value: string): void;

  getAge(): number;
  setAge(value: number): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CreatePatientCommand.AsObject;
  static toObject(includeInstance: boolean, msg: CreatePatientCommand): CreatePatientCommand.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CreatePatientCommand, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CreatePatientCommand;
  static deserializeBinaryFromReader(message: CreatePatientCommand, reader: jspb.BinaryReader): CreatePatientCommand;
}

export namespace CreatePatientCommand {
  export type AsObject = {
    name: string,
    age: number,
  }
}

export class CreatePatientCommandResult extends jspb.Message {
  getId(): string;
  setId(value: string): void;

  getName(): string;
  setName(value: string): void;

  getAge(): number;
  setAge(value: number): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CreatePatientCommandResult.AsObject;
  static toObject(includeInstance: boolean, msg: CreatePatientCommandResult): CreatePatientCommandResult.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: CreatePatientCommandResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CreatePatientCommandResult;
  static deserializeBinaryFromReader(message: CreatePatientCommandResult, reader: jspb.BinaryReader): CreatePatientCommandResult;
}

export namespace CreatePatientCommandResult {
  export type AsObject = {
    id: string,
    name: string,
    age: number,
  }
}

export class GetPatientQuery extends jspb.Message {
  getId(): string;
  setId(value: string): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetPatientQuery.AsObject;
  static toObject(includeInstance: boolean, msg: GetPatientQuery): GetPatientQuery.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: GetPatientQuery, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetPatientQuery;
  static deserializeBinaryFromReader(message: GetPatientQuery, reader: jspb.BinaryReader): GetPatientQuery;
}

export namespace GetPatientQuery {
  export type AsObject = {
    id: string,
  }
}

export class GetPatientQueryResult extends jspb.Message {
  getId(): string;
  setId(value: string): void;

  getName(): string;
  setName(value: string): void;

  getAge(): number;
  setAge(value: number): void;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): GetPatientQueryResult.AsObject;
  static toObject(includeInstance: boolean, msg: GetPatientQueryResult): GetPatientQueryResult.AsObject;
  static extensions: {[key: number]: jspb.ExtensionFieldInfo<jspb.Message>};
  static extensionsBinary: {[key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message>};
  static serializeBinaryToWriter(message: GetPatientQueryResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): GetPatientQueryResult;
  static deserializeBinaryFromReader(message: GetPatientQueryResult, reader: jspb.BinaryReader): GetPatientQueryResult;
}

export namespace GetPatientQueryResult {
  export type AsObject = {
    id: string,
    name: string,
    age: number,
  }
}

