syntax = "proto3";

package test;

// Generate typescript bindings using
// protoc --plugin=../../../node_modules/.bin/protoc-gen-ts --ts_out=./generated ./test-service.proto

// Patient Command/Query service
service PatientService {
  rpc CreatePatient(CreatePatientCommand) returns (CreatePatientCommandResult);
  rpc GetPatient(GetPatientQuery) returns (GetPatientQueryResult);
}

message CreatePatientCommand {
  string name = 1;
  int32 age = 2;
}

message CreatePatientCommandResult {
  string id = 1;
  string name = 2;
  int32 age = 3;
}

message GetPatientQuery {
  string id = 1;
}

message GetPatientQueryResult {
  string id = 1;
  string name = 2;
  int32 age = 3;
}
