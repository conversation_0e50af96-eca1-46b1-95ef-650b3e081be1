import { createClient } from '../createClient';
import { expectTypeOf } from 'expect-type';
import type { DaprClient } from '@dapr/dapr';
import { CommunicationProtocolEnum, HttpMethod } from '@dapr/dapr';
import type generated from './generated/test-service_pb';
import type * as generatedTsProto from './generated/test-service';

type GeneratedTypes = typeof generated;
type GeneratedTsProto = typeof generatedTsProto;

// Shared test setup
const mockInvoke = jest.fn();
const createMockClient = (overrides: Partial<DaprClient> = {}) => ({
  invoker: { invoke: mockInvoke },
  ...overrides,
} as DaprClient);

jest.mock('@dapr/dapr', () => {
  const original = jest.requireActual('@dapr/dapr');
  return {
    ...original,
    DaprClient: jest.fn().mockImplementation(() => createMockClient()),
  };
});

describe('gRPC Client', () => {
  let client: ReturnType<typeof createClient<GeneratedTsProto>>;

  beforeEach(() => {
    jest.clearAllMocks();
    client = createClient<GeneratedTypes>({
      serviceAppId: 'test-service',
      communicationProtocol: 'grpc',
      daprClient: createMockClient(),
    });
  });

  describe('Type Safety', () => {
    describe('Command Methods', () => {
      it('should have correctly typed createPatient method', () => {
        // Method should exist and be a function
        expectTypeOf(client.commands.createPatient).toBeFunction();
        
        // Input type should match CreatePatientCommand.AsObject
        expectTypeOf(client.commands.createPatient)
          .parameter(0)
          .toMatchTypeOf<{ name: string; age: number }>();
        
        // Should accept optional second parameter for HTTP method
        expectTypeOf(client.commands.createPatient)
          .parameter(1)
          .toMatchTypeOf<{ methodName?: 'PUT' | 'POST' | 'GET' | 'DELETE' | 'PATCH' } | undefined>();
        
        // Return type should match CreatePatientCommandResult.AsObject
        expectTypeOf(client.commands.createPatient)
          .returns
          .toEqualTypeOf<Promise<{ id: string; name: string; age: number }>>();
      });
    });

    describe('Query Methods', () => {
      it('should have correctly typed getPatient method', () => {
        // Method should exist and be a function
        expectTypeOf(client.queries.getPatient).toBeFunction();
        
        // Input type should match GetPatientQuery.AsObject
        expectTypeOf(client.queries.getPatient)
          .parameter(0)
          .toMatchTypeOf<{ id: string }>();
        
        // Should accept optional second parameter for HTTP method
        expectTypeOf(client.queries.getPatient)
          .parameter(1)
          .toMatchTypeOf<{ methodName?: 'PUT' | 'POST' | 'GET' | 'DELETE' | 'PATCH' } | undefined>();
        
        // Return type should match GetPatientQueryResult.AsObject
        expectTypeOf(client.queries.getPatient)
          .returns
          .toEqualTypeOf<Promise<{ id: string; name: string; age: number }>>();
      });
    });

    describe('Method Name Conversion', () => {
      it('should convert camelCase to PascalCase for service calls', async () => {
        mockInvoke.mockResolvedValue({ id: 'test', name: 'Test', age: 30 });

        await client.commands.createPatient({ name: 'Test', age: 30 });
        expect(mockInvoke).toHaveBeenCalledWith(
          'test-service',
          'CreatePatient', // camelCase -> PascalCase
          HttpMethod.POST,
          { name: 'Test', age: 30 }
        );

        await client.queries.getPatient({ id: 'test' });
        expect(mockInvoke).toHaveBeenCalledWith(
          'test-service',
          'GetPatient', // camelCase -> PascalCase
          HttpMethod.POST,
          { id: 'test' }
        );
      });
    });
  });

  describe('Core Functionality', () => {
    it('should create client with required serviceAppId', () => {
      expect(() => createClient<GeneratedTypes>({
        serviceAppId: 'valid-service',
        communicationProtocol: 'grpc',
      })).not.toThrow();

      expect(() => createClient<GeneratedTypes>({
        serviceAppId: '',
        communicationProtocol: 'grpc',
      })).toThrow('serviceAppId is required');
    });

    it('should execute commands and queries successfully', async () => {
      const commandResult = { id: 'patient-123', name: 'John Doe', age: 30 };
      const queryResult = { id: 'patient-123', name: 'John Doe', age: 30 };
      
      mockInvoke
        .mockResolvedValueOnce(commandResult)
        .mockResolvedValueOnce(queryResult);

      const createResult = await client.commands.createPatient({ name: 'John Doe', age: 30 });
      const getResult = await client.queries.getPatient({ id: 'patient-123' });

      expect(createResult).toEqual(commandResult);
      expect(getResult).toEqual(queryResult);
      expect(mockInvoke).toHaveBeenCalledTimes(2);
    });

    it('should handle different HTTP methods', async () => {
      const input = { name: 'Test', age: 30 };
      
      await client.commands.createPatient(input, { methodName: 'PUT' });
      expect(mockInvoke).toHaveBeenLastCalledWith('test-service', 'CreatePatient', HttpMethod.PUT, input);

      await client.commands.createPatient(input, { methodName: 'PATCH' });
      expect(mockInvoke).toHaveBeenLastCalledWith('test-service', 'CreatePatient', HttpMethod.PATCH, input);

      await client.queries.getPatient({ id: 'test' }, { methodName: 'GET' });
      expect(mockInvoke).toHaveBeenLastCalledWith('test-service', 'GetPatient', HttpMethod.GET, { id: 'test' });
    });

    it('should propagate errors from Dapr client', async () => {
      const testError = new Error('Service unavailable');
      mockInvoke.mockRejectedValue(testError);

      await expect(client.commands.createPatient({ name: 'Test', age: 30 }))
        .rejects.toThrow('Service unavailable');

      await expect(client.queries.getPatient({ id: 'test' }))
        .rejects.toThrow('Service unavailable');
    });
  });

  describe('Protocol Configuration', () => {
    it.each([
      ['grpc', CommunicationProtocolEnum.GRPC],
      ['http', CommunicationProtocolEnum.HTTP],
      [undefined, CommunicationProtocolEnum.GRPC], // default
    ] as const)('should configure %s protocol correctly', (protocol, expected) => {
      createClient<GeneratedTypes>({
        serviceAppId: 'test-service',
        communicationProtocol: protocol,
        daprHost: 'localhost',
        daprPort: 3500,
      });

      expect(require('@dapr/dapr').DaprClient).toHaveBeenCalledWith({
        daprHost: 'localhost',
        daprPort: '3500',
        communicationProtocol: expected,
      });
    });

    it('should handle daprAddress URL parsing', () => {
      createClient<GeneratedTypes>({
        serviceAppId: 'test-service',
        daprAddress: 'http://custom-host:4000',
        communicationProtocol: 'http',
      });

      expect(require('@dapr/dapr').DaprClient).toHaveBeenCalledWith({
        daprHost: 'custom-host',
        daprPort: '4000',
        communicationProtocol: CommunicationProtocolEnum.HTTP,
      });
    });

    it('should fallback gracefully for invalid daprAddress', () => {
      createClient<GeneratedTypes>({
        serviceAppId: 'test-service',
        daprAddress: 'invalid-url',
        communicationProtocol: 'grpc',
      });

      expect(require('@dapr/dapr').DaprClient).toHaveBeenCalledWith({
        communicationProtocol: CommunicationProtocolEnum.GRPC,
      });
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle concurrent operations', async () => {
      mockInvoke
        .mockResolvedValueOnce({ id: 'patient-1', name: 'Patient 1', age: 30 })
        .mockResolvedValueOnce({ id: 'patient-2', name: 'Patient 2', age: 25 })
        .mockResolvedValueOnce({ id: 'patient-1', name: 'Patient 1', age: 30 });

      const [createResult1, createResult2, getResult] = await Promise.all([
        client.commands.createPatient({ name: 'Patient 1', age: 30 }),
        client.commands.createPatient({ name: 'Patient 2', age: 25 }),
        client.queries.getPatient({ id: 'patient-1' }),
      ]);

      expect(createResult1).toEqual({ id: 'patient-1', name: 'Patient 1', age: 30 });
      expect(createResult2).toEqual({ id: 'patient-2', name: 'Patient 2', age: 25 });
      expect(getResult).toEqual({ id: 'patient-1', name: 'Patient 1', age: 30 });
      expect(mockInvoke).toHaveBeenCalledTimes(3);
    });

    it('should preserve input data integrity', async () => {
      const originalInput = { name: 'Test Patient', age: 35 };
      const inputCopy = { ...originalInput };
      
      mockInvoke.mockResolvedValue({ id: 'test', name: 'Test Patient', age: 35 });
      await client.commands.createPatient(originalInput);
      
      expect(originalInput).toEqual(inputCopy);
    });
  });
});
