import type { CreateClientOptions, GetCommandHandlers, GetQueryHandlers, AllTypesBase, InvokeProcedureOptions } from './types';
import { DaprClient, CommunicationProtocolEnum, HttpMethod } from '@dapr/dapr';
import { env } from '../pubsub/env';

function toGRPCMethodName(methodName: string): string {
  // Simply converts to PascalCase for gRPC method names
  return methodName.charAt(0).toUpperCase() + methodName.slice(1);
}

function getCommunicationProtocol(options: CreateClientOptions<any>): CommunicationProtocolEnum {
  return options.communicationProtocol === 'http' ? CommunicationProtocolEnum.HTTP : CommunicationProtocolEnum.GRPC;
}

function getMethodName(options?: InvokeProcedureOptions): HttpMethod {
  switch (options?.methodName) {
    case 'PUT':
      return HttpMethod.PUT;
    case 'POST':
      return HttpMethod.POST;
    case 'GET':
      return HttpMethod.GET;
    case 'DELETE':
      return HttpMethod.DELETE;
    case 'PATCH':
      return HttpMethod.PATCH;
    default:
      return HttpMethod.POST;
  }
}

export function createClient<TProcedureTypes extends AllTypesBase>(options: CreateClientOptions<TProcedureTypes>) {
  const { serviceAppId } = options;
  const communicationProtocol = getCommunicationProtocol(options);

  if (!serviceAppId) {
    throw new Error('serviceAppId is required');
  }

  // Resolve / create Dapr client
  const client = options?.daprClient ?? (() => {
    if (options?.daprHost || options?.daprPort) {
      return new DaprClient({
        daprHost: options.daprHost ?? '0.0.0.0',
        daprPort: String(options.daprPort ?? '3500'),
        communicationProtocol,
      });
    }

    const daprAddress = options?.daprAddress ?? env.DEFAULT_DAPR_ADDRESS;
    try {
      const url = new URL(daprAddress);
      return new DaprClient({ daprHost: url.hostname, daprPort: url.port || '3500', communicationProtocol });
    } catch {
      // Fallback to default
      return new DaprClient({ communicationProtocol });
    }
  })();

  return {
    commands: new Proxy({} as GetCommandHandlers<TProcedureTypes>, {
      get: (target, prop) => {
        // Convert method name to PascalCase for gRPC
        const propString = String(prop);
        const methodName = toGRPCMethodName(propString);

        return async (input: any, options?: InvokeProcedureOptions) => {
          const result = await client.invoker.invoke(serviceAppId, methodName, getMethodName(options), input);
          return result;
        }
      }
    }),
    queries: new Proxy({} as GetQueryHandlers<TProcedureTypes>, {
      get: (target, prop) => {
        // Convert method name to PascalCase for gRPC
        const propString = String(prop);
        const methodName = toGRPCMethodName(propString);

        return async (input: any, options?: InvokeProcedureOptions) => {
          const result = await client.invoker.invoke(serviceAppId, methodName, getMethodName(options), input);
          return result;
        }
      }
    }),
  };
}
