import type { DaprClient } from '@dapr/dapr';

export type CreateClientOptions<TProcedureTypes extends AllTypesBase> = {
  daprAddress?: string; // e.g., 'http://localhost:3500'
  daprHost?: string;    // optional override when using @dapr/dapr client
  daprPort?: string | number; // optional override when using @dapr/dapr client
  daprClient?: DaprClient;
  procedures?: TProcedureTypes;
  communicationProtocol?: 'http' | 'grpc';
  serviceAppId: string;
};

export type AllTypesBase = Record<string, unknown>;

type CommandKeys<TProcedureTypes extends AllTypesBase> = Extract<keyof TProcedureTypes, `${string}Command`>;
type QueryKeys<TProcedureTypes extends AllTypesBase> = Extract<keyof TProcedureTypes, `${string}Query`>;

// This depends on using ts-proto or ts-protoc-gen (which use either `toObject` or `decode` to convert to plain objects)
type InferProcedureInput<TProcedure> = TProcedure extends { toObject: (...args: any[]) => infer T } ? T : TProcedure extends { decode: (...args: any[]) => infer T } ? T : never;
type InferProcedureOutput<TProcedureTypes extends AllTypesBase, TKey extends keyof TProcedureTypes> = `${Extract<TKey, string>}Result` extends keyof TProcedureTypes ? InferProcedureInput<TProcedureTypes[`${Extract<TKey, string>}Result`]> : never;

type ConvertCommandToHandler<TKey extends string> = TKey extends `${infer K}Command` 
  ? K extends `${infer First}${infer Rest}`
    ? `${Lowercase<First>}${Rest}`
    : never
  : never;

type ConvertQueryToHandler<TKey extends string> = TKey extends `${infer K}Query` 
  ? K extends `${infer First}${infer Rest}`
    ? `${Lowercase<First>}${Rest}`
    : never
  : never;

export type InvokeProcedureOptions = {
  /**
   * The method name to use for a REST procedure.
   * No need to specify for gRPC procedures.
   * @default 'POST'
   * */
  methodName?: 'PUT' | 'POST' | 'GET' | 'DELETE' | 'PATCH';
}

export type GetCommandHandlers<TProcedureTypes extends AllTypesBase> = {
  [K in CommandKeys<TProcedureTypes> as ConvertCommandToHandler<K>]: (input: InferProcedureInput<TProcedureTypes[K]>, options?: InvokeProcedureOptions) => Promise<InferProcedureOutput<TProcedureTypes, K>>;
};

export type GetQueryHandlers<TProcedureTypes extends AllTypesBase> = {
  [K in QueryKeys<TProcedureTypes> as ConvertQueryToHandler<K>]: (input: InferProcedureInput<TProcedureTypes[K]>, options?: InvokeProcedureOptions) => Promise<InferProcedureOutput<TProcedureTypes, K>>;
};
