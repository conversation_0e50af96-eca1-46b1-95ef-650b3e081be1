import { z } from 'zod';
import { CQRS } from '../../cqrs';

// Input/Output schemas
const CreateUserInput = z.object({
    name: z.string().min(1),
    email: z.string().email(),
    age: z.number().min(0).max(120),
});

const UserOutput = z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    age: z.number(),
    createdAt: z.string(),
});

const GetUserInput = z.object({
    id: z.string(),
});

const UpdateUserInput = z.object({
    id: z.string(),
    name: z.string().optional(),
    email: z.string().email().optional(),
    age: z.number().min(0).max(120).optional(),
});

const GetUserStatsInput = z.object({
    userId: z.string(),
});

const UserStatsOutput = z.object({
    totalPosts: z.number(),
    totalComments: z.number(),
    joinDate: z.string(),
});

const { registry: createRegistry, define } = CQRS.create();

export const createUser = define.command({
    input: CreateUserInput,
    output: UserOutput,
    // Common fields declared once at top level
    metadata: {
        title: 'Create a new user',
        description: 'Creates a new user account with the provided information',
        tags: ['users'],
        // REST-specific overrides if needed
        rest: {
            path: '/users/create',
        },
    },
    handler: async ({ input }: { input: z.infer<typeof CreateUserInput> }) => {
        // Simulate user creation
        return {
            id: Math.random().toString(36),
            ...input,
            createdAt: new Date().toISOString(),
        };
    },
});

export const getUser = define.query({
    input: GetUserInput,
    output: UserOutput,
    metadata: {
        title: 'Get user by ID',
        description: 'Retrieves a user by their unique identifier',
        tags: ['users'],
        rest: {},
    },
    handler: async ({ input }: { input: z.infer<typeof GetUserInput> }) => {
        // Simulate user retrieval
        return {
            id: input.id,
            name: 'John Doe',
            email: '<EMAIL>',
            age: 30,
            createdAt: '2023-01-01T00:00:00Z',
        };
    },
});

export const updateUser = define.command({
    input: UpdateUserInput,
    output: UserOutput,
    metadata: {
        // REST gets smart defaults: POST method, /updateUser path
        // All common fields automatically inherited
        title: 'Update user information',
        description: 'Updates an existing user with new information',
        tags: ['users'],
        rest: {},
    },
    handler: async ({ input }: { input: z.infer<typeof UpdateUserInput> }) => {
        // Simulate user update
        return {
            id: input.id,
            name: input.name || 'Updated Name',
            email: input.email || '<EMAIL>',
            age: input.age || 25,
            createdAt: '2023-01-01T00:00:00Z',
        };
    },
});

export const internalCleanup = define.command({
    input: z.object({ force: z.boolean() }),
    output: z.object({ cleaned: z.number() }),
    metadata: {
        title: 'Internal cleanup operation',
        description: 'Performs internal cleanup - not exposed via REST',
        rest: null, // Explicitly disable REST for this procedure
    },
    handler: async ({ input }: { input: { force: boolean } }) => {
        return { cleaned: input.force ? 100 : 10 };
    },
});

export const getUserStats = define.query({
    input: GetUserStatsInput,
    output: UserStatsOutput,
    metadata: {
        title: 'Get user statistics',
        description: 'Retrieves comprehensive statistics for a user',
        tags: ['users'],
        deprecated: true,
        rest: {},
    },
    // Only REST enabled, future protocols can be added easily
    handler: async ({ input }: { input: z.infer<typeof GetUserStatsInput> }) => {
        return {
            totalPosts: 42,
            totalComments: 128,
            joinDate: '2023-01-01',
        };
    },
});

// Create registry with all procedures
const registry = createRegistry({
    createUser,
    getUser,
    updateUser,
    internalCleanup,
    getUserStats,
});

// Example: Start CQRS with Swagger documentation
async function startExample() {
    const cqrs = new CQRS({ 
        registry,
        getContext: () => ({}), // Add missing getContext function
        runtimes: {
            http: {
                adapter: 'fastify',
                config: {
                    port: 3000,
                    protocols: {
                        rest: { 
                            enabled: true,
                            swagger: {
                                enabled: true,
                                prefix: '/docs',
                                title: 'User Management API',
                                version: '1.0.0',
                            },
                        },
                        trpc: { enabled: true },
                    },
                },
            },
        } 
    });

    await cqrs.run();
    
    console.log('🚀 CQRS API running on http://localhost:3000');
    console.log('📚 Swagger docs available at http://localhost:3000/docs');
}

// Export for testing
export { registry, startExample };

// Example of graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Graceful shutdown initiated...');
    // Note: In a real application, you would store the CQRS instance
    // and call cqrs.stop() here
    process.exit(0);
});

// Start if this file is run directly
if (require.main === module) {
    // Start the server
    startExample();

    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Graceful shutdown initiated...');
        process.exit(0);
    });
}

/* 
 * Alternative usage patterns (for advanced use cases):
 * 
 * // Using adapter instance for custom configuration
 * const customAdapter = new FastifyRestAdapter({ cqrs });
 * await cqrs.run({
 *     server: {
 *         adapter: customAdapter,
 *         config: { port: 3000 }
 *     }
 * });
 * 
 * // Manual adapter lifecycle (not recommended for typical usage)
 * const adapter = new FastifyRestAdapter({ cqrs });
 * await adapter.configure({ port: 3000 });
 * await adapter.registerRoutes(appRegistry.getRouteDefinitions());
 * await adapter.start();
 * // ... later
 * await adapter.stop();
 */ 