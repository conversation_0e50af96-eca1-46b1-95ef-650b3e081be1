import { z } from 'zod';
import { CQRSInternal as CQRS } from '../../../cqrs/cqrs';
import { FastifyServerAdapter } from '../fastify';

describe('Swagger Integration Tests', () => {
    let cqrs: any;
    let adapter: FastifyServerAdapter;
    let port: number;

    beforeEach(() => {
        // Use a random port to avoid conflicts
        port = 13000 + Math.floor(Math.random() * 1000);
    });

    afterEach(async () => {
        if (adapter && adapter.status === 'running') {
            await adapter.stop();
        }
        if (cqrs) {
            await cqrs.stop();
        }
    });

    describe('Zod Schema to JSON Schema Conversion', () => {
        beforeEach(() => {
            const { define, registry } = CQRS.create<{ userService: any }>();

            // Define commands and queries with various Zod schema types
            const createUser = define.command({
                input: z.object({
                    name: z.string().min(1).max(100).describe('User full name'),
                    email: z.string().email().describe('User email address'),
                    age: z.number().int().min(18).max(120).optional().describe('User age'),
                    role: z.enum(['user', 'admin', 'moderator']).default('user').describe('User role'),
                    preferences: z.object({
                        theme: z.enum(['light', 'dark']).default('light'),
                        notifications: z.boolean().default(true),
                    }).optional(),
                    tags: z.array(z.string()).optional().describe('User tags'),
                }),
                output: z.object({
                    id: z.string().uuid().describe('Unique user identifier'),
                    name: z.string().describe('User full name'),
                    email: z.string().email().describe('User email address'),
                    age: z.number().int().optional().describe('User age'),
                    role: z.enum(['user', 'admin', 'moderator']).describe('User role'),
                    createdAt: z.string().datetime().describe('Creation timestamp'),
                    preferences: z.object({
                        theme: z.enum(['light', 'dark']),
                        notifications: z.boolean(),
                    }).optional(),
                    tags: z.array(z.string()).optional().describe('User tags'),
                }),
                metadata: {
                    title: 'Create a new user',
                    description: 'Creates a new user account with the provided information',
                    tags: ['Users'],
                    rest: {
                        method: 'POST',
                        path: '/users/create',
                    },
                },
                handler: async ({ input }) => ({
                    id: `user_${Date.now()}`,
                    name: input.name,
                    email: input.email,
                    age: input.age,
                    role: input.role || 'user',
                    createdAt: new Date().toISOString(),
                    preferences: input.preferences,
                    tags: input.tags,
                }),
            });

            const getUser = define.query({
                input: z.object({
                    id: z.string().uuid().describe('User ID to retrieve'),
                    includePreferences: z.boolean().optional().default(false).describe('Include user preferences'),
                }),
                output: z.object({
                    id: z.string().uuid().describe('Unique user identifier'),
                    name: z.string().describe('User full name'),
                    email: z.string().email().describe('User email address'),
                    role: z.enum(['user', 'admin', 'moderator']).describe('User role'),
                    preferences: z.object({
                        theme: z.enum(['light', 'dark']),
                        notifications: z.boolean(),
                    }).optional(),
                }),
                metadata: {
                    title: 'Get user by ID',
                    description: 'Retrieves a user by their unique identifier',
                    tags: ['Users'],
                    rest: {
                        method: 'GET',
                        path: '/users/getUser',
                    },
                },
                handler: async ({ input }) => ({
                    id: input.id,
                    name: 'John Doe',
                    email: '<EMAIL>',
                    role: 'user' as const,
                    preferences: input.includePreferences ? {
                        theme: 'light' as const,
                        notifications: true,
                    } : undefined,
                }),
            });

            const testRegistry = registry({
                createUser,
                getUser,
            });

            cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ userService: {} }),
            });

            adapter = new FastifyServerAdapter({ cqrs });
        });

        it('should serve Swagger UI with proper OpenAPI documentation', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        prefix: '/api',
                        swagger: {
                            enabled: true,
                            title: 'Test API',
                            version: '1.0.0',
                            prefix: '/docs',
                        },
                    },
                },
            });

            const routes = cqrs.getRouteDefinitions();
            await adapter.registerRoutes(routes);
            await adapter.start();

            // Check if Swagger UI is available
            const swaggerResponse = await fetch(`http://localhost:${port}/docs`);
            expect(swaggerResponse.status).toBe(200);

            const contentType = swaggerResponse.headers.get('content-type');
            expect(contentType).toContain('text/html');

            const htmlContent = await swaggerResponse.text();
            expect(htmlContent).toContain('swagger-ui');
            
            // Check that the OpenAPI spec contains the correct title
            const openApiResponse = await fetch(`http://localhost:${port}/docs/json`);
            expect(openApiResponse.status).toBe(200);
            const openApiSpec = await openApiResponse.json();
            expect(openApiSpec.info.title).toBe('Test API');
        });

        it('should generate proper OpenAPI JSON specification', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        prefix: '/api',
                        swagger: {
                            enabled: true,
                            title: 'User Management API',
                            version: '2.0.0',
                            prefix: '/docs',
                        },
                    },
                },
            });

            const routes = cqrs.getRouteDefinitions();
            await adapter.registerRoutes(routes);
            await adapter.start();

            // Get the OpenAPI JSON specification
            const openApiResponse = await fetch(`http://localhost:${port}/docs/json`);
            expect(openApiResponse.status).toBe(200);

            const openApiSpec = await openApiResponse.json();

            // Verify basic OpenAPI structure
            expect(openApiSpec.openapi).toBe('3.0.0');
            expect(openApiSpec.info.title).toBe('User Management API');
            expect(openApiSpec.info.version).toBe('2.0.0');

            // Verify paths are present (with API prefix)
            expect(openApiSpec.paths).toBeDefined();
            expect(openApiSpec.paths['/api/users/create']).toBeDefined();
            expect(openApiSpec.paths['/api/users/getUser']).toBeDefined();

            // Verify POST /api/users/create endpoint
            const createUserEndpoint = openApiSpec.paths['/api/users/create'].post;
            expect(createUserEndpoint).toBeDefined();
            expect(createUserEndpoint.summary).toBe('Create a new user');
            expect(createUserEndpoint.description).toBe('Creates a new user account with the provided information');
            expect(createUserEndpoint.tags).toEqual(['Users']);

            // Verify request body schema for createUser
            expect(createUserEndpoint.requestBody).toBeDefined();
            expect(createUserEndpoint.requestBody.content['application/json']).toBeDefined();
            
            const createUserSchema = createUserEndpoint.requestBody.content['application/json'].schema;
            expect(createUserSchema.type).toBe('object');
            expect(createUserSchema.properties).toBeDefined();
            expect(createUserSchema.properties.name).toBeDefined();
            expect(createUserSchema.properties.email).toBeDefined();
            expect(createUserSchema.properties.role).toBeDefined();

            // Verify response schema for createUser
            expect(createUserEndpoint.responses['200']).toBeDefined();
            const createUserResponseSchema = createUserEndpoint.responses['200'].content['application/json'].schema;
            expect(createUserResponseSchema.type).toBe('object');
            expect(createUserResponseSchema.properties.id).toBeDefined();
            expect(createUserResponseSchema.properties.createdAt).toBeDefined();

            // Verify GET /api/users/getUser endpoint
            const getUserEndpoint = openApiSpec.paths['/api/users/getUser'].get;
            expect(getUserEndpoint).toBeDefined();
            expect(getUserEndpoint.summary).toBe('Get user by ID');
            expect(getUserEndpoint.tags).toEqual(['Users']);

            // Verify query parameters for getUser
            expect(getUserEndpoint.parameters).toBeDefined();
            const queryParams = getUserEndpoint.parameters;
            expect(queryParams.length).toBeGreaterThan(0);
            
            const idParam = queryParams.find((p: any) => p.name === 'id');
            expect(idParam).toBeDefined();
            expect(idParam.in).toBe('query');
            expect(idParam.required).toBe(true);
        });

        it('should properly convert complex Zod schemas to JSON Schema', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        prefix: '/api',
                        swagger: {
                            enabled: true,
                            title: 'Schema Test API',
                            version: '1.0.0',
                        },
                    },
                },
            });

            const routes = cqrs.getRouteDefinitions();
            await adapter.registerRoutes(routes);
            await adapter.start();

            const openApiResponse = await fetch(`http://localhost:${port}/docs/json`);
            const openApiSpec = await openApiResponse.json();

            // Test createUser schema conversion (with API prefix)
            const createUserSchema = openApiSpec.paths['/api/users/create'].post.requestBody.content['application/json'].schema;
            
            // Verify string properties with constraints
            expect(createUserSchema.properties.name).toMatchObject({
                type: 'string',
                minLength: 1,
                maxLength: 100,
                description: 'User full name',
            });

            expect(createUserSchema.properties.email).toMatchObject({
                type: 'string',
                format: 'email',
                description: 'User email address',
            });

            // Verify number properties with constraints
            expect(createUserSchema.properties.age).toMatchObject({
                type: 'integer',
                minimum: 18,
                maximum: 120,
                description: 'User age',
            });

            // Verify enum properties
            expect(createUserSchema.properties.role).toMatchObject({
                type: 'string',
                enum: ['user', 'admin', 'moderator'],
                default: 'user',
                description: 'User role',
            });

            // Verify nested object properties
            expect(createUserSchema.properties.preferences).toBeDefined();
            expect(createUserSchema.properties.preferences.type).toBe('object');
            expect(createUserSchema.properties.preferences.properties.theme).toMatchObject({
                type: 'string',
                enum: ['light', 'dark'],
                default: 'light',
            });

            // Verify array properties
            expect(createUserSchema.properties.tags).toMatchObject({
                type: 'array',
                items: {
                    type: 'string',
                },
                description: 'User tags',
            });

            // Verify required fields
            expect(createUserSchema.required).toEqual(['name', 'email']);
        });

        it('should handle schema conversion errors gracefully', async () => {
            // Create a command with a potentially problematic schema
            const { define, registry } = CQRS.create<{ service: any }>();

            const testCommand = define.command({
                input: z.object({
                    data: z.any(), // z.any() might cause issues with conversion
                }),
                output: z.object({
                    result: z.string(),
                }),
                metadata: {
                    rest: {
                        method: 'POST',
                        path: '/test',
                    },
                },
                handler: async ({ input }) => ({ result: 'ok' }),
            });

            const testRegistry = registry({ testCommand });
            const testCqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ service: {} }),
            });

            const testAdapter = new FastifyServerAdapter({ cqrs: testCqrs });

            await testAdapter.configure({
                port: port + 1,
                protocols: {
                    rest: {
                        enabled: true,
                        swagger: {
                            enabled: true,
                            title: 'Error Test API',
                            version: '1.0.0',
                        },
                    },
                },
            });

            const routes = testCqrs.getRouteDefinitions();
            await testAdapter.registerRoutes(routes);
            await testAdapter.start();

            // Should still serve Swagger UI even with schema conversion issues
            const swaggerResponse = await fetch(`http://localhost:${port + 1}/docs`);
            expect(swaggerResponse.status).toBe(200);

            await testAdapter.stop();
        });
    });

    describe('Swagger Configuration Options', () => {
        beforeEach(() => {
            const { define, registry } = CQRS.create<{ service: any }>();
            const testRegistry = registry({});
            cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ service: {} }),
            });
            adapter = new FastifyServerAdapter({ cqrs });
        });

        it('should use custom Swagger path', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        swagger: {
                            enabled: true,
                            prefix: '/api-docs',
                            title: 'Custom Path API',
                            version: '1.0.0',
                        },
                    },
                },
            });
            await adapter.start();

            // Should be available at custom path
            const customPathResponse = await fetch(`http://localhost:${port}/api-docs`);
            expect(customPathResponse.status).toBe(200);

            // Should not be available at default path
            const defaultPathResponse = await fetch(`http://localhost:${port}/docs`);
            expect(defaultPathResponse.status).toBe(404);
        });

        it('should use default values when not specified', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        swagger: {
                            enabled: true,
                        },
                    },
                },
            });
            await adapter.start();

            const openApiResponse = await fetch(`http://localhost:${port}/docs/json`);
            const openApiSpec = await openApiResponse.json();

            expect(openApiSpec.info.title).toBe('CQRS API');
            expect(openApiSpec.info.version).toBe('1.0.0');
        });

        it('should work without Swagger when disabled', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        swagger: {
                            enabled: false,
                        },
                    },
                },
            });
            await adapter.start();

            // Swagger UI should not be available
            const swaggerResponse = await fetch(`http://localhost:${port}/docs`);
            expect(swaggerResponse.status).toBe(404);

            // Health endpoint should still work
            const healthResponse = await fetch(`http://localhost:${port}/health`);
            expect(healthResponse.status).toBe(200);
        });

        it('should work without Swagger configuration', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        // No swagger configuration
                    },
                },
            });
            await adapter.start();

            // Swagger UI should not be available
            const swaggerResponse = await fetch(`http://localhost:${port}/docs`);
            expect(swaggerResponse.status).toBe(404);

            // Health endpoint should still work
            const healthResponse = await fetch(`http://localhost:${port}/health`);
            expect(healthResponse.status).toBe(200);
        });
    });

    describe('OpenAPI Metadata Integration', () => {
        beforeEach(() => {
            const { define, registry } = CQRS.create<{ service: any }>();

            const commandWithMetadata = define.command({
                input: z.object({
                    name: z.string(),
                }),
                output: z.object({
                    id: z.string(),
                }),
                metadata: {
                    title: 'Create item',
                    description: 'Creates a new item with detailed description',
                    tags: ['Items', 'Management'],
                    rest: {
                        method: 'POST',
                        path: '/items',
                    },
                },
                handler: async ({ input }) => ({ id: '123' }),
            });

            const queryWithoutMetadata = define.query({
                input: z.object({
                    id: z.string(),
                }),
                output: z.object({
                    name: z.string(),
                }),
                metadata: {
                    rest: {
                        method: 'GET',
                        path: '/items/get',
                        // No openapi metadata
                    },
                },
                handler: async ({ input }) => ({ name: 'Test Item' }),
            });

            const testRegistry = registry({
                commandWithMetadata,
                queryWithoutMetadata,
            });

            cqrs = new CQRS({
                registry: testRegistry,
                getContext: () => ({ service: {} }),
            });

            adapter = new FastifyServerAdapter({ cqrs });
        });

        it('should include OpenAPI metadata when provided', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: {
                        enabled: true,
                        prefix: '/api',
                        swagger: {
                            enabled: true,
                            title: 'Metadata Test API',
                            version: '1.0.0',
                        },
                    },
                },
            });

            const routes = cqrs.getRouteDefinitions();
            await adapter.registerRoutes(routes);
            await adapter.start();

            const openApiResponse = await fetch(`http://localhost:${port}/docs/json`);
            const openApiSpec = await openApiResponse.json();

            // Command with metadata should have all OpenAPI properties (with API prefix)
            const commandEndpoint = openApiSpec.paths['/api/items'].post;
            expect(commandEndpoint.summary).toBe('Create item');
            expect(commandEndpoint.description).toBe('Creates a new item with detailed description');
            expect(commandEndpoint.tags).toEqual(['Items', 'Management']);

            // Query without metadata should still work but without custom metadata (with API prefix)
            const queryEndpoint = openApiSpec.paths['/api/items/get'].get;
            expect(queryEndpoint).toBeDefined();
            // Should not have custom summary/description/tags
            expect(queryEndpoint.summary).toBeUndefined();
            expect(queryEndpoint.description).toBeUndefined();
            expect(queryEndpoint.tags).toBeUndefined();
        });
    });
});
