import { z } from 'zod';
import { CQRS } from '../../../cqrs';
import fetch from 'cross-fetch';

describe('Fastify Integration', () => {
    let cqrs: CQRS<any, any>;
    let port: number;

    beforeEach(() => {
        port = 11500 + Math.floor(Math.random() * 1000);
    });

    afterEach(async () => {
        if (cqrs) {
            await cqrs.stop();
        }
    });

    describe('zod-to-json-schema fallback', () => {
        it('should handle missing zod-to-json-schema gracefully', async () => {
            // Mock require to simulate missing module
            const originalRequire = require;
            jest.doMock('zod-to-json-schema', () => {
                throw new Error('Module not found');
            });

            const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

            const { define, registry } = CQRS.create();
            const testCommand = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    rest: { method: 'POST', path: '/test' }
                },
                handler: async ({ input }) => ({ id: 'test' })
            });

            cqrs = new CQRS({
                registry: registry({ testCommand }),
                getContext: () => ({})
            });

            await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port,
                        protocols: { rest: { enabled: true } }
                    }
                }
            });

            // Should have logged warning about zod-to-json-schema
            expect(consoleWarnSpy).toHaveBeenCalledWith(
                'zod-to-json-schema conversion failed:',
                expect.any(Error)
            );

            consoleWarnSpy.mockRestore();
            jest.dontMock('zod-to-json-schema');
        });
    });

    describe('Development error handling', () => {
        it('should include stack trace in development mode', async () => {
            const originalEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'development';

            const { define, registry } = CQRS.create();
            const errorCommand = define.command({
                input: z.object({ message: z.string() }),
                output: z.object({ success: z.boolean() }),
                metadata: {
                    rest: { method: 'POST', path: '/error' }
                },
                handler: async ({ input }) => {
                    throw new Error(input.message);
                }
            });

            cqrs = new CQRS({
                registry: registry({ errorCommand }),
                getContext: () => ({})
            });

            await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port,
                        protocols: { rest: { enabled: true } }
                    }
                }
            });

            const response = await fetch(`http://localhost:${port}/api/error`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: 'Test error' })
            });

            const result = await response.json();
            expect(result.error.stack).toBeDefined();
            expect(result.error.stack).toContain('Error: Test error');

            process.env.NODE_ENV = originalEnv;
        });
    });

    describe('CORS configuration', () => {
        it('should handle boolean CORS config', async () => {
            const { define, registry } = CQRS.create();
            const testCommand = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    rest: { method: 'POST', path: '/test' }
                },
                handler: async ({ input }) => ({ id: 'test' })
            });

            cqrs = new CQRS({
                registry: registry({ testCommand }),
                getContext: () => ({})
            });

            await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port,
                        cors: true, // Boolean config
                        protocols: { rest: { enabled: true } }
                    }
                }
            });

            // Server should start without errors
            const response = await fetch(`http://localhost:${port}/health`);
            expect(response.status).toBe(200);
        });

        it('should handle object CORS config', async () => {
            const { define, registry } = CQRS.create();
            const testCommand = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    rest: { method: 'POST', path: '/test' }
                },
                handler: async ({ input }) => ({ id: 'test' })
            });

            cqrs = new CQRS({
                registry: registry({ testCommand }),
                getContext: () => ({})
            });

            await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port,
                        cors: { origin: '*' }, // Object config
                        protocols: { rest: { enabled: true } }
                    }
                }
            });

            // Server should start without errors
            const response = await fetch(`http://localhost:${port}/health`);
            expect(response.status).toBe(200);
        });
    });

    describe('Middleware configuration', () => {
        it('should handle compression middleware', async () => {
            const { define, registry } = CQRS.create();
            const testCommand = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    rest: { method: 'POST', path: '/test' }
                },
                handler: async ({ input }) => ({ id: 'test' })
            });

            cqrs = new CQRS({
                registry: registry({ testCommand }),
                getContext: () => ({})
            });

            await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port,
                        middleware: { compression: true },
                        protocols: { rest: { enabled: true } }
                    }
                }
            });

            // Server should start without errors
            const response = await fetch(`http://localhost:${port}/health`);
            expect(response.status).toBe(200);
        });

        it('should handle helmet middleware', async () => {
            const { define, registry } = CQRS.create();
            const testCommand = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    rest: { method: 'POST', path: '/test' }
                },
                handler: async ({ input }) => ({ id: 'test' })
            });

            cqrs = new CQRS({
                registry: registry({ testCommand }),
                getContext: () => ({})
            });

            await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port,
                        middleware: { helmet: true },
                        protocols: { rest: { enabled: true } }
                    }
                }
            });

            // Server should start without errors
            const response = await fetch(`http://localhost:${port}/health`);
            expect(response.status).toBe(200);
        });
    });

    describe('WebSocket support', () => {
        it('should handle WebSocket configuration', async () => {
            const { define, registry } = CQRS.create();
            const testCommand = define.command({
                input: z.object({ name: z.string() }),
                output: z.object({ id: z.string() }),
                metadata: {
                    rest: { method: 'POST', path: '/test' }
                },
                handler: async ({ input }) => ({ id: 'test' })
            });

            cqrs = new CQRS({
                registry: registry({ testCommand }),
                getContext: () => ({})
            });

            await cqrs.run({
                http: {
                    adapter: 'fastify',
                    config: {
                        port,
                        protocols: {
                            rest: { enabled: true },
                            trpc: { enabled: true, useWSS: true }
                        }
                    }
                }
            });

            // Server should start without errors
            const response = await fetch(`http://localhost:${port}/health`);
            expect(response.status).toBe(200);
        });
    });
});
