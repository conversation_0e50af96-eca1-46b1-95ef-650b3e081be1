import { FastifyServerAdapter } from '../fastify';

describe('FastifyServerAdapter', () => {
    let adapter: FastifyServerAdapter;
    let port: number;

    beforeEach(() => {
        port = 12000 + Math.floor(Math.random() * 1000);
        adapter = new FastifyServerAdapter();
    });

    afterEach(async () => {
        if (adapter?.status === 'running') {
            await adapter.stop();
        }
    });

    describe('Error Status Code Mapping', () => {
        it('should map error codes to HTTP status codes', () => {
            // Test all error code cases
            const testCases = [
                { code: 'BAD_REQUEST', expected: 400 },
                { code: 'UNAUTHORIZED', expected: 401 },
                { code: 'FORBIDDEN', expected: 403 },
                { code: 'NOT_FOUND', expected: 404 },
                { code: 'CONFLICT', expected: 409 },
                { code: 'UNPROCESSABLE_CONTENT', expected: 422 },
                { code: 'TOO_MANY_REQUESTS', expected: 429 },
                { code: 'INTERNAL_SERVER_ERROR', expected: 500 },
                { code: 'UNKNOWN_CODE', expected: 500 }, // default case
            ];

            testCases.forEach(({ code, expected }) => {
                const error = { code };
                const result = (adapter as any).getErrorStatusCode(error);
                expect(result).toBe(expected);
            });
        });

        it('should map error messages to HTTP status codes', () => {
            const testCases = [
                { message: 'User not found', expected: 404 },
                { message: 'User unauthorized', expected: 401 },
                { message: 'Action forbidden', expected: 403 },
                { message: 'validation failed', expected: 400 }, // lowercase to match includes
                { message: 'invalid input', expected: 400 }, // lowercase to match includes
                { message: 'Some other error', expected: 500 }, // default case
            ];

            testCases.forEach(({ message, expected }) => {
                const error = { message };
                const result = (adapter as any).getErrorStatusCode(error);
                expect(result).toBe(expected);
            });
        });
    });

    describe('Protocol Support', () => {
        it('should support all protocol types', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: { enabled: true },
                    trpc: { enabled: true, useWSS: false } // Disable WebSocket to avoid missing module
                }
            });

            expect(adapter.supportsProtocol('rest')).toBe(true);
            expect(adapter.supportsProtocol('trpc')).toBe(true);
            expect(adapter.supportsProtocol('websocket')).toBe(false); // Should be false when useWSS is false
            expect(adapter.supportsProtocol('unknown' as any)).toBe(false); // default case
        });

        it('should handle disabled protocols', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: { enabled: false },
                    trpc: { enabled: false }
                }
            });

            expect(adapter.supportsProtocol('rest')).toBe(false);
            expect(adapter.supportsProtocol('trpc')).toBe(false);
            expect(adapter.supportsProtocol('websocket')).toBe(false);
        });
    });

    describe('Context Creation', () => {
        it('should create context with CQRS function', () => {
            const mockCqrs = {
                getContextFunction: () => (params: any) => ({ userId: 'test', ...params })
            };
            adapter = new FastifyServerAdapter({ cqrs: mockCqrs });

            const result = (adapter as any).createUnifiedContext({
                procedureName: 'test',
                procedureType: 'command',
                input: { data: 'test' },
                rest: { headers: {} }
            });

            expect(result.userId).toBe('test');
            expect(result.procedureName).toBe('test');
        });

        it('should return empty object when no CQRS context function', () => {
            const result = (adapter as any).createUnifiedContext({
                procedureName: 'test',
                procedureType: 'command',
                input: { data: 'test' }
            });

            expect(result).toEqual({});
        });
    });

    describe('Server Lifecycle', () => {
        it('should handle start errors', async () => {
            // Configure with invalid port to trigger start error
            await adapter.configure({ port: -1 });

            await expect(adapter.start()).rejects.toThrow('Failed to start Fastify server');
            expect(adapter.status).toBe('error');
        });

        it('should handle stop when not configured', async () => {
            // Should not throw when stopping without configuration
            await expect(adapter.stop()).resolves.not.toThrow();
        });

        it('should handle stop errors', async () => {
            await adapter.configure({ port });
            await adapter.start();

            // Mock fastify.close to throw error
            const mockClose = jest.fn().mockRejectedValue(new Error('Close failed'));
            (adapter as any)._fastify.close = mockClose;

            await expect(adapter.stop()).rejects.toThrow('Failed to stop Fastify server');
            expect(adapter.status).toBe('error');
        });

        it('should handle successful stop', async () => {
            await adapter.configure({ port });
            await adapter.start();
            expect(adapter.status).toBe('running');

            await adapter.stop();
            expect(adapter.status).toBe('stopped');
        });

        it('should register health endpoint when REST is enabled', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: { enabled: true, health: true }
                }
            });
            await adapter.start();

            // Health endpoint should be registered
            expect(adapter.status).toBe('running');
            expect(adapter.supportsProtocol('rest')).toBe(true);
        });

        it('should not register health endpoint when disabled', async () => {
            await adapter.configure({
                port,
                protocols: {
                    rest: { enabled: true, health: false }
                }
            });
            await adapter.start();

            expect(adapter.status).toBe('running');
        });
    });

    describe('Health Check', () => {
        it('should return healthy status when running', async () => {
            await adapter.configure({ port });
            await adapter.start();

            const health = await adapter.getHealth();
            expect(health.status).toBe('healthy');
            expect(health.details?.status).toBe('running');
            expect(health.details?.protocols.rest).toBe(false); // REST not enabled by default
        });

        it('should return unhealthy status when not running', async () => {
            const health = await adapter.getHealth();
            expect(health.status).toBe('unhealthy');
            expect(health.details?.status).toBe('stopped');
        });
    });

    describe('tRPC Integration', () => {
        it('should handle tRPC disabled configuration', async () => {
            await adapter.configure({
                port,
                protocols: {
                    trpc: { enabled: false }
                }
            });

            const mockRouter = { _def: {} };
            await adapter.registerTRPCRouter(mockRouter);

            // Should not register tRPC routes when disabled
            expect(adapter.supportsProtocol('trpc')).toBe(false);
        });

        it('should register tRPC UI when enabled', async () => {
            await adapter.configure({
                port,
                protocols: {
                    trpc: {
                        enabled: true,
                        ui: { enabled: true, prefix: '/trpc-ui' }
                    }
                }
            });

            const mockRouter = { _def: {} };
            await adapter.registerTRPCRouter(mockRouter);

            expect(adapter.supportsProtocol('trpc')).toBe(true);
        });

        it('should log tRPC UI during startup when enabled', async () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            await adapter.configure({
                port,
                protocols: {
                    trpc: {
                        enabled: true,
                        ui: { enabled: true, prefix: '/ui' }
                    }
                }
            });

            const mockRouter = { _def: {} };
            await adapter.registerTRPCRouter(mockRouter);
            await adapter.start();

            expect(consoleSpy).toHaveBeenCalledWith(
                expect.stringContaining('🎨 tRPC UI:')
            );

            consoleSpy.mockRestore();
        });
    });

    describe('REST Route Handling', () => {
        it('should handle missing Fastify instance in registerRestRoute', async () => {
            // Create adapter without configuring
            const unconfiguredAdapter = new FastifyServerAdapter();

            const mockRoute = {
                procedureName: 'test',
                procedureType: 'query' as const,
                inputSchema: {},
                outputSchema: {},
                metadata: { rest: { method: 'GET' as const } },
                handler: jest.fn()
            };

            // Should return early when Fastify not configured
            await expect(
                (unconfiguredAdapter as any).registerRestRoute(mockRoute, '/api')
            ).resolves.not.toThrow();
        });

        it('should handle missing CQRS instance error', async () => {
            await adapter.configure({ port });

            const mockRoute = {
                procedureName: 'test',
                procedureType: 'query' as const,
                inputSchema: {},
                outputSchema: {},
                metadata: { rest: { method: 'GET' as const } },
                handler: jest.fn()
            };

            // Register route without CQRS instance
            await (adapter as any).registerRestRoute(mockRoute, '/api');

            // Simulate request handling without CQRS
            (adapter as any)._cqrs = null;

            const mockRequest = {
                method: 'GET',
                query: {},
                headers: {},
                url: '/test',
                ip: '127.0.0.1'
            };

            const mockReply = {
                status: jest.fn().mockReturnThis(),
            };

            const result = await (adapter as any).handleRestRequest(
                mockRequest,
                mockReply,
                mockRoute
            );

            expect(mockReply.status).toHaveBeenCalledWith(500);
            expect(result.error.message).toContain('CQRS instance not available');
        });

        it('should include stack trace in development environment', async () => {
            const originalEnv = process.env.NODE_ENV;
            process.env.NODE_ENV = 'development';

            try {
                await adapter.configure({ port });

                const mockRoute = {
                    procedureName: 'test',
                    procedureType: 'query' as const,
                    inputSchema: {},
                    outputSchema: {},
                    metadata: { rest: { method: 'GET' as const } },
                    handler: jest.fn()
                };

                await (adapter as any).registerRestRoute(mockRoute, '/api');

                // Mock CQRS to throw error
                (adapter as any)._cqrs = {
                    executeWithContext: jest.fn().mockRejectedValue(new Error('Test error'))
                };

                const mockRequest = {
                    method: 'GET',
                    query: {},
                    headers: {},
                    url: '/test',
                    ip: '127.0.0.1'
                };

                const mockReply = {
                    status: jest.fn().mockReturnThis(),
                };

                const result = await (adapter as any).handleRestRequest(
                    mockRequest,
                    mockReply,
                    mockRoute
                );

                expect(result.error).toHaveProperty('stack');
                expect(typeof result.error.stack).toBe('string');
            } finally {
                process.env.NODE_ENV = originalEnv;
            }
        });
    });
});
