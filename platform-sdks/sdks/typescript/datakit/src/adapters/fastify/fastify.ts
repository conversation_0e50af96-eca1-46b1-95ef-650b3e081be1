import type { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fastify from 'fastify';
import type { FastifyTRPCPluginOptions } from '@trpc/server/adapters/fastify';
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify';
import type {
    HTTPAdapter,
    FastifyServerConfig,
    RouteDefinition,
    HttpMethod,
    ServerProtocolConfig,
    HealthResponse,
    AdapterStatus
} from '../../cqrs/types';
import { generateTraceId } from '../../cqrs/trace';

export class FastifyServerAdapter implements HTTPAdapter<FastifyServerConfig> {
    readonly name = 'fastify-server';

    private _status: AdapterStatus = 'stopped';
    private _fastify?: FastifyInstance;
    private _config?: FastifyServerConfig;
    private _cqrs?: any; // Reference to CQRS instance
    private _trpcRouter?: any;
    private _routes: RouteDefinition[] = [];

    get status(): AdapterStatus {
        return this._status;
    }

    constructor(private options: { cqrs?: any } = {}) {
        this._cqrs = options.cqrs;
    }

    async configure(config: FastifyServerConfig): Promise<void> {
        this._status = 'starting';
        this._config = { ...config };

        try {
            // Create Fastify instance
            this._fastify = fastify({
                logger: this._config.fastifyOptions?.logger ?? false,
                trustProxy: this._config.fastifyOptions?.trustProxy ?? false,
                bodyLimit: this._config.fastifyOptions?.bodyLimit ?? 1048576,
                maxParamLength: 5000, // Required for tRPC
                ...this._config.fastifyOptions,
            });

            // Configure CORS if enabled
            if (this._config.cors) {
                await this._fastify.register(require('@fastify/cors'),
                    typeof this._config.cors === 'boolean' ? {} : this._config.cors
                );
            }

            // Configure middleware
            if (this._config.middleware?.compression) {
                await this._fastify.register(require('@fastify/compress'));
            }

            if (this._config.middleware?.helmet) {
                await this._fastify.register(require('@fastify/helmet'));
            }

            // Register Swagger plugin early if REST with Swagger is enabled
            const restConfig = this._config.protocols?.rest;
            if (restConfig?.enabled && restConfig.swagger?.enabled) {
                const swaggerConfig = restConfig.swagger;

                await this._fastify.register(require('@fastify/swagger'), {
                    openapi: {
                        openapi: '3.0.0',
                        info: {
                            title: swaggerConfig.title || 'CQRS API',
                            version: swaggerConfig.version || '1.0.0',
                        },
                    },
                });

                // Register Swagger UI
                await this._fastify.register(require('@fastify/swagger-ui'), {
                    routePrefix: swaggerConfig.prefix || '/docs',
                    uiConfig: {
                        docExpansion: 'list',
                        deepLinking: false,
                    },
                });
            }

            // Register WebSocket support if tRPC with WebSocket is enabled
            const trpcConfig = this._config.protocols?.trpc;
            if (trpcConfig?.enabled && trpcConfig.useWSS) {
                await this._fastify.register(require('@fastify/websocket'));
            }

            this._status = 'configured';
        } catch (error) {
            this._status = 'error';
            throw new Error(`Failed to configure Fastify server: ${error}`);
        }
    }

    async registerRoutes(routes: RouteDefinition[]): Promise<void> {
        if (!this._fastify) {
            throw new Error('Fastify server not configured');
        }

        this._routes = routes;
        const restConfig = this._config?.protocols?.rest;

        if (!restConfig?.enabled) {
            return; // REST is disabled
        }

        const restPrefix = restConfig.prefix || '/api';

        // Filter routes that have REST metadata
        const restRoutes = routes.filter(route => route.metadata.rest !== null);

        for (const route of restRoutes) {
            await this.registerRestRoute(route, restPrefix);
        }

        // Register Swagger documentation if enabled
        if (restConfig.swagger?.enabled) {
            await this.registerSwagger(restRoutes, restConfig);
        }
    }

    async registerTRPCRouter(router: any): Promise<void> {
        if (!this._fastify) {
            throw new Error('Fastify instance not configured');
        }

        this._trpcRouter = router;

        const trpcConfig = this._config?.protocols?.trpc;
        if (!trpcConfig?.enabled) {
            return; // tRPC is disabled
        }

        const trpcPrefix = trpcConfig.prefix || '/trpc';

        // Register tRPC plugin
        const trpcOptions: FastifyTRPCPluginOptions<typeof router>['trpcOptions'] = {
            router,
            createContext: ({ req, res, info }) => {
                return this.createUnifiedContext({
                    procedureName: info.calls?.[0]?.path,
                    procedureType: info.type === 'mutation' ? 'command' : 'query',
                    input: undefined,
                    rest: {
                        headers: req.headers,
                        ip: req.ip,
                        url: req.url,
                        query: req.query,
                        body: req.body,
                        req,
                        res,
                    },
                    origin: 'tRPC',
                });
            },
            onError: ({ path, error }) => {
                this._fastify?.log.error(`tRPC error on path '${path}':`, error);
            },
        };

        if (trpcConfig?.enabled && trpcConfig.ui?.enabled) {
            const uiConfig = trpcConfig.ui;

            // Add tRPC UI panel route
            this._fastify.get(uiConfig.prefix || '/trpc-ui', async (_, reply) => {
                const { renderTrpcPanel } = await import('trpc-ui');

                return reply
                    .header('Content-Type', 'text/html')
                    .send(renderTrpcPanel(router, {
                        url: trpcPrefix,
                        transformer: uiConfig.transformer,
                        cache: true,
                        meta: {
                            title: this._cqrs?.meta?.title || this._config?.swagger?.title || 'CQRS API',
                            description: this._cqrs?.meta?.description || this._config?.swagger?.info?.description || 'tRPC API UI',
                        }
                    }));
            });
        }

        await this._fastify.register(fastifyTRPCPlugin, {
            prefix: trpcPrefix,
            useWSS: trpcConfig.useWSS || false,
            keepAlive: trpcConfig.keepAlive,
            trpcOptions,
        });
    }

    private async registerRestRoute(route: RouteDefinition, prefix: string): Promise<void> {
        if (!this._fastify || !route.metadata.rest) {
            return;
        }

        const restMeta = route.metadata.rest;
        const method = (restMeta.method || (route.procedureType === 'query' ? 'GET' : 'POST')).toLowerCase();
        const path = `${prefix}${restMeta.path || `/${route.procedureName}`}`;

        // Create Fastify schema from Zod schemas for OpenAPI generation
        const schema: any = {
            response: {
                200: this.zodToJsonSchema(route.outputSchema),
            },
        };

        // Add request schema based on HTTP method
        if (method === 'get' || method === 'head') {
            // For GET requests, convert input schema to query parameters
            schema.querystring = this.zodToJsonSchema(route.inputSchema);
        } else {
            // For POST/PUT/PATCH requests, use request body
            schema.body = this.zodToJsonSchema(route.inputSchema);
        }

        // Build route options with OpenAPI metadata
        const routeOptions: any = {
            method: method.toUpperCase(),
            url: path,
            schema,
            handler: async (request: FastifyRequest, reply: FastifyReply) => {
                return this.handleRestRequest(request, reply, route);
            },
        };

        // Add OpenAPI metadata from procedure metadata
        if (route.metadata.title || route.metadata.description || route.metadata.tags || restMeta.openapi) {
            routeOptions.schema = {
                ...routeOptions.schema,
                summary: route.metadata.title || restMeta.openapi?.summary,
                description: route.metadata.description || restMeta.openapi?.description,
                tags: route.metadata.tags || restMeta.openapi?.tags,
                deprecated: route.metadata.deprecated || restMeta.openapi?.deprecated,
                operationId: restMeta.openapi?.operationId || `${route.procedureType}_${route.procedureName}`,
            };
        }

        // Register the route with Fastify
        this._fastify.route(routeOptions);
    }

    private zodToJsonSchema(zodSchema: any): any {
        try {
            // Try to use zod-to-json-schema for proper conversion
            const zodToJsonSchemaModule = require('zod-to-json-schema');
            const zodToJsonSchema = zodToJsonSchemaModule.zodToJsonSchema || zodToJsonSchemaModule.default;

            const result = zodToJsonSchema(zodSchema, {
                target: 'openApi3',
                $refStrategy: 'none', // Inline schemas for simplicity
            });

            return result;
        } catch (error) {
            console.warn('zod-to-json-schema conversion failed:', error);
            // Fallback to simple schema
            return {
                type: 'object',
                additionalProperties: true,
            };
        }
    }

    private async handleRestRequest(
        request: FastifyRequest,
        reply: FastifyReply,
        route: RouteDefinition
    ): Promise<any> {
        try {
            // Extract input based on HTTP method
            let input: any;
            const method = request.method.toUpperCase() as HttpMethod;

            if (method === 'GET' || method === 'HEAD') {
                input = request.query as Record<string, any>;
            } else {
                input = request.body;
            }

            // Use the new efficient executeWithContext method
            if (!this._cqrs) {
                throw new Error('CQRS instance not available');
            }

            const contextParams = {
                traceId: generateTraceId(),
                procedureName: route.procedureName,
                procedureType: route.procedureType,
                input,
                rest: {
                    headers: request.headers,
                    url: request.url,
                    query: request.query,
                    body: request.body,
                    ip: request.ip,
                },
                origin: 'REST',
            };

            const result = await this._cqrs.executeWithContext(
                route.procedureName,
                input,
                contextParams
            );

            // Set appropriate status code based on method and result
            const statusCode = method === 'POST' ? 200 : 200;
            reply.status(statusCode);

            return result;
        } catch (error: any) {
            const statusCode = this.getErrorStatusCode(error);
            reply.status(statusCode);

            return {
                error: {
                    message: error.message || 'Internal server error',
                    code: statusCode,
                    ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
                },
            };
        }
    }

    private async registerSwagger(routes: RouteDefinition[], restConfig: NonNullable<ServerProtocolConfig['rest']>): Promise<void> {
        // Swagger plugin and UI are already registered in configure method
        // This method is kept for potential future use but currently does nothing
        return;
    }

    private getErrorStatusCode(error: any): number {
        if (error.code) {
            switch (error.code) {
                case 'BAD_REQUEST': return 400;
                case 'UNAUTHORIZED': return 401;
                case 'FORBIDDEN': return 403;
                case 'NOT_FOUND': return 404;
                case 'CONFLICT': return 409;
                case 'UNPROCESSABLE_CONTENT': return 422;
                case 'TOO_MANY_REQUESTS': return 429;
                case 'INTERNAL_SERVER_ERROR': return 500;
                default: return 500;
            }
        }
        // Map common error types to HTTP status codes
        if (error.message?.includes('not found') || error.message?.includes('Not found')) {
            return 404;
        }
        if (error.message?.includes('unauthorized') || error.message?.includes('Unauthorized')) {
            return 401;
        }
        if (error.message?.includes('forbidden') || error.message?.includes('Forbidden')) {
            return 403;
        }
        if (error.message?.includes('validation') || error.message?.includes('invalid')) {
            return 400;
        }
        return 500;
    }

    /**
     * Create unified context for tRPC (returns the actual context object)
     */
    private createUnifiedContext(params: {
        procedureName: string;
        procedureType: 'command' | 'query';
        input: unknown;
        rest?: Record<string, any>;
        origin?: string;
    }) {
        // Use CQRS context creation if available
        if (this._cqrs?.getContextFunction()) {
            const contextParams = {
                traceId: generateTraceId(),
                procedureName: params.procedureName,
                procedureType: params.procedureType,
                input: params.input,
                rest: params.rest,
                origin: params.origin,
            };
            return this._cqrs.getContextFunction()(contextParams);
        }
        return {};
    }

    supportsProtocol(protocol: 'rest' | 'trpc' | 'websocket'): boolean {
        switch (protocol) {
            case 'rest':
                return this._config?.protocols?.rest?.enabled ?? false;
            case 'trpc':
                return this._config?.protocols?.trpc?.enabled ?? false;
            case 'websocket':
                return this._config?.protocols?.trpc?.useWSS ?? false;
            default:
                return false;
        }
    }

    async start(): Promise<void> {
        if (!this._fastify) {
            throw new Error('Fastify server not configured');
        }

        this._status = 'starting';

        try {
            // Register health endpoint
            if (this._config?.protocols?.rest?.enabled && this._config?.protocols?.rest?.health !== false) {
                this._fastify.get('/health', async () => {
                    return this.getHealth();
                });
            }

            const port = this._config?.port || 3000;
            const host = this._config?.host || '0.0.0.0';

            await this._fastify.listen({ port, host });
            this._status = 'running';

            console.log(`🚀 Fastify server started on ${host}:${port}`);

            if (this.supportsProtocol('rest')) {
                const restPrefix = this._config?.protocols?.rest?.prefix || '/';
                console.log(`📡 REST API: http://${host}:${port}${restPrefix}`);

                if (this._config?.protocols?.rest?.swagger?.enabled) {
                    const swaggerPath = this._config.protocols.rest.swagger.prefix || '/docs';
                    console.log(`📚 Swagger UI: http://${host}:${port}${swaggerPath}`);
                }
            }

            if (this.supportsProtocol('trpc')) {
                const trpcPrefix = this._config?.protocols?.trpc?.prefix || '/trpc';
                console.log(`🔧 tRPC: http://${host}:${port}${trpcPrefix}`);

                if (this.supportsProtocol('websocket')) {
                    console.log(`🔌 WebSocket: ws://${host}:${port}${trpcPrefix}`);
                }

                if (this._config?.protocols?.trpc?.ui?.enabled) {
                    const uiPrefix = this._config?.protocols?.trpc?.ui?.prefix || '/trpc-ui';
                    console.log(`🎨 tRPC UI: http://${host}:${port}${uiPrefix}`);
                }
            }
        } catch (error) {
            this._status = 'error';
            throw new Error(`Failed to start Fastify server: ${error}`);
        }
    }

    async stop(): Promise<void> {
        if (!this._fastify) {
            return;
        }

        this._status = 'stopping';

        try {
            await this._fastify.close();
            this._status = 'stopped';
            console.log('🛑 Fastify server stopped');
        } catch (error) {
            this._status = 'error';
            throw new Error(`Failed to stop Fastify server: ${error}`);
        }
    }

    async getHealth(): Promise<HealthResponse> {
        const isHealthy = this._status === 'running' && this._fastify !== undefined;

        return {
            status: isHealthy ? 'healthy' : 'unhealthy',
            timestamp: Date.now(),
            details: {
                status: this._status,
                protocols: {
                    rest: this.supportsProtocol('rest'),
                    trpc: this.supportsProtocol('trpc'),
                    websocket: this.supportsProtocol('websocket'),
                },
                routes: this._routes.length,
                trpcRouter: !!this._trpcRouter,
            },
        };
    }
}
