import type { FastMCP } from 'fastmcp';

import type {
    MCPAdapter,
    MCPConfig,
    RouteDefinition,
    MCPResource,
    MCPResourceTemplate,
    AdapterStatus,
    HealthResponse,
    MCPContext
} from '../../cqrs/types';
import { generateTraceId } from '../../cqrs/trace';

export class FastMCPAdapter implements MCPAdapter<MCPConfig> {
    public readonly name = 'fastmcp-adapter';
    public status: AdapterStatus = 'stopped';

    private _fastMCP?: FastMCP;
    private _cqrs?: any;
    private _config?: MCPConfig;

    constructor(options: { cqrs: any }) {
        this._cqrs = options.cqrs;
    }

    async configure(config: MCPConfig): Promise<void> {
        this._config = config;

        // Derive name/version from CQRS metadata if available
        const cqrsMeta = this._cqrs?.getMetadata ? this._cqrs.getMetadata() : undefined;

        const fastmcpModule = await import('fastmcp');
        const { FastMCP: FastMCPClass } = fastmcpModule as { FastMCP: typeof import('fastmcp').FastMCP };

        this._fastMCP = new FastMCPClass({
            name: cqrsMeta?.serviceName || config.name || 'CQRS MCP Server',
            version: (cqrsMeta?.serviceVersion as `${number}.${number}.${number}`) || config.version || '1.0.0',
            ...(config.instructions && { instructions: config.instructions }),
        });

        this.status = 'configured';
    }

    async registerRoutes(routes: RouteDefinition[]): Promise<void> {
        if (!this._fastMCP) {
            throw new Error('MCP adapter not configured. Call configure() first.');
        }

        const mcpRoutes = routes.filter(route => route.metadata.mcp !== null);

        for (const route of mcpRoutes) {
            await this.registerMCPTool(route);
        }
    }

    async registerResources(resources: MCPResource[]): Promise<void> {
        if (!this._fastMCP) {
            throw new Error('MCP adapter not configured. Call configure() first.');
        }

        for (const resource of resources) {
            this._fastMCP.addResource({
                uri: resource.uri,
                name: resource.name,
                mimeType: resource.mimeType,
                load: async () => {
                    const result = await Promise.resolve(resource.load());
                    return {
                        text: result.text || '',
                        ...(result.blob && { blob: result.blob }),
                        ...(result.mimeType && { mimeType: result.mimeType }),
                    };
                },
            });
        }
    }

    async registerResourceTemplates(templates: MCPResourceTemplate[]): Promise<void> {
        if (!this._fastMCP) {
            throw new Error('MCP adapter not configured. Call configure() first.');
        }

        for (const template of templates) {
            this._fastMCP.addResourceTemplate({
                uriTemplate: template.uriTemplate,
                name: template.name,
                mimeType: template.mimeType,
                arguments: template.arguments.map(arg => ({
                    name: arg.name,
                    required: arg.required,
                    ...(arg.description && { description: arg.description }),
                })),
                load: async (args: Record<string, string>) => {
                    const result = await Promise.resolve(template.load(args));
                    return {
                        text: result.text || '',
                        ...(result.blob && { blob: result.blob }),
                        ...(result.mimeType && { mimeType: result.mimeType }),
                    };
                },
            });
        }
    }

    supportsProtocol(protocol: 'mcp'): boolean {
        return protocol === 'mcp';
    }

    async start(): Promise<void> {
        if (!this._fastMCP) {
            throw new Error('MCP adapter not configured. Call configure() first.');
        }

        this.status = 'starting';

        // Start the MCP server based on transport type
        if (this._config?.transportType === 'httpStream' && this._config.httpStream) {
            // For HTTP stream, pass the correct format to FastMCP
            const port = this._config.httpStream.port || 8080;
            const endpoint = this._config.httpStream.endpoint
                ? (this._config.httpStream.endpoint.startsWith('/')
                    ? this._config.httpStream.endpoint as `/${string}`
                    : `/${this._config.httpStream.endpoint}` as const)
                : '/mcp' as const;
            await this._fastMCP.start({
                transportType: 'httpStream',
                httpStream: {
                    port,
                    endpoint
                }
            });
        } else {
            await this._fastMCP.start({
                transportType: 'stdio'
            });
        }

        this.status = 'running';
    }

    async stop(): Promise<void> {
        if (this._fastMCP && this.status === 'running') {
            this.status = 'stopping';
            await this._fastMCP.stop();
            this.status = 'stopped';
        }
    }

    async getHealth(): Promise<HealthResponse> {
        return {
            status: this.status === 'running' ? 'healthy' : 'unhealthy',
            timestamp: Date.now(),
            details: {
                transport: this._config?.transportType || 'stdio',
                ...(this._config?.httpStream && {
                    httpStream: this._config.httpStream
                })
            }
        };
    }

    private async registerMCPTool(route: RouteDefinition): Promise<void> {
        if (!this._fastMCP || !route.metadata?.mcp || route.metadata.mcp.enabled === false) return;

        const mcpMetadata = route.metadata.mcp!;

        // Derive smart defaults from procedure type and general metadata
        const isQuery = route.procedureType === 'query';
        const isCommand = route.procedureType === 'command';

        // Use title from general metadata, fallback to procedure name
        const title = route.metadata.title || route.procedureName;

        // Pass Zod schema directly to FastMCP - it handles the conversion internally
        this._fastMCP.addTool({
            name: route.procedureName,
            description: route.metadata.description || `${route.procedureType} operation: ${route.procedureName}`,
            parameters: route.inputSchema, // Pass Zod schema directly
            annotations: {
                // Smart defaults based on procedure type
                readOnlyHint: mcpMetadata.readOnlyHint ?? isQuery, // Queries are read-only by default
                idempotentHint: mcpMetadata.idempotentHint ?? isQuery, // Queries are idempotent by default
                destructiveHint: mcpMetadata.destructiveHint ?? false, // Conservative default

                // Use title from general metadata for UI display
                title: title,

                // User-specified overrides
                ...(mcpMetadata.streamingHint !== undefined && { streamingHint: mcpMetadata.streamingHint }),
                ...(mcpMetadata.openWorldHint !== undefined && { openWorldHint: mcpMetadata.openWorldHint }),
            },
            ...(mcpMetadata.timeoutMs && { timeoutMs: mcpMetadata.timeoutMs }),
            execute: async (args, mcpContext) => {
                try {
                    // Create MCP context wrapper - simplified log interface
                    const mcpContextWrapper: MCPContext = {
                        streamContent: async (content) => {
                            if (Array.isArray(content)) {
                                await mcpContext.streamContent(content);
                            } else {
                                await mcpContext.streamContent([content]);
                            }
                        },
                        reportProgress: mcpContext.reportProgress,
                    };

                    // Execute through CQRS with enhanced context
                    const result = await this._cqrs.executeWithContext(
                        route.procedureName,
                        args,
                        {
                            traceId: generateTraceId(),
                            procedureName: route.procedureName,
                            procedureType: route.procedureType,
                            input: args,
                            mcp: mcpContextWrapper,
                            origin: 'MCP',
                        }
                    );

                    // Transform result for MCP
                    return this.transformResult(result);
                } catch (error) {
                    console.error(`MCP tool execution failed:`, error);
                    throw error;
                }
            },
        });
    }

    private transformResult(result: any): any {
        // Handle null/undefined results
        if (result === undefined || result === null) {
            return {
                content: [{ type: 'text', text: '' }]
            };
        }

        // Handle results with _mcp key (special MCP formatting)
        if (typeof result === 'object' && '_mcp' in result) {
            const payload = (result as any)._mcp;

            if (typeof payload === 'string') {
                return { content: [{ type: 'text', text: payload }] };
            }

            // If payload is MCPContent or array
            if (Array.isArray(payload)) {
                return { content: payload };
            }

            if (payload && typeof payload === 'object' && payload.type) {
                return { content: [payload] };
            }
        }

        // Default transformation - convert to JSON string for display
        const textContent = typeof result === 'string'
            ? result
            : JSON.stringify(result, null, 2);

        return {
            content: [
                {
                    type: 'text',
                    text: textContent
                }
            ]
        };
    }
}
