import { expectTypeOf } from 'expect-type';
import { z } from 'zod';
import { FastMCPAdapter } from '../mcp-adapter';
import type {
    MCPConfig,
    MCPResource,
    MCPResourceTemplate,
    RouteDefinition,
    MCPContext,
    MCPResourceResult,
    MCPContent,
    AdapterStatus
} from '../../../cqrs/types';

// Mock FastMCP
jest.mock('fastmcp', () => ({
    FastMCP: jest.fn().mockImplementation(() => ({
        addTool: jest.fn(),
        addResource: jest.fn(),
        addResourceTemplate: jest.fn(),
        start: jest.fn(),
        stop: jest.fn(),
    }))
}));

// Mock trace generation
jest.mock('../../../cqrs/trace', () => ({
    generateTraceId: jest.fn(() => 'test-trace-id')
}));

describe('FastMCPAdapter', () => {
    let adapter: FastMCPAdapter;
    let mockCQRS: any;
    let mockFastMCP: any;

    const createMockRoute = (overrides: Partial<RouteDefinition> = {}): RouteDefinition => ({
        procedureName: 'testProcedure',
        procedureType: 'command',
        inputSchema: z.object({ name: z.string() }),
        outputSchema: z.object({ id: z.string() }),
        metadata: { mcp: { enabled: true } },
        handler: jest.fn(),
        ...overrides
    });

    beforeEach(async () => {
        jest.clearAllMocks();

        mockCQRS = {
            executeWithContext: jest.fn()
        };

        const { FastMCP } = await import('fastmcp');
        mockFastMCP = {
            addTool: jest.fn(),
            addResource: jest.fn(),
            addResourceTemplate: jest.fn(),
            start: jest.fn(),
            stop: jest.fn(),
        };

        (FastMCP as any).mockImplementation(() => mockFastMCP);
        adapter = new FastMCPAdapter({ cqrs: mockCQRS });
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('constructor', () => {
        it('should initialize with correct name and default status', () => {
            expect(adapter.name).toBe('fastmcp-adapter');
            expect(adapter.status).toBe('stopped');
        });

        it('should store CQRS reference', () => {
            expect((adapter as any)._cqrs).toBe(mockCQRS);
        });
    });

    describe('configure', () => {
        it('should configure with minimal config', async () => {
            const config: MCPConfig = { name: 'Test Server' };
            await adapter.configure(config);

            expect(adapter.status).toBe('configured');
            expect((adapter as any)._config).toEqual(config);
            expect((adapter as any)._fastMCP).toBeDefined();
        });

        it('should configure with full config', async () => {
            const config: MCPConfig = {
                name: 'Test Server',
                version: '2.0.0',
                instructions: 'Test instructions',
                transportType: 'httpStream',
                httpStream: { port: 8080 }
            };

            await adapter.configure(config);
            expect(adapter.status).toBe('configured');
        });

        it('should use default values when not provided', async () => {
            const config: MCPConfig = {};
            await adapter.configure(config);
            expect(adapter.status).toBe('configured');
        });
    });

    describe('registerRoutes', () => {
        beforeEach(async () => {
            await adapter.configure({ name: 'Test Server' });
        });

        it('should throw error if not configured', async () => {
            const unconfiguredAdapter = new FastMCPAdapter({ cqrs: mockCQRS });
            await expect(unconfiguredAdapter.registerRoutes([])).rejects.toThrow(
                'MCP adapter not configured. Call configure() first.'
            );
        });

        it('should register MCP routes only', async () => {
            const routes = [
                createMockRoute({
                    procedureName: 'createUser',
                    metadata: { mcp: { enabled: true } }
                }),
                createMockRoute({
                    procedureName: 'getUser',
                    metadata: { mcp: null } // Not an MCP route
                })
            ];

            await adapter.registerRoutes(routes);
            expect(mockFastMCP.addTool).toHaveBeenCalledTimes(1);
        });

        it('should set readOnlyHint for queries by default', async () => {
            const route = createMockRoute({
                procedureName: 'getUser',
                procedureType: 'query',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            expect(mockFastMCP.addTool).toHaveBeenCalledWith(
                expect.objectContaining({
                    annotations: expect.objectContaining({
                        readOnlyHint: true
                    })
                })
            );
        });

        it('should handle all MCP metadata properties', async () => {
            const route = createMockRoute({
                procedureName: 'updateUser',
                metadata: {
                    description: 'Update user information',
                    mcp: {
                        enabled: true,
                        streamingHint: true,
                        readOnlyHint: false,
                        destructiveHint: true,
                        idempotentHint: false,
                        openWorldHint: true,
                        timeoutMs: 5000
                    }
                }
            });

            await adapter.registerRoutes([route]);

            expect(mockFastMCP.addTool).toHaveBeenCalledWith(
                expect.objectContaining({
                    name: 'updateUser',
                    description: 'Update user information',
                    timeoutMs: 5000,
                    annotations: {
                        readOnlyHint: false,
                        idempotentHint: false,
                        destructiveHint: true,
                        title: 'updateUser',
                        streamingHint: true,
                        openWorldHint: true
                    }
                })
            );
        });

        it('should apply smart defaults for query procedures', async () => {
            const queryRoute = createMockRoute({
                procedureType: 'query',
                metadata: {
                    title: 'Get User Data',
                    mcp: {
                        enabled: true,
                        // No explicit hints - should use smart defaults
                    }
                }
            });

            await adapter.registerRoutes([queryRoute]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            expect(addToolCall.annotations).toEqual({
                readOnlyHint: true,      // Smart default for queries
                idempotentHint: true,    // Smart default for queries
                destructiveHint: false,  // Conservative default
                title: 'Get User Data',  // From general metadata
            });
        });

        it('should apply smart defaults for command procedures', async () => {
            const commandRoute = createMockRoute({
                procedureType: 'command',
                metadata: {
                    title: 'Create User',
                    mcp: {
                        enabled: true,
                        streamingHint: true,
                        // No explicit hints - should use smart defaults
                    }
                }
            });

            await adapter.registerRoutes([commandRoute]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            expect(addToolCall.annotations).toEqual({
                readOnlyHint: false,     // Smart default for commands
                idempotentHint: false,   // Smart default for commands
                destructiveHint: false,  // Conservative default
                title: 'Create User',    // From general metadata
                streamingHint: true,     // User override
            });
        });

        it('should allow explicit overrides of smart defaults', async () => {
            const queryRoute = createMockRoute({
                procedureType: 'query',
                metadata: {
                    title: 'Special Query',
                    mcp: {
                        enabled: true,
                        readOnlyHint: false,     // Override smart default
                        idempotentHint: false,   // Override smart default
                        destructiveHint: true,   // Override conservative default
                    }
                }
            });

            await adapter.registerRoutes([queryRoute]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            expect(addToolCall.annotations).toEqual({
                readOnlyHint: false,     // User override
                idempotentHint: false,   // User override
                destructiveHint: true,   // User override
                title: 'Special Query',  // From general metadata
            });
        });

        it('should use procedure name as title fallback', async () => {
            const route = createMockRoute({
                procedureName: 'createSpecialUser',
                metadata: {
                    // No title in general metadata
                    mcp: {
                        enabled: true,
                    }
                }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            expect(addToolCall.annotations.title).toBe('createSpecialUser');
        });
    });

    describe('registerResources', () => {
        beforeEach(async () => {
            await adapter.configure({ name: 'Test Server' });
        });

        it('should throw error if not configured', async () => {
            const unconfiguredAdapter = new FastMCPAdapter({ cqrs: mockCQRS });
            await expect(unconfiguredAdapter.registerResources([])).rejects.toThrow(
                'MCP adapter not configured. Call configure() first.'
            );
        });

        it('should register resources with sync load function', async () => {
            const resources: MCPResource[] = [{
                uri: 'test://resource1',
                name: 'Test Resource',
                description: 'A test resource',
                mimeType: 'text/plain',
                load: () => ({ text: 'Hello World' })
            }];

            await adapter.registerResources(resources);
            expect(mockFastMCP.addResource).toHaveBeenCalledTimes(1);

            const addResourceCall = mockFastMCP.addResource.mock.calls[0][0];
            expect(addResourceCall.uri).toBe('test://resource1');
            expect(addResourceCall.name).toBe('Test Resource');
            expect(addResourceCall.mimeType).toBe('text/plain');
        });

        it('should register resources with async load function', async () => {
            const resources: MCPResource[] = [{
                uri: 'test://resource2',
                name: 'Async Resource',
                mimeType: 'application/json',
                load: async () => ({
                    text: '{"key": "value"}',
                    mimeType: 'application/json'
                })
            }];

            await adapter.registerResources(resources);
            expect(mockFastMCP.addResource).toHaveBeenCalledTimes(1);

            // Test the wrapped load function
            const addResourceCall = mockFastMCP.addResource.mock.calls[0][0];
            const result = await addResourceCall.load();
            expect(result).toEqual({
                text: '{"key": "value"}',
                mimeType: 'application/json'
            });
        });

        it('should handle resources with blob data', async () => {
            const resources: MCPResource[] = [{
                uri: 'test://binary',
                name: 'Binary Resource',
                mimeType: 'image/png',
                load: () => ({
                    blob: 'base64encodeddata',
                    mimeType: 'image/png'
                })
            }];

            await adapter.registerResources(resources);

            const addResourceCall = mockFastMCP.addResource.mock.calls[0][0];
            const result = await addResourceCall.load();
            expect(result).toEqual({
                text: '',
                blob: 'base64encodeddata',
                mimeType: 'image/png'
            });
        });

        it('should handle resources with only text', async () => {
            const resources: MCPResource[] = [{
                uri: 'test://text-only',
                name: 'Text Resource',
                load: () => ({ text: 'Simple text content' })
            }];

            await adapter.registerResources(resources);

            const addResourceCall = mockFastMCP.addResource.mock.calls[0][0];
            const result = await addResourceCall.load();
            expect(result).toEqual({
                text: 'Simple text content'
            });
        });
    });

    describe('registerResourceTemplates', () => {
        beforeEach(async () => {
            await adapter.configure({ name: 'Test Server' });
        });

        it('should throw error if not configured', async () => {
            const unconfiguredAdapter = new FastMCPAdapter({ cqrs: mockCQRS });
            await expect(unconfiguredAdapter.registerResourceTemplates([])).rejects.toThrow(
                'MCP adapter not configured. Call configure() first.'
            );
        });

        it('should register resource templates', async () => {
            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://users/{id}',
                name: 'User Template',
                description: 'Get user by ID',
                mimeType: 'application/json',
                arguments: [
                    { name: 'id', description: 'User ID', required: true }
                ],
                load: (args) => ({
                    text: `{"id": "${args.id}", "name": "User ${args.id}"}`
                })
            }];

            await adapter.registerResourceTemplates(templates);

            expect(mockFastMCP.addResourceTemplate).toHaveBeenCalledTimes(1);
            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];
            expect(addTemplateCall.uriTemplate).toBe('test://users/{id}');
            expect(addTemplateCall.name).toBe('User Template');
            expect(addTemplateCall.arguments).toEqual([
                { name: 'id', description: 'User ID', required: true }
            ]);
        });

        it('should handle template load function', async () => {
            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://posts/{id}',
                name: 'Post Template',
                mimeType: 'text/plain',
                arguments: [
                    { name: 'id', required: true }
                ],
                load: async (args) => ({
                    text: `Post content for ${args.id}`,
                    mimeType: 'text/markdown'
                })
            }];

            await adapter.registerResourceTemplates(templates);

            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];
            const result = await addTemplateCall.load({ id: '123' });
            expect(result).toEqual({
                text: 'Post content for 123',
                mimeType: 'text/markdown'
            });
        });

        it('should handle templates with multiple arguments', async () => {
            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://data/{type}/{id}',
                name: 'Data Template',
                arguments: [
                    { name: 'type', description: 'Data type', required: true },
                    { name: 'id', required: true },
                    { name: 'format', description: 'Output format', required: false }
                ],
                load: (args) => ({
                    text: `Data: ${args.type}/${args.id} (${args.format || 'default'})`
                })
            }];

            await adapter.registerResourceTemplates(templates);

            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];
            expect(addTemplateCall.arguments).toHaveLength(3);
            expect(addTemplateCall.arguments[2]).toEqual({
                name: 'format',
                description: 'Output format',
                required: false
            });
        });
    });

    describe('supportsProtocol', () => {
        it('should return true for mcp protocol', () => {
            expect(adapter.supportsProtocol('mcp')).toBe(true);
        });
    });

    describe('start and stop', () => {
        beforeEach(async () => {
            await adapter.configure({ name: 'Test Server' });
        });

        it('should throw error if not configured', async () => {
            const unconfiguredAdapter = new FastMCPAdapter({ cqrs: mockCQRS });
            await expect(unconfiguredAdapter.start()).rejects.toThrow(
                'MCP adapter not configured. Call configure() first.'
            );
        });

        it('should start with stdio transport by default', async () => {
            await adapter.start();

            expect(adapter.status).toBe('running');
            expect(mockFastMCP.start).toHaveBeenCalledTimes(1);
        });

        it('should start with HTTP stream transport', async () => {
            await adapter.configure({
                name: 'Test Server',
                transportType: 'httpStream',
                httpStream: { port: 8080 }
            });

            await adapter.start();

            expect(adapter.status).toBe('running');
            expect(mockFastMCP.start).toHaveBeenCalledTimes(1);
        });

        it('should stop when running', async () => {
            await adapter.start();
            expect(adapter.status).toBe('running');

            await adapter.stop();

            expect(adapter.status).toBe('stopped');
            expect(mockFastMCP.stop).toHaveBeenCalledTimes(1);
        });

        it('should handle stop when not running', async () => {
            expect(adapter.status).toBe('configured');

            await adapter.stop();

            expect(adapter.status).toBe('configured');
            expect(mockFastMCP.stop).not.toHaveBeenCalled();
        });

        it('should handle multiple start/stop cycles', async () => {
            await adapter.start();
            expect(adapter.status).toBe('running');

            await adapter.stop();
            expect(adapter.status).toBe('stopped');

            await adapter.start();
            expect(adapter.status).toBe('running');

            expect(mockFastMCP.start).toHaveBeenCalledTimes(2);
            expect(mockFastMCP.stop).toHaveBeenCalledTimes(1);
        });
    });

    describe('getHealth', () => {
        it('should return healthy when running', async () => {
            await adapter.configure({ name: 'Test Server' });
            await adapter.start();

            const health = await adapter.getHealth();

            expect(health).toEqual({
                status: 'healthy',
                timestamp: expect.any(Number),
                details: {
                    transport: 'stdio'
                }
            });
        });

        it('should return unhealthy when not running', async () => {
            const health = await adapter.getHealth();

            expect(health).toEqual({
                status: 'unhealthy',
                timestamp: expect.any(Number),
                details: {
                    transport: 'stdio'
                }
            });
        });

        it('should include HTTP stream details', async () => {
            await adapter.configure({
                name: 'Test Server',
                transportType: 'httpStream',
                httpStream: { port: 8080 }
            });

            const health = await adapter.getHealth();

            expect(health.details).toBeDefined();
            expect(health.details!).toEqual({
                transport: 'httpStream',
                httpStream: { port: 8080 }
            });
        });

        it('should handle health check when configured but not started', async () => {
            await adapter.configure({ name: 'Test Server' });

            const health = await adapter.getHealth();

            expect(health.status).toBe('unhealthy');
            expect(health.details).toBeDefined();
            expect(health.details!.transport).toBe('stdio');
        });
    });

    describe('tool execution integration', () => {
        beforeEach(async () => {
            await adapter.configure({ name: 'Test Server' });
            mockCQRS.executeWithContext.mockResolvedValue({ id: '123' });
        });

        it('should register tool with execute function', async () => {
            const route = createMockRoute({
                procedureName: 'testCommand',
                metadata: {
                    mcp: {
                        enabled: true,
                    }
                }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            expect(addToolCall.execute).toBeInstanceOf(Function);
        });

        it('should execute tool and call CQRS', async () => {
            const route = createMockRoute({
                procedureName: 'testCommand',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: jest.fn(),
                reportProgress: jest.fn(),
                log: { debug: jest.fn(), info: jest.fn(), warn: jest.fn(), error: jest.fn() }
            };

            await addToolCall.execute({ name: 'test' }, mockMCPContext);

            expect(mockCQRS.executeWithContext).toHaveBeenCalledWith(
                'testCommand',
                { name: 'test' },
                expect.objectContaining({
                    traceId: 'test-trace-id',
                    procedureName: 'testCommand'
                })
            );
        });

        it('should handle MCP context methods', async () => {
            const route = createMockRoute({
                procedureName: 'streamingCommand',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: jest.fn(),
                reportProgress: jest.fn(),
                log: { debug: jest.fn(), info: jest.fn(), warn: jest.fn(), error: jest.fn() }
            };

            // Execute the tool to get the MCP context wrapper
            await addToolCall.execute({ name: 'test' }, mockMCPContext);

            // Get the MCP context passed to CQRS
            const contextArg = mockCQRS.executeWithContext.mock.calls[0][2];
            const mcpWrapper = contextArg.mcp;

            expect(mcpWrapper).toBeDefined();
            expect(typeof mcpWrapper.streamContent).toBe('function');
            expect(mcpWrapper.reportProgress).toBe(mockMCPContext.reportProgress);
        });

        it('should handle execution errors', async () => {
            const route = createMockRoute({
                procedureName: 'failingCommand',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: jest.fn(),
                reportProgress: jest.fn(),
                log: { debug: jest.fn(), info: jest.fn(), warn: jest.fn(), error: jest.fn() }
            };

            const error = new Error('Execution failed');
            mockCQRS.executeWithContext.mockRejectedValue(error);

            await expect(addToolCall.execute({ name: 'test' }, mockMCPContext)).rejects.toThrow('Execution failed');
        });

        it('should use default transformation when no transformer provided', async () => {
            const route = createMockRoute({
                procedureName: 'simpleCommand',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: jest.fn(),
                reportProgress: jest.fn(),
                log: { debug: jest.fn(), info: jest.fn(), warn: jest.fn(), error: jest.fn() }
            };

            mockCQRS.executeWithContext.mockResolvedValue('simple result');

            const result = await addToolCall.execute({ value: 'test' }, mockMCPContext);

            expect(result).toEqual({
                content: [{ type: 'text', text: 'simple result' }]
            });
        });

        it('should use _mcp key when provided', async () => {
            const route = createMockRoute({
                procedureName: 'mcpKeyCommand',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: jest.fn(),
                reportProgress: jest.fn()
            };

            mockCQRS.executeWithContext.mockResolvedValue({ _mcp: { type: 'text', text: 'Special content' } });

            const result = await addToolCall.execute({ input: 'test' }, mockMCPContext);

            expect(result).toEqual({
                content: [{ type: 'text', text: 'Special content' }]
            });
        });
    });

    describe('type tests', () => {
        it('should have correct types for resources', () => {
            const resource: MCPResource = {
                uri: 'test://resource',
                name: 'Test Resource',
                description: 'Optional description',
                mimeType: 'text/plain',
                load: () => ({ text: 'content' })
            };

            expectTypeOf(resource.uri).toEqualTypeOf<string>();
            expectTypeOf(resource.name).toEqualTypeOf<string>();
            expectTypeOf(resource.description).toEqualTypeOf<string | undefined>();
            expectTypeOf(resource.mimeType).toEqualTypeOf<string | undefined>();
            expectTypeOf(resource.load).toEqualTypeOf<() => Promise<MCPResourceResult> | MCPResourceResult>();
        });

        it('should have correct types for resource templates', () => {
            const template: MCPResourceTemplate = {
                uriTemplate: 'test://template/{id}',
                name: 'Test Template',
                mimeType: 'application/json',
                arguments: [
                    { name: 'id', required: true },
                    { name: 'format', description: 'Output format', required: false }
                ],
                load: (args) => ({ text: `Content for ${args.id}` })
            };

            expectTypeOf(template.uriTemplate).toEqualTypeOf<string>();
            expectTypeOf(template.name).toEqualTypeOf<string>();
            expectTypeOf(template.arguments).toEqualTypeOf<MCPResourceTemplate['arguments']>();
            expectTypeOf(template.load).toEqualTypeOf<(args: Record<string, string>) => Promise<MCPResourceResult> | MCPResourceResult>();
        });

        it('should have correct types for MCP context', () => {
            const mockContext: MCPContext = {
                streamContent: async (content) => {},
                reportProgress: async (progress) => {},
            };

            expectTypeOf(mockContext.streamContent).toEqualTypeOf<(content: MCPContent | MCPContent[]) => Promise<void>>();
            expectTypeOf(mockContext.reportProgress).toEqualTypeOf<(progress: { progress: number; total?: number }) => Promise<void>>();
        });

        it('should have correct adapter status types', () => {
            expectTypeOf(adapter.status).toEqualTypeOf<AdapterStatus>();
            expectTypeOf(adapter.name).toEqualTypeOf<string>();
        });

        it('should have correct config types', () => {
            const config: MCPConfig = {
                name: 'Test Server',
                version: '1.0.0',
                transportType: 'httpStream',
                httpStream: { port: 8080 },
                instructions: 'Test instructions'
            };

            expectTypeOf(config.name).toEqualTypeOf<string | undefined>();
            expectTypeOf(config.version).toEqualTypeOf<string | undefined>();
            expectTypeOf(config.transportType).toEqualTypeOf<'stdio' | 'httpStream' | undefined>();
            expectTypeOf(config.httpStream).toEqualTypeOf<{ port?: number; endpoint?: string; } | undefined>();
        });
    });

    describe('edge cases and error handling', () => {
        it('should handle empty routes array', async () => {
            await adapter.configure({ name: 'Test Server' });
            await adapter.registerRoutes([]);
            expect(mockFastMCP.addTool).not.toHaveBeenCalled();
        });

        it('should handle empty resources array', async () => {
            await adapter.configure({ name: 'Test Server' });
            await adapter.registerResources([]);
            expect(mockFastMCP.addResource).not.toHaveBeenCalled();
        });

        it('should handle empty resource templates array', async () => {
            await adapter.configure({ name: 'Test Server' });
            await adapter.registerResourceTemplates([]);
            expect(mockFastMCP.addResourceTemplate).not.toHaveBeenCalled();
        });

        it('should handle routes with missing MCP metadata', async () => {
            await adapter.configure({ name: 'Test Server' });
            const route = createMockRoute({
                metadata: { mcp: null } // Explicitly null MCP metadata
            });

            await adapter.registerRoutes([route]);
            expect(mockFastMCP.addTool).not.toHaveBeenCalled();
        });

        it('should handle FastMCP initialization errors', async () => {
            const { FastMCP } = await import('fastmcp');
            (FastMCP as any).mockImplementation(() => {
                throw new Error('FastMCP initialization failed');
            });

            const errorAdapter = new FastMCPAdapter({ cqrs: mockCQRS });
            await expect(errorAdapter.configure({ name: 'Test' })).rejects.toThrow('FastMCP initialization failed');
        });

        it('should handle start errors', async () => {
            await adapter.configure({ name: 'Test Server' });
            mockFastMCP.start.mockRejectedValue(new Error('Start failed'));

            await expect(adapter.start()).rejects.toThrow('Start failed');
        });

        it('should handle stop errors gracefully', async () => {
            await adapter.configure({ name: 'Test Server' });
            await adapter.start();

            mockFastMCP.stop.mockRejectedValue(new Error('Stop failed'));

            await expect(adapter.stop()).rejects.toThrow('Stop failed');
        });

        it('should validate configuration errors in configure', async () => {
            // Test coverage for configure error handling
            const { FastMCP } = await import('fastmcp');
            (FastMCP as any).mockImplementation(() => {
                throw new Error('Invalid configuration');
            });

            await expect(adapter.configure({ name: 'Test' })).rejects.toThrow();
            expect(adapter.status).toBe('stopped');
        });

        it('should handle graceful shutdown when not started', async () => {
            await adapter.configure({ name: 'Test Server' });
            // Stop without starting - should handle gracefully
            await adapter.stop();
            expect(adapter.status).toBe('configured');
        });

        it('should handle default config when config is empty', async () => {
            await adapter.configure({});
            expect(adapter.status).toBe('configured');
            expect((adapter as any)._config).toEqual({});
        });

        it('should handle resource loading errors', async () => {
            await adapter.configure({ name: 'Test Server' });

            const resources: MCPResource[] = [{
                uri: 'test://failing-resource',
                name: 'Failing Resource',
                load: () => {
                    throw new Error('Load failed');
                }
            }];

            await adapter.registerResources(resources);

            const addResourceCall = mockFastMCP.addResource.mock.calls[0][0];

            // The wrapped load function should propagate the error
            await expect(addResourceCall.load()).rejects.toThrow('Load failed');
        });

        it('should handle resource template loading errors', async () => {
            await adapter.configure({ name: 'Test Server' });

            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://failing/{id}',
                name: 'Failing Template',
                arguments: [{ name: 'id', required: true }],
                load: (args) => {
                    throw new Error('Template load failed');
                }
            }];

            await adapter.registerResourceTemplates(templates);

            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];

            // The wrapped load function should propagate the error
            await expect(addTemplateCall.load({ id: '123' })).rejects.toThrow('Template load failed');
        });

        it('should handle tool execution with missing MCP context methods', async () => {
            await adapter.configure({ name: 'Test Server' });
            mockCQRS.executeWithContext.mockResolvedValue({ result: 'success' });

            const route = createMockRoute({
                procedureName: 'testCommand',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];

            // Call with minimal MCP context (missing optional methods)
            const minimalMCPContext = {
                reportProgress: jest.fn(),
                // Missing streamContent - should still work
            };

            const result = await addToolCall.execute({ name: 'test' }, minimalMCPContext);

            expect(result).toBeDefined();
            expect(mockCQRS.executeWithContext).toHaveBeenCalled();
        });

        it('should handle complex MCP content types in tool responses', async () => {
            await adapter.configure({ name: 'Test Server' });

            const route = createMockRoute({
                procedureName: 'complexResponse',
                metadata: { mcp: { enabled: true } }
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: jest.fn(),
                reportProgress: jest.fn()
            };

            // Test with _mcp containing multiple content types
            mockCQRS.executeWithContext.mockResolvedValue({
                _mcp: [
                    { type: 'text', text: 'First part' },
                    { type: 'image', data: 'base64data', mimeType: 'image/png' }
                ]
            });

            const result = await addToolCall.execute({ input: 'test' }, mockMCPContext);

            expect(result).toEqual({
                content: [
                    { type: 'text', text: 'First part' },
                    { type: 'image', data: 'base64data', mimeType: 'image/png' }
                ]
            });
        });
    });

    describe('MCP Context Integration', () => {
        beforeEach(async () => {
            await adapter.configure({ name: 'Test Server' });
        });

        it('should provide streamContent functionality to handlers', async () => {
            const streamContentSpy = jest.fn();
            const route = createMockRoute({
                procedureName: 'streamingCommand',
                metadata: { mcp: { enabled: true, streamingHint: true } }
            });

            // Mock CQRS to capture and use MCP context
            mockCQRS.executeWithContext.mockImplementation(async (name, input, context) => {
                const { mcp } = context;

                // Test streaming different content types
                await mcp.streamContent([{ type: 'text', text: 'First chunk' }]);
                await mcp.streamContent([
                    { type: 'text', text: 'Second chunk' },
                    { type: 'image', data: 'base64data', mimeType: 'image/png' }
                ]);

                return { success: true };
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: streamContentSpy,
                reportProgress: jest.fn()
            };

            await addToolCall.execute({ data: 'test' }, mockMCPContext);

            expect(streamContentSpy).toHaveBeenCalledTimes(2);
            expect(streamContentSpy).toHaveBeenNthCalledWith(1, [{ type: 'text', text: 'First chunk' }]);
            expect(streamContentSpy).toHaveBeenNthCalledWith(2, [
                { type: 'text', text: 'Second chunk' },
                { type: 'image', data: 'base64data', mimeType: 'image/png' }
            ]);
        });

        it('should provide reportProgress functionality to handlers', async () => {
            const reportProgressSpy = jest.fn();
            const route = createMockRoute({
                procedureName: 'progressCommand',
                metadata: { mcp: { enabled: true } }
            });

            mockCQRS.executeWithContext.mockImplementation(async (name, input, context) => {
                const { mcp } = context;

                // Test different progress reporting scenarios
                await mcp.reportProgress({ progress: 25, total: 100 });
                await mcp.reportProgress({ progress: 50 });
                await mcp.reportProgress({ progress: 100, total: 100 });

                return { complete: true };
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            const mockMCPContext = {
                streamContent: jest.fn(),
                reportProgress: reportProgressSpy
            };

            await addToolCall.execute({ task: 'process' }, mockMCPContext);

            expect(reportProgressSpy).toHaveBeenCalledTimes(3);
            expect(reportProgressSpy).toHaveBeenNthCalledWith(1, { progress: 25, total: 100 });
            expect(reportProgressSpy).toHaveBeenNthCalledWith(2, { progress: 50 });
            expect(reportProgressSpy).toHaveBeenNthCalledWith(3, { progress: 100, total: 100 });
        });

        it('should handle _mcp property in complex response structures', async () => {
            const route = createMockRoute({
                procedureName: 'complexMCPResponse',
                metadata: { mcp: { enabled: true } }
            });

            const testCases = [
                {
                    name: 'single text content',
                    response: { _mcp: { type: 'text', text: 'Simple response' } },
                    expected: { content: [{ type: 'text', text: 'Simple response' }] }
                },
                {
                    name: 'array of mixed content',
                    response: {
                        _mcp: [
                            { type: 'text', text: 'Text part' },
                            { type: 'resource', resource: { uri: 'file://test.txt', text: 'File content' } }
                        ]
                    },
                    expected: {
                        content: [
                            { type: 'text', text: 'Text part' },
                            { type: 'resource', resource: { uri: 'file://test.txt', text: 'File content' } }
                        ]
                    }
                },
                {
                    name: 'nested object with _mcp',
                    response: {
                        data: { value: 123 },
                        _mcp: { type: 'text', text: 'Additional info' }
                    },
                    expected: { content: [{ type: 'text', text: 'Additional info' }] }
                }
            ];

            await adapter.registerRoutes([route]);
            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];

            for (const testCase of testCases) {
                mockCQRS.executeWithContext.mockResolvedValue(testCase.response);

                const result = await addToolCall.execute({ test: testCase.name }, {
                    streamContent: jest.fn(),
                    reportProgress: jest.fn()
                });

                expect(result).toEqual(testCase.expected);
            }
        });

        it('should handle missing MCP context methods gracefully', async () => {
            const route = createMockRoute({
                procedureName: 'robustCommand',
                metadata: { mcp: { enabled: true } }
            });

            mockCQRS.executeWithContext.mockImplementation(async (name, input, context) => {
                const { mcp } = context;

                // Should not throw even if methods are missing
                try {
                    await mcp.streamContent?.({ type: 'text', text: 'Optional stream' });
                    await mcp.reportProgress?.({ progress: 50 });
                } catch (error) {
                    // Should handle gracefully
                }

                return { robust: true };
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];

            // Provide minimal context
            const minimalContext = {
                // Missing streamContent and reportProgress
            };

            const result = await addToolCall.execute({ test: 'minimal' }, minimalContext);

            expect(result).toEqual({
                content: [{ type: 'text', text: JSON.stringify({ robust: true }, null, 2) }]
            });
        });

        it('should preserve origin information in MCP context', async () => {
            const route = createMockRoute({
                procedureName: 'originAwareCommand',
                metadata: { mcp: { enabled: true } }
            });

            mockCQRS.executeWithContext.mockImplementation(async (name, input, context) => {
                // Verify origin is set correctly
                expect(context.origin).toBe('MCP');
                return { origin: context.origin };
            });

            await adapter.registerRoutes([route]);

            const addToolCall = mockFastMCP.addTool.mock.calls[0][0];
            await addToolCall.execute({ test: 'origin' }, {
                streamContent: jest.fn(),
                reportProgress: jest.fn()
            });

            expect(mockCQRS.executeWithContext).toHaveBeenCalledWith(
                'originAwareCommand',
                { test: 'origin' },
                expect.objectContaining({
                    origin: 'MCP'
                })
            );
        });
    });

    describe('Resource Template Complex Scenarios', () => {
        beforeEach(async () => {
            await adapter.configure({ name: 'Test Server' });
        });

        it('should handle resource templates with many arguments (10+ arguments)', async () => {
            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://complex/{type}/{id}/{format}/{version}/{env}/{region}/{shard}/{tenant}/{namespace}/{endpoint}',
                name: 'Complex Resource Template',
                description: 'Template with many arguments',
                mimeType: 'application/json',
                arguments: [
                    { name: 'type', description: 'Resource type', required: true },
                    { name: 'id', description: 'Resource ID', required: true },
                    { name: 'format', description: 'Output format', required: false },
                    { name: 'version', description: 'API version', required: true },
                    { name: 'env', description: 'Environment', required: false },
                    { name: 'region', description: 'Geographic region', required: true },
                    { name: 'shard', description: 'Shard identifier', required: false },
                    { name: 'tenant', description: 'Tenant ID', required: true },
                    { name: 'namespace', description: 'Namespace', required: false },
                    { name: 'endpoint', description: 'Endpoint path', required: true }
                ],
                load: async (args) => {
                    const baseResult = `Resource: ${args.type}/${args.id} in ${args.region}/${args.tenant}/${args.endpoint}`;
                    const optionalParts = [
                        args.format && `format=${args.format}`,
                        args.env && `env=${args.env}`,
                        args.shard && `shard=${args.shard}`,
                        args.namespace && `namespace=${args.namespace}`
                    ].filter(Boolean);

                    return {
                        text: JSON.stringify({
                            base: baseResult,
                            optional: optionalParts,
                            version: args.version
                        }),
                        mimeType: 'application/json'
                    };
                }
            }];

            await adapter.registerResourceTemplates(templates);

            expect(mockFastMCP.addResourceTemplate).toHaveBeenCalledTimes(1);
            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];

            expect(addTemplateCall.arguments).toHaveLength(10);
            expect(addTemplateCall.arguments.filter(arg => arg.required)).toHaveLength(6);
            expect(addTemplateCall.arguments.filter(arg => !arg.required)).toHaveLength(4);

            // Test the load function with complex arguments
            const result = await addTemplateCall.load({
                type: 'user',
                id: '123',
                format: 'json',
                version: 'v2',
                env: 'prod',
                region: 'us-east-1',
                shard: 'shard-1',
                tenant: 'acme-corp',
                namespace: 'default',
                endpoint: 'api/users'
            });

            const parsedResult = JSON.parse(result.text!);
            expect(parsedResult.base).toBe('Resource: user/123 in us-east-1/acme-corp/api/users');
            expect(parsedResult.optional).toEqual(['format=json', 'env=prod', 'shard=shard-1', 'namespace=default']);
            expect(parsedResult.version).toBe('v2');
        });

        it('should handle resource templates with complex argument validation', async () => {
            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://validated/{category}/{subcategory}/{item}',
                name: 'Validated Template',
                arguments: [
                    { name: 'category', description: 'Must be one of: users, products, orders', required: true },
                    { name: 'subcategory', description: 'Depends on category', required: true },
                    { name: 'item', description: 'Item identifier', required: true },
                    { name: 'includeMetadata', description: 'Include metadata (true/false)', required: false },
                    { name: 'fields', description: 'Comma-separated field list', required: false }
                ],
                load: async (args) => {
                    // Simulate validation logic
                    const validCategories = ['users', 'products', 'orders'];
                    if (!validCategories.includes(args.category)) {
                        throw new Error(`Invalid category: ${args.category}. Must be one of: ${validCategories.join(', ')}`);
                    }

                    const validSubcategories: Record<string, string[]> = {
                        users: ['profiles', 'settings', 'activities'],
                        products: ['catalog', 'inventory', 'reviews'],
                        orders: ['pending', 'completed', 'cancelled']
                    };

                    if (!validSubcategories[args.category]?.includes(args.subcategory)) {
                        throw new Error(`Invalid subcategory: ${args.subcategory} for category: ${args.category}`);
                    }

                    const includeMetadata = args.includeMetadata === 'true';
                    const fields = args.fields ? args.fields.split(',').map(f => f.trim()) : null;

                    const response = {
                        category: args.category,
                        subcategory: args.subcategory,
                        item: args.item,
                        data: `Mock data for ${args.category}/${args.subcategory}/${args.item}`,
                        ...(includeMetadata && {
                            metadata: {
                                timestamp: new Date().toISOString(),
                                source: 'test-template'
                            }
                        }),
                        ...(fields && { selectedFields: fields })
                    };

                    return {
                        text: JSON.stringify(response, null, 2),
                        mimeType: 'application/json'
                    };
                }
            }];

            await adapter.registerResourceTemplates(templates);
            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];

            // Test valid arguments
            const validResult = await addTemplateCall.load({
                category: 'users',
                subcategory: 'profiles',
                item: 'user-123',
                includeMetadata: 'true',
                fields: 'name, email, createdAt'
            });

            const validParsed = JSON.parse(validResult.text!);
            expect(validParsed.category).toBe('users');
            expect(validParsed.subcategory).toBe('profiles');
            expect(validParsed.metadata).toBeDefined();
            expect(validParsed.selectedFields).toEqual(['name', 'email', 'createdAt']);

            // Test invalid category
            await expect(addTemplateCall.load({
                category: 'invalid',
                subcategory: 'profiles',
                item: 'user-123'
            })).rejects.toThrow('Invalid category: invalid');

            // Test invalid subcategory
            await expect(addTemplateCall.load({
                category: 'users',
                subcategory: 'invalid',
                item: 'user-123'
            })).rejects.toThrow('Invalid subcategory: invalid for category: users');
        });

        it('should handle resource templates with dynamic content generation', async () => {
            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://dynamic/{type}/{count}',
                name: 'Dynamic Content Generator',
                arguments: [
                    { name: 'type', description: 'Type of content to generate', required: true },
                    { name: 'count', description: 'Number of items to generate', required: true },
                    { name: 'seed', description: 'Random seed for reproducible results', required: false }
                ],
                load: async (args) => {
                    const count = parseInt(args.count, 10);
                    if (isNaN(count) || count < 1 || count > 100) {
                        throw new Error('Count must be a number between 1 and 100');
                    }

                    const seed = args.seed ? parseInt(args.seed, 10) : Date.now();
                    const random = (index: number) => {
                        // Simple deterministic pseudo-random function
                        return ((seed + index) * 9301 + 49297) % 233280 / 233280;
                    };

                    const generators: Record<string, (index: number) => any> = {
                        users: (index) => ({
                            id: `user-${index + 1}`,
                            name: `User ${index + 1}`,
                            email: `user${index + 1}@example.com`,
                            score: Math.floor(random(index) * 100)
                        }),
                        products: (index) => ({
                            id: `prod-${index + 1}`,
                            name: `Product ${index + 1}`,
                            price: Math.floor(random(index) * 1000) + 1,
                            category: ['electronics', 'books', 'clothing'][Math.floor(random(index) * 3)]
                        }),
                        events: (index) => ({
                            id: `event-${index + 1}`,
                            type: ['click', 'view', 'purchase'][Math.floor(random(index) * 3)],
                            timestamp: new Date(Date.now() - Math.floor(random(index) * 86400000)).toISOString(),
                            value: Math.floor(random(index) * 50)
                        })
                    };

                    if (!generators[args.type]) {
                        throw new Error(`Unsupported type: ${args.type}. Available types: ${Object.keys(generators).join(', ')}`);
                    }

                    const items = Array.from({ length: count }, (_, index) => generators[args.type](index));

                    return {
                        text: JSON.stringify({
                            type: args.type,
                            count,
                            seed,
                            items
                        }, null, 2),
                        mimeType: 'application/json'
                    };
                }
            }];

            await adapter.registerResourceTemplates(templates);
            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];

            // Test dynamic content generation
            const result1 = await addTemplateCall.load({
                type: 'users',
                count: '3',
                seed: '12345'
            });

            const parsed1 = JSON.parse(result1.text!);
            expect(parsed1.items).toHaveLength(3);
            expect(parsed1.items[0]).toMatchObject({
                id: 'user-1',
                name: 'User 1',
                email: '<EMAIL>'
            });

            // Test reproducibility with same seed
            const result2 = await addTemplateCall.load({
                type: 'users',
                count: '3',
                seed: '12345'
            });

            const parsed2 = JSON.parse(result2.text!);
            expect(parsed2.items).toEqual(parsed1.items); // Should be identical

            // Test different types
            const productResult = await addTemplateCall.load({
                type: 'products',
                count: '2'
            });

            const productParsed = JSON.parse(productResult.text!);
            expect(productParsed.items).toHaveLength(2);
            expect(productParsed.items[0]).toHaveProperty('price');
            expect(productParsed.items[0]).toHaveProperty('category');

            // Test error cases
            await expect(addTemplateCall.load({
                type: 'users',
                count: '101' // Too many
            })).rejects.toThrow('Count must be a number between 1 and 100');

            await expect(addTemplateCall.load({
                type: 'invalid-type',
                count: '5'
            })).rejects.toThrow('Unsupported type: invalid-type');
        });

        it('should handle resource templates with binary and text content combinations', async () => {
            const templates: MCPResourceTemplate[] = [{
                uriTemplate: 'test://mixed/{contentType}/{id}',
                name: 'Mixed Content Template',
                arguments: [
                    { name: 'contentType', description: 'Type of content: text, binary, or mixed', required: true },
                    { name: 'id', description: 'Content identifier', required: true },
                    { name: 'encoding', description: 'Encoding format for binary content', required: false }
                ],
                load: async (args) => {
                    const contentGenerators: Record<string, () => MCPResourceResult> = {
                        text: () => ({
                            text: `Text content for ID: ${args.id}`,
                            mimeType: 'text/plain'
                        }),
                        binary: () => ({
                            blob: Buffer.from(`Binary content for ID: ${args.id}`).toString('base64'),
                            mimeType: 'application/octet-stream'
                        }),
                        json: () => ({
                            text: JSON.stringify({
                                id: args.id,
                                type: 'json',
                                data: `JSON content for ${args.id}`,
                                encoding: args.encoding || 'utf-8'
                            }),
                            mimeType: 'application/json'
                        }),
                        image: () => ({
                            blob: 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', // 1x1 PNG
                            mimeType: 'image/png'
                        })
                    };

                    if (!contentGenerators[args.contentType]) {
                        throw new Error(`Unsupported content type: ${args.contentType}`);
                    }

                    return contentGenerators[args.contentType]();
                }
            }];

            await adapter.registerResourceTemplates(templates);
            const addTemplateCall = mockFastMCP.addResourceTemplate.mock.calls[0][0];

            // Test text content
            const textResult = await addTemplateCall.load({
                contentType: 'text',
                id: 'doc-123'
            });
            expect(textResult.text).toBe('Text content for ID: doc-123');
            expect(textResult.mimeType).toBe('text/plain');
            expect(textResult.blob).toBeUndefined();

            // Test binary content
            const binaryResult = await addTemplateCall.load({
                contentType: 'binary',
                id: 'file-456'
            });
            expect(binaryResult.blob).toBeDefined();
            expect(binaryResult.mimeType).toBe('application/octet-stream');
            expect(binaryResult.text).toBeFalsy(); // Can be undefined or empty string

            // Test JSON content
            const jsonResult = await addTemplateCall.load({
                contentType: 'json',
                id: 'data-789',
                encoding: 'utf-8'
            });
            expect(jsonResult.text).toBeDefined();
            expect(jsonResult.mimeType).toBe('application/json');
            const jsonData = JSON.parse(jsonResult.text!);
            expect(jsonData.id).toBe('data-789');
            expect(jsonData.encoding).toBe('utf-8');

            // Test image content
            const imageResult = await addTemplateCall.load({
                contentType: 'image',
                id: 'img-001'
            });
            expect(imageResult.blob).toBeDefined();
            expect(imageResult.mimeType).toBe('image/png');
        });
    });
});
