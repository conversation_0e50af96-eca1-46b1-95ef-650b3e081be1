import type { Subscriber, SubscriberOptions, MessageHandler } from './types';
import { DaprServer } from '@dapr/dapr';
import { env } from './env';

type SubscriberImpl = Subscriber & { getServer: () => any | null };

export function createSubscriber(options?: SubscriberOptions): SubscriberImpl {
  const pubsubName = options?.pubsubName ?? env.DEFAULT_PUBSUB_NAME;
  const appPort = String(options?.port ?? env.DEFAULT_APP_PORT);

  const daprHost = options?.daprHost ||
    (options?.daprAddress ? new URL(options.daprAddress).hostname : '') ||
    (env.DEFAULT_DAPR_ADDRESS ? new URL(env.DEFAULT_DAPR_ADDRESS).hostname : '0.0.0.0');

  const daprPort = String(options?.daprPort ||
    (options?.daprAddress ? new URL(options.daprAddress).port || '3500' : '') ||
    (env.DEFAULT_DAPR_ADDRESS ? new URL(env.DEFAULT_DAPR_ADDRESS).port || '3500' : '3500'));

  // Create DaprServer instance
  const server = options?.daprServer ?? new DaprServer({
    serverHost: '0.0.0.0',
    serverPort: appPort,
    clientOptions: {
      daprHost,
      daprPort,
    },
  });

  let isStarted = false;
  const handlers = new Map<string, MessageHandler<any>[]>();

  return {
    subscribe: async <T>(topic: string, handler: MessageHandler<T>) => {
      // Subscribe to Dapr
      await server.pubsub.subscribe(pubsubName, topic, async (message: T) => {
        return handler(message);
      });
    },

    start: async () => {
      if (isStarted) {
        return;
      }
      isStarted = true;
      await server.start();
    },

    close: async () => {
      if (isStarted) {
        await server.stop();
        isStarted = false;
      }
      handlers.clear();
    },

    getServer: () => server,
  };
}
