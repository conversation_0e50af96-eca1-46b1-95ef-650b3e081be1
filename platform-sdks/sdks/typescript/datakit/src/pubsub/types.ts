// Minimal types for pubsub SDK

export type PublisherOptions = {
  daprAddress?: string; // e.g., 'http://localhost:3500'
  pubsubName?: string;  // e.g., 'pubsub'
  daprHost?: string;    // optional override when using @dapr/dapr client
  daprPort?: string | number; // optional override when using @dapr/dapr client
  daprClient?: import('@dapr/dapr').DaprClient; // supply existing client instance
};

export type SubscriberOptions = {
  daprAddress?: string;
  pubsubName?: string;
  port?: number; // Port for HTTP server to receive Dapr events
  daprHost?: string;
  daprPort?: string | number;
  daprServer?: import('@dapr/dapr').DaprServer; // For testing - allow injection of mock server
};

export type MessageHandler<T = unknown> = (data: T) => Promise<void>;

export type Publisher = {
  publish: (topic: string, data: unknown) => Promise<void>;
  publishBulk: (topic: string, data: unknown[]) => Promise<void>;
  close: () => Promise<void>;
};

export type Subscriber = {
  subscribe: <T = unknown>(topic: string, handler: MessageHandler<T>) => Promise<void>;
  start: () => Promise<void>;
  close: () => Promise<void>;
};
