import { spawn, ChildProcess } from 'child_process';
import { mkdtempSync, writeFileSync, rmSync, existsSync, readFileSync } from 'fs';
import { tmpdir } from 'os';
import { join } from 'path';
import portfinder from 'portfinder';

export type DaprIntegrationInfo = {
  appId: string;
  appPort: number;
  daprHttpPort: number;
  daprGrpcPort: number;
  pubsubComponent: string;
  resourcesPath: string;
  daprProcessPid: number | null;
};

export type DaprTestOptions = {
  appProtocol?: 'http' | 'grpc';
};

const PUBSUB_PREFIX = 'pubsub-';

export class DaprTestManager {
  private namespace: string;
  private info: DaprIntegrationInfo | null = null;
  private tempDir: string | null = null;
  private daprProcess: ChildProcess | null = null;
  private daprStdout: string = '';
  private daprStderr: string = '';
  private options: DaprTestOptions;

  constructor(namespace: string = 'default', options: DaprTestOptions = {}) {
    this.namespace = namespace;
    this.options = { appProtocol: 'http', ...options };
  }

  async start(): Promise<DaprIntegrationInfo> {
    // First attempt to detect an existing sidecar for this namespace via `ps`
    const detected = detectExistingDaprForNamespace(this.namespace);
    if (detected) {
      this.info = detected;
      return detected;
    }

    const appPort = await portfinder.getPortPromise({ startPort: 16000 + Math.floor(Math.random() * 500) });
    const daprHttpPort = await portfinder.getPortPromise({ startPort: 19000 + Math.floor(Math.random() * 500) });
    const daprGrpcPort = await portfinder.getPortPromise({ startPort: 20000 + Math.floor(Math.random() * 500) });
    // Stable names so we can detect later
    const appId = `test-${this.namespace}`;
    const pubsubComponent = `${PUBSUB_PREFIX}${this.namespace}`;
    this.tempDir = mkdtempSync(join(tmpdir(), 'dapr-test-'));
    const resourcesPath = this.tempDir;
    
    const pubsubYaml = join(resourcesPath, `${pubsubComponent}.yaml`);
    const daprConfigYaml = join(resourcesPath, `dapr-config.yaml`);

    // Write pubsub component YAML
    const pubsubConfig = `
apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: ${pubsubComponent}
spec:
  type: pubsub.in-memory
  version: v1
  metadata:
    - name: maxReadBuffer
      value: "10000"
    - name: maxConcurrentMessages
      value: "100"
    - name: maxMessageSize
      value: "1048576"
    - name: consumerID
      value: "test-${this.namespace}"
    - name: closeTimeout
      value: "5s"
`;
    writeFileSync(pubsubYaml, pubsubConfig);

    // Write Dapr config YAML to disable tracing
    const daprConfigContent = `
apiVersion: dapr.io/v1alpha1
kind: Configuration
metadata:
  name: disable-tracing
spec:
  tracing:
    samplingRate: "0"`;

    const daprConfig = this.options.appProtocol === 'grpc' 
      ? daprConfigContent + `
  features:
    - name: proxy.grpc
      enabled: true`
      : daprConfigContent;
    writeFileSync(daprConfigYaml, daprConfig);

    // Ensure files are written and readable
    if (!existsSync(pubsubYaml)) {
      throw new Error(`Failed to write pubsub component file at ${pubsubYaml}`);
    }
    if (!existsSync(daprConfigYaml)) {
      throw new Error(`Failed to write dapr config file at ${daprConfigYaml}`);
    }

    // Start Dapr sidecar with additional logging
    const daprArgs = [
      'run',
      '--app-id', appId,
      '--app-port', appPort.toString(),
      '--dapr-http-port', daprHttpPort.toString(),
      '--dapr-grpc-port', daprGrpcPort.toString(),
      '--resources-path', resourcesPath,
      '--config', daprConfigYaml,
      '--log-level', 'debug', // Change to debug for more info
      '--components-path', resourcesPath, // Explicitly set components path
    ];

    // Add app protocol if it's gRPC
    if (this.options.appProtocol === 'grpc') {
      daprArgs.splice(5, 0, '--app-protocol', 'grpc');
    }
    this.daprProcess = spawn('dapr', daprArgs, {
      stdio: ['ignore', 'pipe', 'pipe'],
    });
    this.daprProcess.unref();
    // Capture logs for diagnostics
    if (this.daprProcess.stdout) {
      this.daprProcess.stdout.on('data', (chunk) => {
        this.daprStdout += chunk.toString();
      });
    }
    if (this.daprProcess.stderr) {
      this.daprProcess.stderr.on('data', (chunk) => {
        this.daprStderr += chunk.toString();
      });
    }

    // Wait for sidecar to start by watching stdout for a specific marker
    const startedMarker = new RegExp(/You're up and running|Sidecar Started/);
    try {
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          console.error('[DAPR STDOUT]', this.daprStdout);
          console.error('[DAPR STDERR]', this.daprStderr);
          reject(new Error(`Timed out waiting for Dapr to emit '${startedMarker.source}'`));
        }, 5000).unref();

        const handleChunk = (chunk: Buffer) => {
          const text = chunk.toString();
          if (startedMarker.test(text)) {
            clearTimeout(timeout);
            this.daprProcess?.stdout?.off('data', handleChunk);
            resolve();
          }
        };
        this.daprProcess?.stdout?.on('data', handleChunk);
      });
    } catch (err) {
      throw err;
    }

    this.info = {
      appId,
      appPort,
      daprHttpPort,
      daprGrpcPort,
      pubsubComponent,
      resourcesPath,
      daprProcessPid: this.daprProcess.pid ?? null,
    };
    return this.info;
  }

  async stop(): Promise<void> {
    // Kill the Dapr process by PID if present
    if (this.info?.daprProcessPid) {
      try {
        process.kill(this.info.daprProcessPid, 'SIGTERM');
        // Wait up to 5s for process to exit, then SIGKILL
        let exited = false;
        const check = () => {
          try {
            process.kill(this.info!.daprProcessPid!, 0);
            return false;
          } catch {
            return true;
          }
        };
        const checkInterval = setInterval(() => {
          if (check()) {
            exited = true;
            clearInterval(checkInterval);
          }
        }, 500).unref();

        // Set a timeout to force kill after 5s
        setTimeout(() => {
          clearInterval(checkInterval);
          if (!exited) {
            try {
              process.kill(this.info!.daprProcessPid!, 'SIGKILL');
            } catch {/* ignore */}
          }
        }, 5000).unref();

        // Wait for process to exit
        while (!exited) {
          await new Promise(resolve => setTimeout(resolve, 100).unref());
          if (check()) break;
        }
      } catch (e) {
        // Ignore if already dead
      }
    }
    if (this.tempDir && existsSync(this.tempDir)) {
      rmSync(this.tempDir, { recursive: true, force: true });
    }
    this.info = null;
    this.tempDir = null;
    this.daprProcess = null;
    this.daprStdout = '';
    this.daprStderr = '';
  }

  getInfo(): DaprIntegrationInfo | null {
    return this.info;
  }
}

// -------------------- Singleton helpers --------------------

// In-memory cache to ensure a single manager per namespace within the same process
const _managers: Record<string, DaprTestManager> = {};

const INFO_FILE_PREFIX = '.dapr-test-info';
const infoFilePath = (namespace: string) => join(tmpdir(), `${INFO_FILE_PREFIX}-${namespace}.json`);

/**
 * Start (or retrieve) a Dapr test side-car for a given namespace.  
 * Calls are idempotent across Jest workers by coordinating through a file in the OS temp directory.
 */
export async function startDaprSidecar(namespace='default', options: DaprTestOptions = {}): Promise<DaprIntegrationInfo & { stop: () => Promise<any> }> {
  // First, check if we already have it in-process
  if (_managers[namespace]) {
    const existing = _managers[namespace].getInfo();
    if (existing) return {
      ...existing,
      stop: () => stopDaprTestManager(existing.pubsubComponent.replace(PUBSUB_PREFIX, '')),
    };
  }

  // Cross-process coordination via a tmp file
  const path = infoFilePath(namespace);
  if (existsSync(path)) {
    try {
      const persisted: DaprIntegrationInfo = JSON.parse(readFileSync(path, 'utf8'));
      // Verify the side-car PID is still alive
      if (persisted.daprProcessPid && isProcessAlive(persisted.daprProcessPid)) {
        // Cache and return
        const placeholder = new DaprTestManager(namespace) as any;
        placeholder.info = persisted;
        _managers[namespace] = placeholder as DaprTestManager;
        return {
          ...persisted,
          stop: () => stopDaprTestManager(persisted.pubsubComponent.replace(PUBSUB_PREFIX, ''))
        };
      }
    } catch {
      // Ignore corrupt file and continue to start a new one
    }
  }

  // Need to start a fresh manager
  const manager = new DaprTestManager(namespace, options);
  const info = await manager.start();
  _managers[namespace] = manager;
  // Persist to disk for other processes
  writeFileSync(path, JSON.stringify(info, null, 2));
  return {
    ...info,
    stop: manager.stop,
  };
}

/**
 * Stop (and clean up) the Dapr test side-car for a given namespace.  
 * If multiple workers attempt to stop, the call is idempotent.
 */
export async function stopDaprTestManager(namespace: string): Promise<void> {
  const mgr = _managers[namespace];
  if (mgr) {
    await mgr.stop().catch(() => {/* ignore */});
    delete _managers[namespace];
  }
  // Remove tmp file if present
  const path = infoFilePath(namespace);
  if (existsSync(path)) {
    try { rmSync(path); } catch {/* ignore */}
  }
}

function isProcessAlive(pid: number): boolean {
  try {
    process.kill(pid, 0);
    return true;
  } catch {
    return false;
  }
}

function cleanupDapr() {
  // Check all running dapr processes and kill them.
  
}

/**
 * Attempts to find a running Dapr sidecar that belongs to the given namespace.
 * It looks for a process that was started with an `--app-id` matching `test-${namespace}`.
 * If found and ports can be extracted, returns a minimal DaprIntegrationInfo.
 */
function detectExistingDaprForNamespace(namespace: string): DaprIntegrationInfo | null {
  try {
    const { execSync } = require('child_process');
    const output: string = execSync('ps -eo pid,args').toString();
    const lines = output.split('\n');
    const targetAppId = `test-${namespace}`;
    for (const line of lines) {
      if (line.includes('dapr') && line.includes('--app-id') && line.includes(targetAppId)) {
        const parts = line.trim().split(/\s+/);
        const pid = parseInt(parts[0], 10);
        // Extract ports and resources path from args
        const args = parts.slice(1);
        const getArg = (name: string) => {
          const idx = args.indexOf(name);
          if (idx !== -1 && idx + 1 < args.length) {
            return args[idx + 1];
          }
          return undefined;
        };
        const appPortStr = getArg('--app-port');
        const daprHttpPortStr = getArg('--dapr-http-port');
        const daprGrpcPortStr = getArg('--dapr-grpc-port');
        const resourcesPath = getArg('--resources-path') ?? '';
        if (!appPortStr || !daprHttpPortStr || !daprGrpcPortStr) continue;
        return {
          appId: targetAppId,
          appPort: parseInt(appPortStr, 10),
          daprHttpPort: parseInt(daprHttpPortStr, 10),
          daprGrpcPort: parseInt(daprGrpcPortStr, 10),
          pubsubComponent: `${PUBSUB_PREFIX}${namespace}`,
          resourcesPath,
          daprProcessPid: pid,
        };
      }
    }
  } catch {
    /* ignore errors, e.g., ps not available */
  }
  return null;
}

if (require.main === module) {
  const manager = new DaprTestManager();
  manager.start().then((info) => {
    console.log(info);
  });
}
