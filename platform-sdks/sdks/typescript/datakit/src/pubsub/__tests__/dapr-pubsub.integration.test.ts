import { createPublisher, createSubscriber } from '../index';
import { spawnSync } from 'child_process';
import portfinder from 'portfinder';
import {
  startDaprSidecar,
  stopDaprTestManager,
  DaprIntegrationInfo,
} from './dapr-integration-utils';

describe('Dapr PubSub Integration (real sidecar)', () => {

  beforeAll(async () => {
    // Ensure Dapr CLI is available
    const daprCheck = spawnSync('dapr', ['--version'], { encoding: 'utf-8' });
    if (daprCheck.error || daprCheck.status !== 0) {
      console.warn('Dapr CLI not found, skipping integration test.');
      (global as any).skipDapr = true;
      return;
    }
  }, 10000);

  it('should publish and receive a message end-to-end', async () => {
    if ((global as any).skipDapr) return;

    const namespace = 'endToEndSubscriber';
    const info = await startDaprSidecar(namespace);

    const subscriber = createSubscriber({
      daprHost: 'localhost',
      daprPort: info.daprHttpPort,
      pubsubName: info.pubsubComponent,
      port: info.appPort,
    });

    const publisher = createPublisher({
      daprHost: 'localhost',
      daprPort: info.daprHttpPort,
      pubsubName: info.pubsubComponent,
    });

    try {
      const received: any[] = [];
      await subscriber.subscribe('integration-topic', async (data) => {
        console.log('Received integration-topic message', data);
        received.push(data);
      });
      await subscriber.start();

      // Wait a bit for the subscription to be fully registered
      await new Promise((r) => setTimeout(r, 500).unref());

      const testMsg = { hello: 'world', ts: Date.now() };
      console.log('Publishing message:', testMsg);
      await publisher.publish('integration-topic', testMsg);

      const start = Date.now();
      while (!received.length && Date.now() - start < 10000) {
        await new Promise((r) => setTimeout(r, 200).unref());
        console.log('Waiting for message... received:', received.length);
      }
      console.log('Final received messages:', received);
      expect(received).toContainEqual(testMsg);
    } catch (err) {
      console.error('Test failed:', err);
      throw err;
    } finally {
      await subscriber.close();
      await info.stop();
    }
  }, 30000);

  it('should handle bulk publishing', async () => {
    if ((global as any).skipDapr) return;

    const namespace = 'bulkPublishing';
    const info = await startDaprSidecar(namespace);
    const subscriberPort = info.appPort;
    const publisher = createPublisher({
      daprHost: 'localhost',
      daprPort: info.daprHttpPort,
      pubsubName: info.pubsubComponent,
    });
    const received: any[] = [];
    const subscriber = createSubscriber({
      daprHost: 'localhost',
      daprPort: info.daprHttpPort,
      pubsubName: info.pubsubComponent,
      port: subscriberPort,
    });

    try {
      console.log('Subscribing to bulk-topic...');
      await subscriber.subscribe('bulk-topic', async (data) => {
        console.log('Received bulk-topic message', data);
        received.push(data);
      });
      console.log('Starting subscriber...');
      await subscriber.start();
      console.log('Subscriber started successfully');

      // Wait a bit for the subscription to be fully registered
      await new Promise((r) => setTimeout(r, 500).unref());

      const msgs = [{ a: 1 }, { b: 2 }, { c: 3 }];
      console.log('Publishing messages:', msgs);
      const result = await publisher.publishBulk('bulk-topic', msgs);
      console.log('Publish result:', result);

      const start = Date.now();
      while (received.length < msgs.length && Date.now() - start < 10000) {
        await new Promise((r) => setTimeout(r, 200).unref());
        console.log('Waiting for messages... received:', received.length);
      }
      console.log('Final received messages:', received);
      expect(received).toEqual(expect.arrayContaining(msgs));
    } catch (err) {
      console.error('Test failed:', err);
      throw err;
    } finally {
      await subscriber.close();
      await info.stop();
    }
  }, 30000);

  it('should handle multiple subscriptions to the same topic', async () => {
    const namespace = 'multiSubscribe';
    const info = await startDaprSidecar(namespace);
    const info2 = await startDaprSidecar(namespace + '2');
    const subscriberPort = info.appPort;
    const publisher = createPublisher({
      daprHost: 'localhost',
      daprPort: info.daprHttpPort,
      pubsubName: info.pubsubComponent,
    });

    const received1: any[] = [];
    const received2: any[] = [];
    const subscriber = createSubscriber({
      daprHost: 'localhost',
      daprPort: info.daprHttpPort,
      pubsubName: info.pubsubComponent,
      port: subscriberPort,
    });
    const subscriber2 = createSubscriber({
      daprHost: 'localhost',
      daprPort: info2.daprHttpPort,
      pubsubName: info2.pubsubComponent,
      port: info2.appPort,
    });

    try {
      // Add two handlers for the same topic
      await subscriber.subscribe('shared-topic', async (data) => {
        received1.push(data);
      });

      await subscriber2.subscribe('shared-topic', async (data) => {
        received2.push(data);
      });

      await subscriber.start();
      await subscriber2.start();

      // Wait for subscription setup
      await new Promise((r) => setTimeout(r, 500).unref());

      const message = { test: 'data' };
      await publisher.publish('shared-topic', message);

      // Wait for message processing
      await new Promise((r) => setTimeout(r, 1000).unref());

      // Only one handler should have received the message
      if (received1.length > 0) {
        expect(received1).toEqual([message]);
        expect(received2).toEqual([]);
      } else {
        expect(received1).toEqual([]);
        expect(received2).toEqual([message]);
      }
    } finally {
      await subscriber.close();
      await subscriber2.close();
      await info.stop();
    }
  }, 30000);
});
