import { createSubscriber } from '../subscriber';
import { DaprServer } from '@dapr/dapr';
import { env } from '../env';

// Mock DaprServer
jest.mock('@dapr/dapr', () => ({
  DaprServer: jest.fn()
}));

describe('Subscriber', () => {
  let mockDaprServer: any;
  let mockPubsub: any;
  let mockStart: jest.Mock;
  let mockStop: jest.Mock;

  beforeEach(() => {
    mockPubsub = {
      subscribe: jest.fn(),
    };

    mockStart = jest.fn();
    mockStop = jest.fn();

    mockDaprServer = {
      pubsub: mockPubsub,
      start: mockStart,
      stop: mockStop,
    };

    (DaprServer as jest.MockedClass<typeof DaprServer>).mockImplementation(() => mockDaprServer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSubscriber', () => {
    it('should create subscriber with default configuration', () => {
      const subscriber = createSubscriber();

      expect(DaprServer).toHaveBeenCalledWith({
        clientOptions: {
          daprHost: '0.0.0.0',
          daprPort: '3500',
        },
        serverHost: '0.0.0.0',
        serverPort: '3000',
      });
      expect(subscriber).toHaveProperty('subscribe');
      expect(subscriber).toHaveProperty('start');
      expect(subscriber).toHaveProperty('close');
    });

    it('should create subscriber with custom port', () => {
      const subscriber = createSubscriber({
        port: 4001,
      });

      expect(DaprServer).toHaveBeenCalledWith({
        serverHost: '0.0.0.0',
        serverPort: '4001',
        clientOptions: {
          daprHost: '0.0.0.0',
          daprPort: '3500',
        },
      });
    });

    it('should create subscriber with custom Dapr server instance', () => {
      const customServer = mockDaprServer;
      const subscriber = createSubscriber({ daprServer: customServer });

      expect(DaprServer).not.toHaveBeenCalled(); // Should not create new server
      expect(subscriber).toBeDefined();
    });

    it('should create subscriber with custom pubsub name', () => {
      const subscriber = createSubscriber({ pubsubName: 'custom-pubsub' });

      expect(subscriber).toBeDefined();
      // pubsubName is used in subscribe calls, not in server construction
    });

    it('should use environment defaults when no options provided', () => {
      const originalEnv = { ...env };

      // Temporarily modify env
      (env as any).DEFAULT_PUBSUB_NAME = 'test-pubsub';
      (env as any).DEFAULT_APP_PORT = 4002;

      const subscriber = createSubscriber();

      expect(DaprServer).toHaveBeenCalledWith({
        serverHost: '0.0.0.0',
        serverPort: '4002',
        clientOptions: {
          daprHost: '0.0.0.0',
          daprPort: '3500',
        },
      });

      // Restore env
      Object.assign(env, originalEnv);
    });
  });

  describe('subscribe', () => {
    it('should subscribe to topic successfully', async () => {
      const subscriber = createSubscriber({ pubsubName: 'test-pubsub' });
      const handler = jest.fn();

      await subscriber.subscribe('test-topic', handler);

      expect(mockPubsub.subscribe).toHaveBeenCalledWith(
        'test-pubsub',
        'test-topic',
        expect.any(Function)
      );
    });

    it('should handle message successfully in handler wrapper', async () => {
      const subscriber = createSubscriber();
      const handler = jest.fn().mockResolvedValue(undefined);

      await subscriber.subscribe('test-topic', handler);

      // Get the wrapper function that was passed to Dapr
      const wrapperFunction = mockPubsub.subscribe.mock.calls[0][2];

      // Simulate message from Dapr (sent directly, not wrapped in data property)
      const mockMessage = { message: 'Hello World' };
      await wrapperFunction(mockMessage);

      expect(handler).toHaveBeenCalledWith({ message: 'Hello World' });
    });

    it('should forward errors from message handler', async () => {
      const subscriber = createSubscriber();
      const handler = jest.fn().mockRejectedValue(new Error('Handler failed'));

      await subscriber.subscribe('error-topic', handler);

      const wrapperFunction = mockPubsub.subscribe.mock.calls[0][2];
      const mockMessage = { error: 'test' };

      await expect(wrapperFunction(mockMessage)).rejects.toThrow(/Handler failed/);

      expect(handler).toHaveBeenCalledWith({ error: 'test' });
    });

    it('should handle different message data types', async () => {
      const subscriber = createSubscriber();
      const handler = jest.fn();

      await subscriber.subscribe('multi-type-topic', handler);
      const wrapperFunction = mockPubsub.subscribe.mock.calls[0][2];

      // String data (sent directly by Dapr)
      await wrapperFunction('simple string');
      expect(handler).toHaveBeenLastCalledWith('simple string');

      // Number data (sent directly by Dapr)
      await wrapperFunction(42);
      expect(handler).toHaveBeenLastCalledWith(42);

      // Array data (sent directly by Dapr)
      await wrapperFunction([1, 2, 3]);
      expect(handler).toHaveBeenLastCalledWith([1, 2, 3]);

      // Object data (sent directly by Dapr)
      await wrapperFunction({ complex: { nested: 'object' } });
      expect(handler).toHaveBeenLastCalledWith({ complex: { nested: 'object' } });
    });

    it('should handle null and undefined messages', async () => {
      const subscriber = createSubscriber();
      const handler = jest.fn();

      await subscriber.subscribe('null-topic', handler);
      const wrapperFunction = mockPubsub.subscribe.mock.calls[0][2];

      // Null message (sent directly by Dapr)
      await wrapperFunction(null);
      expect(handler).toHaveBeenCalledWith(null);

      // Undefined message (sent directly by Dapr)
      await wrapperFunction(undefined);
      expect(handler).toHaveBeenCalledWith(undefined);
    });

    it('should allow multiple subscriptions to different topics', async () => {
      const subscriber = createSubscriber();
      const handler1 = jest.fn();
      const handler2 = jest.fn();

      await subscriber.subscribe('topic1', handler1);
      await subscriber.subscribe('topic2', handler2);

      expect(mockPubsub.subscribe).toHaveBeenCalledTimes(2);
      expect(mockPubsub.subscribe).toHaveBeenNthCalledWith(1,
        env.DEFAULT_PUBSUB_NAME,
        'topic1',
        expect.any(Function)
      );
      expect(mockPubsub.subscribe).toHaveBeenNthCalledWith(2,
        env.DEFAULT_PUBSUB_NAME,
        'topic2',
        expect.any(Function)
      );
    });


    it('should handle concurrent message processing', async () => {
      const subscriber = createSubscriber();
      const handler = jest.fn().mockImplementation(async (data) => {
        // Simulate async processing
        await new Promise(resolve => setTimeout(resolve, 10));
        return `processed-${data.id}`;
      });

      await subscriber.subscribe('concurrent-topic', handler);
      const wrapperFunction = mockPubsub.subscribe.mock.calls[0][2];

      // Send multiple messages concurrently
      const messages = [
        { id: 1 },
        { id: 2 },
        { id: 3 }
      ];

      const promises = messages.map(msg => wrapperFunction(msg));
      await Promise.all(promises);

      expect(handler).toHaveBeenCalledTimes(3);
      expect(handler).toHaveBeenCalledWith({ id: 1 });
      expect(handler).toHaveBeenCalledWith({ id: 2 });
      expect(handler).toHaveBeenCalledWith({ id: 3 });
    });

    it('should forward errors to Dapr', async () => {
      const subscriber = createSubscriber();

      const handler = jest.fn()
        .mockRejectedValueOnce(new Error('First call fails'))
        .mockResolvedValueOnce('Second call succeeds')
        .mockRejectedValueOnce(new Error('Third call fails'));

      await subscriber.subscribe('error-recovery-topic', handler);
      const wrapperFunction = mockPubsub.subscribe.mock.calls[0][2];

      // First message should fail but not throw
      await expect(wrapperFunction({ attempt: 1 })).rejects.toThrow('First call fails');

      // Second message should succeed
      await expect(wrapperFunction({ attempt: 2 })).resolves.toBe('Second call succeeds');

      // Third message should fail but not throw
      await expect(wrapperFunction({ attempt: 3 })).rejects.toThrow('Third call fails');

      expect(handler).toHaveBeenCalledTimes(3);
    });
  });

  describe('start', () => {
    it('should start the Dapr server', async () => {
      const subscriber = createSubscriber();

      await subscriber.start();

      expect(mockStart).toHaveBeenCalledTimes(1);
    });

    it('should not start multiple times', async () => {
      const subscriber = createSubscriber();

      await subscriber.start();
      await subscriber.start();
      await subscriber.start();

      expect(mockStart).toHaveBeenCalledTimes(1);
    });

    it('should start with custom server', async () => {
      const customServer = mockDaprServer;
      const subscriber = createSubscriber({ daprServer: customServer });

      await subscriber.start();

      expect(mockStart).toHaveBeenCalledTimes(1);
    });
  });

  describe('close', () => {
    it('should stop the Dapr server', async () => {
      const subscriber = createSubscriber();

      await subscriber.start();
      await subscriber.close();

      expect(mockStop).toHaveBeenCalledTimes(1);
    });

    it('should handle close without start', async () => {
      const subscriber = createSubscriber();

      // Should not error when closing without starting
      await expect(subscriber.close()).resolves.not.toThrow();
      expect(mockStop).not.toHaveBeenCalled();
    });

    it('should handle multiple close calls', async () => {
      const subscriber = createSubscriber();

      await subscriber.start();
      await subscriber.close();
      await subscriber.close();
      await subscriber.close();

      expect(mockStop).toHaveBeenCalledTimes(1);
    });

    it('should clear handlers on close', async () => {
      const subscriber = createSubscriber();
      const handler = jest.fn();

      await subscriber.subscribe('test-topic', handler);
      await subscriber.start();
      await subscriber.close();

      // After close, handlers should be cleared
      // This is implicitly tested by the implementation
      expect(mockStop).toHaveBeenCalledTimes(1);
    });
  });

  describe('Configuration', () => {
    it('should use custom daprHost and daprPort', () => {
      const subscriber = createSubscriber({
        daprHost: 'custom-host',
        daprPort: 9999,
      });

      expect(DaprServer).toHaveBeenCalledWith({
        serverHost: '0.0.0.0',
        serverPort: '3000',
        clientOptions: {
          daprHost: 'custom-host',
          daprPort: '9999',
        },
      });
    });

    it('should parse daprAddress correctly', () => {
      const subscriber = createSubscriber({
        daprAddress: 'http://my-dapr-host:8080',
      });

      expect(DaprServer).toHaveBeenCalledWith({
        serverHost: '0.0.0.0',
        serverPort: '3000',
        clientOptions: {
          daprHost: 'my-dapr-host',
          daprPort: '8080',
        },
      });
    });

    it('should handle daprAddress without port', () => {
      const subscriber = createSubscriber({
        daprAddress: 'http://my-dapr-host',
      });

      expect(DaprServer).toHaveBeenCalledWith({
        serverHost: '0.0.0.0',
        serverPort: '3000',
        clientOptions: {
          daprHost: 'my-dapr-host',
          daprPort: '3500', // default port
        },
      });
    });

    it('should handle complex configuration', () => {
      const subscriber = createSubscriber({
        pubsubName: 'my-pubsub',
        port: 4000,
        daprHost: 'dapr-sidecar',
        daprPort: 3501,
      });

      expect(DaprServer).toHaveBeenCalledWith({
        serverHost: '0.0.0.0',
        serverPort: '4000',
        clientOptions: {
          daprHost: 'dapr-sidecar',
          daprPort: '3501',
        },
      });
    });
  });

  describe('Integration with server access', () => {
    it('should provide access to underlying server', () => {
      const subscriber = createSubscriber();
      const server = subscriber.getServer();

      expect(server).toBe(mockDaprServer);
    });

    it('should work with custom server instance', () => {
      const customServer = mockDaprServer;
      const subscriber = createSubscriber({ daprServer: customServer });
      const server = subscriber.getServer();

      expect(server).toBe(customServer);
    });
  });
});
