import { createPublisher } from '../publisher';
import { DaprClient } from '@dapr/dapr';
import { env } from '../env';

// Mock DaprClient
jest.mock('@dapr/dapr', () => ({
  DaprClient: jest.fn()
}));

describe('Publisher', () => {
  let mockDaprClient: any;
  let mockPublish: jest.Mock;
  let mockPublishBulk: jest.Mock;

  beforeEach(() => {
    mockPublish = jest.fn();
    mockPublishBulk = jest.fn();
    
    mockDaprClient = {
      pubsub: {
        publish: mockPublish,
        publishBulk: mockPublishBulk,
      },
    };

    (DaprClient as jest.MockedClass<typeof DaprClient>).mockImplementation(() => mockDaprClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPublisher', () => {
    it('should create publisher with default configuration', () => {
      const publisher = createPublisher();

      expect(DaprClient).toHaveBeenCalledWith({
        daprHost: '0.0.0.0',
        daprPort: '3500',
      });
      expect(publisher).toHaveProperty('publish');
      expect(publisher).toHaveProperty('publishBulk');
      expect(publisher).toHaveProperty('close');
    });

    it('should create publisher with custom pubsub name', () => {
      const publisher = createPublisher({ pubsubName: 'custom-pubsub' });

      expect(publisher).toBeDefined();
      // pubsubName is used in publish calls, not in client construction
    });

    it('should create publisher with custom Dapr client', () => {
      const customClient = mockDaprClient;
      const publisher = createPublisher({ daprClient: customClient });

      expect(DaprClient).not.toHaveBeenCalled(); // Should not create new client
      expect(publisher).toBeDefined();
    });

    it('should create publisher with custom Dapr host and port', () => {
      const publisher = createPublisher({
        daprHost: 'custom-host',
        daprPort: 3501,
      });

      expect(DaprClient).toHaveBeenCalledWith({
        daprHost: 'custom-host',
        daprPort: '3501',
      });
    });

    it('should create publisher with custom Dapr address', () => {
      const publisher = createPublisher({
        daprAddress: 'http://localhost:3502',
      });

      expect(DaprClient).toHaveBeenCalledWith({
        daprHost: 'localhost',
        daprPort: '3502',
      });
    });

    it('should handle invalid Dapr address gracefully', () => {
      const publisher = createPublisher({
        daprAddress: 'invalid-url',
      });

      // Should fall back to default DaprClient
      expect(DaprClient).toHaveBeenCalledWith();
    });

    it('should use environment defaults when no options provided', () => {
      const originalEnv = { ...env };
      
      // Temporarily modify env
      (env as any).DEFAULT_DAPR_ADDRESS = 'http://test-host:3503';
      (env as any).DEFAULT_PUBSUB_NAME = 'test-pubsub';

      const publisher = createPublisher();

      expect(DaprClient).toHaveBeenCalledWith({
        daprHost: 'test-host',
        daprPort: '3503',
      });

      // Restore env
      Object.assign(env, originalEnv);
    });
  });

  describe('publish', () => {
    it('should publish message successfully', async () => {
      mockPublish.mockResolvedValue({ error: null });
      
      const publisher = createPublisher({ pubsubName: 'test-pubsub' });
      
      await publisher.publish('test-topic', { message: 'Hello World' });

      expect(mockPublish).toHaveBeenCalledWith(
        'test-pubsub',
        'test-topic',
        { message: 'Hello World' },
        { contentType: 'application/json' }
      );
    });

    it('should handle publish errors', async () => {
      mockPublish.mockResolvedValue({ error: 'Publishing failed' });
      
      const publisher = createPublisher();
      
      await expect(publisher.publish('test-topic', { data: 'test' }))
        .rejects.toThrow("Failed to publish to topic 'test-topic': Publishing failed");

      expect(mockPublish).toHaveBeenCalledWith(
        env.DEFAULT_PUBSUB_NAME,
        'test-topic',
        { data: 'test' },
        { contentType: 'application/json' }
      );
    });

    it('should handle network errors during publish', async () => {
      mockPublish.mockRejectedValue(new Error('Network error'));
      
      const publisher = createPublisher();
      
      await expect(publisher.publish('test-topic', { data: 'test' }))
        .rejects.toThrow('Network error');
    });

    it('should publish different data types', async () => {
      mockPublish.mockResolvedValue({ error: null });
      
      const publisher = createPublisher();

      // String data
      await publisher.publish('topic1', 'simple string');
      expect(mockPublish).toHaveBeenCalledWith(
        env.DEFAULT_PUBSUB_NAME,
        'topic1',
        'simple string',
        { contentType: 'application/json' }
      );

      // Number data
      await publisher.publish('topic2', 42);
      expect(mockPublish).toHaveBeenCalledWith(
        env.DEFAULT_PUBSUB_NAME,
        'topic2',
        42,
        { contentType: 'application/json' }
      );

      // Array data
      await publisher.publish('topic3', [1, 2, 3]);
      expect(mockPublish).toHaveBeenCalledWith(
        env.DEFAULT_PUBSUB_NAME,
        'topic3',
        [1, 2, 3],
        { contentType: 'application/json' }
      );
    });
  });

  describe('publishBulk', () => {
    it('should publish bulk messages successfully', async () => {
      mockPublishBulk.mockResolvedValue({ failedMessages: [] });
      
      const publisher = createPublisher({ pubsubName: 'bulk-pubsub' });
      const messages = [
        { id: 1, message: 'First' },
        { id: 2, message: 'Second' },
      ];
      
      await publisher.publishBulk('bulk-topic', messages);

      expect(mockPublishBulk).toHaveBeenCalledWith(
        'bulk-pubsub',
        'bulk-topic',
        messages
      );
    });

    it('should handle empty message array', async () => {
      const publisher = createPublisher();
      
      // Should return early without calling Dapr
      await publisher.publishBulk('empty-topic', []);

      expect(mockPublishBulk).not.toHaveBeenCalled();
    });

    it('should handle partial failures in bulk publish', async () => {
      mockPublishBulk.mockResolvedValue({
        failedMessages: [
          { message: { entryID: 'msg1' }, error: 'Failed to process message 1' },
          { message: { entryID: 'msg2' }, error: 'Failed to process message 2' },
        ]
      });
      
      const publisher = createPublisher();
      const messages = [
        { id: 1, message: 'First' },
        { id: 2, message: 'Second' },
        { id: 3, message: 'Third' },
      ];
      
      await expect(publisher.publishBulk('bulk-topic', messages))
        .rejects.toThrow(
          "Failed to bulk publish to topic 'bulk-topic', failed entries: " +
          JSON.stringify([
            { entryId: 'msg1', error: 'Failed to process message 1' },
            { entryId: 'msg2', error: 'Failed to process message 2' },
          ])
        );
    });

    it('should handle network errors during bulk publish', async () => {
      mockPublishBulk.mockRejectedValue(new Error('Bulk network error'));
      
      const publisher = createPublisher();
      
      await expect(publisher.publishBulk('bulk-topic', [{ data: 'test' }]))
        .rejects.toThrow('Bulk network error');
    });

    it('should handle bulk publish with large message arrays', async () => {
      mockPublishBulk.mockResolvedValue({ failedMessages: [] });
      
      const publisher = createPublisher();
      const largeMessageArray = Array.from({ length: 1000 }, (_, i) => ({ id: i, data: `Message ${i}` }));
      
      await publisher.publishBulk('large-topic', largeMessageArray);

      expect(mockPublishBulk).toHaveBeenCalledWith(
        env.DEFAULT_PUBSUB_NAME,
        'large-topic',
        largeMessageArray
      );
    });
  });

  describe('close', () => {
    it('should close publisher gracefully', async () => {
      const publisher = createPublisher();
      
      // Should not throw - DaprClient uses HTTP, nothing special to close
      await expect(publisher.close()).resolves.not.toThrow();
    });

    it('should be callable multiple times', async () => {
      const publisher = createPublisher();
      
      await publisher.close();
      await publisher.close(); // Should not throw on second call
    });
  });

  describe('Integration scenarios', () => {
    it('should handle mixed success and error scenarios', async () => {
      const publisher = createPublisher();

      // Successful publish
      mockPublish.mockResolvedValueOnce({ error: null });
      await publisher.publish('success-topic', { status: 'ok' });

      // Failed publish
      mockPublish.mockResolvedValueOnce({ error: 'Validation failed' });
      await expect(publisher.publish('fail-topic', { invalid: 'data' }))
        .rejects.toThrow("Failed to publish to topic 'fail-topic': Validation failed");

      expect(mockPublish).toHaveBeenCalledTimes(2);
    });

    it('should handle concurrent publish operations', async () => {
      mockPublish.mockResolvedValue({ error: null });
      
      const publisher = createPublisher();
      
      const publishPromises = [
        publisher.publish('topic1', { concurrent: 1 }),
        publisher.publish('topic2', { concurrent: 2 }),
        publisher.publish('topic3', { concurrent: 3 }),
      ];

      await Promise.all(publishPromises);

      expect(mockPublish).toHaveBeenCalledTimes(3);
    });

    it('should preserve message ordering in single topic', async () => {
      mockPublish.mockResolvedValue({ error: null });
      
      const publisher = createPublisher();
      
      // Sequential publishes to same topic
      await publisher.publish('ordered-topic', { order: 1 });
      await publisher.publish('ordered-topic', { order: 2 });
      await publisher.publish('ordered-topic', { order: 3 });

      expect(mockPublish).toHaveBeenNthCalledWith(1,
        env.DEFAULT_PUBSUB_NAME,
        'ordered-topic',
        { order: 1 },
        { contentType: 'application/json' }
      );
      expect(mockPublish).toHaveBeenNthCalledWith(2,
        env.DEFAULT_PUBSUB_NAME,
        'ordered-topic',
        { order: 2 },
        { contentType: 'application/json' }
      );
      expect(mockPublish).toHaveBeenNthCalledWith(3,
        env.DEFAULT_PUBSUB_NAME,
        'ordered-topic',
        { order: 3 },
        { contentType: 'application/json' }
      );
    });
  });
});
