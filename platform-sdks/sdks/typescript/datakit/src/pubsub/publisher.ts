import type { Publisher, PublisherOptions } from './types';
import { DaprClient } from '@dapr/dapr';
import { env } from './env';

export function createPublisher(options?: PublisherOptions): Publisher {
  const pubsubName = options?.pubsubName ?? env.DEFAULT_PUBSUB_NAME;

  // Resolve / create Dapr client
  const client = options?.daprClient ?? (() => {
    if (options?.daprHost || options?.daprPort) {
      return new DaprClient({
        daprHost: options.daprHost ?? '0.0.0.0',
        daprPort: String(options.daprPort ?? '3500'),
      });
    }

    const daprAddress = options?.daprAddress ?? env.DEFAULT_DAPR_ADDRESS;
    try {
      const url = new URL(daprAddress);
      return new DaprClient({ daprHost: url.hostname, daprPort: url.port || '3500' });
    } catch {
      // Fallback to default
      return new DaprClient();
    }
  })();

  return {
    publish: async (topic, data) => {
      const res = await client.pubsub.publish(pubsubName, topic, data as any, { contentType: 'application/json' });
      if (res.error) {
        throw new Error(`Failed to publish to topic '${topic}': ${String(res.error)}`);
      }
    },

    publishBulk: async (topic, dataArray) => {
      if (dataArray.length === 0) return;
      const res = await client.pubsub.publishBulk(pubsubName, topic, dataArray as any);
      if (res.failedMessages && res.failedMessages.length > 0) {
        const details = res.failedMessages.map((f) => ({ entryId: f.message.entryID, error: String(f.error) }));
        throw new Error(`Failed to bulk publish to topic '${topic}', failed entries: ${JSON.stringify(details)}`);
      }
    },

    close: async () => {
      // DaprClient uses HTTP, nothing special to close for now.
    },
  };
}
