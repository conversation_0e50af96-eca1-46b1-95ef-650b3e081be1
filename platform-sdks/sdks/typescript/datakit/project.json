{"name": "ts-datakit", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "sdks/typescript/datakit/src", "tags": ["typescript", "datakit", "library"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "sdks/typescript/datakit/dist", "rootDir": "sdks/typescript/datakit/src", "main": "sdks/typescript/datakit/src/index.ts", "tsConfig": "sdks/typescript/datakit/tsconfig.json", "assets": ["sdks/typescript/datakit/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "sdks/typescript/datakit/jest.ci.config.ts", "maxWorkers": "50%", "runInBand": true, "forceExit": true, "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "nx:run-commands", "options": {"command": "npx eslint src/**/*.ts", "cwd": "sdks/typescript/datakit"}}, "lint:fix": {"executor": "nx:run-commands", "options": {"command": "npx eslint src/**/*.ts --fix", "cwd": "sdks/typescript/datakit"}}, "clean": {"executor": "nx:run-commands", "options": {"command": "rm -rf dist", "cwd": "sdks/typescript/datakit"}}, "tidy": {"executor": "@nx/nx-tidy:tidy"}}}