# Datakit TypeScript SDK

A comprehensive TypeScript SDK providing CQRS and PubSub capabilities with Dapr.

## Features

### CQRS Module

- 🔒 **Type-safe** command and query definitions
- 🔄 **Async handler** support with custom context injection
- ✅ **Zod Schema** validation support

### PubSub Module

- 🔒 **Type Safety**: Full TypeScript support
- 🔌 **Dapr Support**: Easy integration with Dapr pub/sub

## Installation

```bash
pnpm add @matricsio/datakit
```

### Peer Dependencies

```bash
pnpm add @dapr/dapr zod
```
