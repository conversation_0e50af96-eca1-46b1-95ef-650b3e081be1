/* eslint-disable */
export default {
    displayName: 'datakit',
    preset: '../../../jest.preset.js',
    testEnvironment: 'node',
    transform: {
        '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.json' }]
    },
    moduleFileExtensions: ['ts', 'js', 'html'],
    coverageDirectory: '../../../coverage/sdks/typescript/datakit',
    maxWorkers: '50%',
    forceExit: true,

    // Coverage configuration
    collectCoverage: true,
    collectCoverageFrom: [
        'src/**/*.{ts,js}',
        '!src/**/*.d.ts',
        '!src/**/__tests__/**',
        '!src/**/*.test.{ts,js}',
        '!src/**/*.spec.{ts,js}',
        '!src/**/index.ts', // Usually just exports
        '!src/**/examples/**', // Example files
        '!src/**/example.ts', // Example files
        '!src/**/example.js', // Example files
    ],
    coverageReporters: [
        'text',
        'text-summary',
        'html',
        'lcov',
        'json'
    ],
    coverageThreshold: {
        global: {
            branches: 70,
            functions: 80,
            lines: 80,
            statements: 80
        }
    }
};
