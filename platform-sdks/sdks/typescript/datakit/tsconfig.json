{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./node_modules/@types"], "noImplicitAny": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true}, "include": ["src"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}