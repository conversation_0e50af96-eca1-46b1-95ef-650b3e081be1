#!/usr/bin/env tsx

import { FastMCP } from 'fastmcp';

async function createMinimalServer(port: number) {
    const server = new FastMCP({
        name: "Minimal Test Server",
        version: "1.0.0",
    });

    server.addTool({
        name: "hello",
        description: "A simple hello tool",
        parameters: {
            type: "object",
            properties: {
                name: { type: "string" }
            },
            required: ["name"],
            additionalProperties: false
        },
        execute: async (args) => {
            return {
                content: [
                    {
                        type: "text",
                        text: `Hello, ${args.name}!`
                    }
                ]
            };
        }
    });

    await server.start({
        transportType: 'httpStream',
        httpStream: {
            port,
            endpoint: '/mcp'
        }
    });

    console.log(`Minimal FastMCP server started on port ${port}`);
    return server;
}

async function main() {
    const port = parseInt(process.argv[2] || '12502');
    
    try {
        const server = await createMinimalServer(port);
        
        // Handle graceful shutdown
        process.on('SIGTERM', async () => {
            console.log('Shutting down minimal server...');
            await server.stop();
            process.exit(0);
        });
        
        process.on('SIGINT', async () => {
            console.log('Shutting down minimal server...');
            await server.stop();
            process.exit(0);
        });
        
    } catch (error) {
        console.error('Failed to start minimal server:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}
