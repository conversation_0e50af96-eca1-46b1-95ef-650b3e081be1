/* eslint-disable */
import baseConfig from './jest.config';

const ciConfig = {
    ...baseConfig,
    // Override testMatch to exclude integration tests
    testMatch: [
        '**/?(*.)+(spec|test).ts?(x)',
        '!**/*.integration.test.ts?(x)'
    ],
    // Ensure we're still collecting coverage but excluding integration tests
    collectCoverageFrom: [
        'src/**/*.{ts,js}',
        '!src/**/*.d.ts',
        '!src/**/__tests__/**',
        '!src/**/*.integration.test.{ts,js}',
        '!src/**/*.spec.{ts,js}',
        '!src/**/index.ts',
        '!src/**/examples/**',
        '!src/**/example.ts',
        '!src/**/example.js',
    ],
    // CI specific reporters - focusing on machine readable formats
    coverageReporters: [
        'text-summary',
        'lcov',
        'json',
        'cobertura' // Added for CI pipeline integration
    ]
};

export default ciConfig;
