package cqrs

import "testing"

func TestPluralize(t *testing.T) {
	engine := &Engine{}

	tests := []struct {
		input    string
		expected string
	}{
		{"notification", "notifications"},
		{"user", "users"},
		{"company", "companies"},
		{"box", "boxes"},
		{"quiz", "quizzes"},
		{"", ""},
	}

	for _, test := range tests {
		result := engine.pluralize(test.input)
		if result != test.expected {
			t.<PERSON><PERSON><PERSON>("pluralize(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}

func TestExtractResourceName(t *testing.T) {
	engine := &Engine{}

	tests := []struct {
		input    string
		expected string
	}{
		{"GetNotificationsQuery", "notifications"},
		{"CreateUserCommand", "users"},
		{"UpdateCompanyCommand", "companies"},
	}

	for _, test := range tests {
		result := engine.extractResourceName(test.input)
		if result != test.expected {
			t.<PERSON><PERSON>("extractResourceName(%q) = %q, expected %q", test.input, result, test.expected)
		}
	}
}
