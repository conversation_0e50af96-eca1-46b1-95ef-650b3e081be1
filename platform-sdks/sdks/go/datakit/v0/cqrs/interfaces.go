package cqrs

import (
	"context"
	"encoding/json"
	"regexp"

	"github.com/gin-gonic/gin"
)

// Named provides type identification without reflection
type Named interface {
	Name() string
}

// Command represents application commands with self-identification
type Command interface {
	Named
}

// Query represents application queries with self-identification
type Query interface {
	Named
}

// Event represents domain events with self-identification
type Event interface {
	Named
}

// CommandHandler handles business logic - transport agnostic
type CommandHandler func(ctx context.Context, cmd Command) error

// QueryHandler handles queries - transport agnostic
type QueryHandler func(ctx context.Context, qry Query) (interface{}, error)

// CommandConfig configures how a command is exposed across transports
type CommandConfig struct {
	Command  Command
	Factory  func() Command // Optional: custom factory function. If nil, reflection-based factory is used
	Handler  interface{}    // Supports both CommandHandler and concrete handlers like func(ctx, *ConcreteCmd) error
	Metadata Metadata
}

// QueryConfig configures how a query is exposed
type QueryConfig struct {
	Query    Query
	Factory  func() Query // Optional: custom factory function. If nil, reflection-based factory is used
	Handler  interface{}  // Supports both QueryHandler and concrete handlers like func(ctx, *ConcreteQuery) (result, error)
	Metadata Metadata
}

// Metadata defines how command/query is exposed with additional information
type Metadata struct {
	Version     string                 // API version (e.g., "1.0", "2.1")
	Description string                 // Human-readable description
	Tags        []string               // Tags for categorization
	REST        *REST                  // HTTP REST configuration
	PubSub      *PubSub                // PubSub messaging configuration
	MCP         *MCP                   // MCP configuration (optional)
	Annotations map[string]interface{} // Additional metadata (rate limits, auth, etc.)
}

// REST configuration for HTTP endpoints
type REST struct {
	Method string // HTTP method (GET, POST, PUT, DELETE, PATCH)
	Path   string // URL path with optional parameters
}

// PubSub configuration for Dapr messaging
type PubSub struct {
	Topic string // Topic name
	Event string // Event name filter - only events with this name will be processed
}

// MCP holds Model Context Protocol specific metadata for a command.
// When populated in CommandConfig.Metadata, the engine automatically exposes
// an additional REST endpoint that conforms to MCP semantics.
//
// All fields follow the semantics outlined in the MCP spec but nothing is
// hard-wired so that future values stay forward compatible.
//
//	ContextType        – high-level categorisation of the model (aggregate, projection …).
//	Version            – sem-ver string (e.g. "1.2.3"). Only major is used for routing.
//	OperationCategory  – describes the type: command, query or event.
//	RequestSchemaRef   – reference (URI/ID) to the request JSON schema.
//	ResponseSchemaRef  – reference (URI/ID) to the response JSON schema.
//	AuthHint           – optional authentication hint (public, jwt, mTLS …).
//	TimeoutSeconds     – optional processing timeout hint in seconds.
//	PathOverride       – optional explicit REST path. Takes precedence over the SDK-derived one.
//
// The struct purposefully avoids mandatory tags so the Engine can run the
// validation logic and return detailed errors instead of failing decoding.
//
// NOTE: the Engine validates Version with semverRx below.
//
// # Stability
// Adding this file is backwards compatible – old binaries ignore it because no
// existing code references the symbol.
type MCP struct {
	ContextType       string `json:"contextType"`
	Version           string `json:"version"`
	OperationCategory string `json:"operationCategory"`
	RequestSchemaRef  string `json:"requestSchemaRef"`
	ResponseSchemaRef string `json:"responseSchemaRef"`
	AuthHint          string `json:"authHint,omitempty"`
	TimeoutSeconds    int    `json:"timeoutSeconds,omitempty"`
	PathOverride      string `json:"pathOverride,omitempty"`
}

// Publisher publishes events to topics
type Publisher interface {
	PublishEvent(ctx context.Context, topic string, event interface{}) error
}

// Subscriber subscribes to topics and handles events
type Subscriber interface {
	Start(ctx context.Context) error
	Subscribe(topic, route string, handler func(ctx context.Context, data []byte) error) error
}

var semverRx = regexp.MustCompile(`^\d+\.\d+\.\d+$`)

// JSON-RPC types for MCP communication
type JSONRPCRequest struct {
	JSONRPC string          `json:"jsonrpc"`
	ID      interface{}     `json:"id"`
	Method  string          `json:"method"`
	Params  json.RawMessage `json:"params,omitempty"`
}

type JSONRPCResponse struct {
	JSONRPC string        `json:"jsonrpc"`
	ID      interface{}   `json:"id"`
	Result  interface{}   `json:"result,omitempty"`
	Error   *JSONRPCError `json:"error,omitempty"`
}

type JSONRPCError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// MCPManager handles all MCP-related functionality
type MCPManager struct {
	engine MCPEngine
}

// MCPEngine interface defines the minimal engine interface needed by MCPManager
type MCPEngine interface {
	ToolNames() []string
	CommandConfigByName(name string) (*CommandConfig, bool)
	QueryConfigByName(name string) (*QueryConfig, bool)
	HandleCommand(ctx context.Context, cmd Command) error
	HandleQuery(ctx context.Context, qry Query) (interface{}, error)
	GetServiceName() string
	GetGin() MCPGinRouter
	ToKebabCase(s string) string
	HasMCPCommands() bool
	HasMCPQueries() bool
}

// MCPGinRouter interface defines the minimal gin router interface needed by MCPManager
type MCPGinRouter interface {
	POST(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes
	GET(relativePath string, handlers ...gin.HandlerFunc) gin.IRoutes
}
