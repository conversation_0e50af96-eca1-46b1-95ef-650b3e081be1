package cqrs

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/google/uuid"
)

// Complete example demonstrating the transformed CQRS SDK with MCP integration

// Example domain entities

type User struct {
	ID        string    `json:"id"`
	UserName  string    `json:"userName"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"createdAt"`
}

// Commands with naming conventions for auto-generation

type CreateUserCommand struct {
	UserName string `json:"user_name" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
}

func (c *CreateUserCommand) Name() string {
	return "CreateUserCommand" // Auto-generates: POST /api/v1/users
}

type UpdateUserCommand struct {
	ID       string `uri:"id" binding:"required"`  // From path parameter
	UserName string `json:"userName"`              // From JSON body
	Email    string `json:"email" binding:"email"` // From JSON body
}

func (c *UpdateUserCommand) Name() string {
	return "UpdateUserCommand" // Auto-generates: PUT /api/v1/users/:id
}

// Queries with naming conventions

type GetUsersQuery struct {
	Limit  int `form:"limit"`  // Query parameter: ?limit=10
	Offset int `form:"offset"` // Query parameter: ?offset=0
}

func (q *GetUsersQuery) Name() string {
	return "GetUsersQuery" // Auto-generates: GET /api/v1/users
}

type GetUserByIDQuery struct {
	ID string `uri:"id" binding:"required"` // From path parameter /:id
}

func (q *GetUserByIDQuery) Name() string {
	return "GetUserByIDQuery" // Auto-generates: GET /api/v1/users/:id
}

// Events

type UserCreatedEvent struct {
	UserID    string    `json:"userId"`
	UserName  string    `json:"userName"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"createdAt"`
}

func (e *UserCreatedEvent) Name() string {
	return "UserCreatedEvent"
}

// Service with concrete handlers (no type assertions needed!)

type UserService struct {
	users     map[string]*User
	publisher Publisher
}

func NewUserService(publisher Publisher) *UserService {
	return &UserService{
		users:     make(map[string]*User),
		publisher: publisher,
	}
}

// Concrete command handler - direct access to command fields

func (s *UserService) CreateUserHandler(ctx context.Context, cmd *CreateUserCommand) error {
	// No type assertion needed - direct access to cmd.UserName, cmd.Email!

	userID := uuid.New().String()
	user := &User{
		ID:        userID,
		UserName:  cmd.UserName,
		Email:     cmd.Email,
		CreatedAt: time.Now(),
	}

	s.users[userID] = user

	// Publish event
	if s.publisher != nil {
		event := &UserCreatedEvent{
			UserID:    userID,
			UserName:  cmd.UserName,
			Email:     cmd.Email,
			CreatedAt: user.CreatedAt,
		}
		s.publisher.PublishEvent(ctx, "users", event)
	}

	fmt.Printf("User created: %s (%s)\n", user.UserName, user.Email)
	return nil
}

// Concrete command handler with path parameter binding

func (s *UserService) UpdateUserHandler(ctx context.Context, cmd *UpdateUserCommand) error {
	// Direct access to cmd.ID from path, cmd.UserName and cmd.Email from body!

	user, exists := s.users[cmd.ID]
	if !exists {
		return fmt.Errorf("user not found: %s", cmd.ID)
	}

	if cmd.UserName != "" {
		user.UserName = cmd.UserName
	}
	if cmd.Email != "" {
		user.Email = cmd.Email
	}

	fmt.Printf("User updated: %s (%s)\n", user.UserName, user.Email)
	return nil
}

// Concrete query handler with pagination

func (s *UserService) GetUsersHandler(ctx context.Context, qry *GetUsersQuery) (interface{}, error) {
	// Direct access to qry.Limit and qry.Offset from query parameters!

	// Set defaults
	limit := qry.Limit
	if limit <= 0 {
		limit = 10
	}
	offset := qry.Offset
	if offset < 0 {
		offset = 0
	}

	// Get users with pagination
	allUsers := make([]*User, 0, len(s.users))
	for _, user := range s.users {
		allUsers = append(allUsers, user)
	}

	// Apply pagination
	total := len(allUsers)
	start := offset
	if start > total {
		start = total
	}
	end := start + limit
	if end > total {
		end = total
	}

	var users []*User
	if start < total {
		users = allUsers[start:end]
	} else {
		users = []*User{}
	}

	return map[string]interface{}{
		"users":  users,
		"total":  total,
		"limit":  limit,
		"offset": offset,
	}, nil
}

// Concrete query handler with path parameter

func (s *UserService) GetUserByIDHandler(ctx context.Context, qry *GetUserByIDQuery) (interface{}, error) {
	// Direct access to qry.ID from path parameter!

	user, exists := s.users[qry.ID]
	if !exists {
		return nil, fmt.Errorf("user not found: %s", qry.ID)
	}

	return user, nil
}

// Complete example function

func ExampleCompleteTransformation() {
	fmt.Println("🚀 CQRS SDK Transformation Example")
	fmt.Println("===================================")

	// Create service
	publisher := NewDaprPublisher("kafka-pubsub", "http://localhost:3500")
	service := NewUserService(publisher)

	// Create engine with comprehensive configuration
	app := NewApp("user-management-service",
		WithHTTP(8080),
		WithSubscriber("kafka-pubsub", "http://localhost:3500", 9081),
		WithHTTPPathPrefix("/api/v1"),
	)

	// Register commands with rich metadata and auto-generation
	app.RegisterCommands(
		// Auto-generated: POST /api/v1/users
		CommandConfig{
			Command: &CreateUserCommand{},
			Handler: service.CreateUserHandler,
			Metadata: Metadata{
				Version:     "1.0",
				Description: "Create a new user account",
				Tags:        []string{"user", "create", "account"},
				Annotations: map[string]interface{}{
					"rateLimit":     "100/minute",
					"requiredRoles": []string{"admin", "staff"},
				},
				// REST: auto-generated as POST /api/v1/users
			},
		},

		// Auto-generated: PUT /api/v1/users/:id
		CommandConfig{
			Command: &UpdateUserCommand{},
			Handler: service.UpdateUserHandler,
			Metadata: Metadata{
				Version:     "1.0",
				Description: "Update existing user information",
				Tags:        []string{"user", "update"},
				Annotations: map[string]interface{}{
					"rateLimit":     "50/minute",
					"requiredRoles": []string{"admin", "staff", "self"},
					"auditLevel":    "medium",
				},
				// REST: auto-generated as PUT /api/v1/users/:id
			},
		},
	)

	// Register queries with auto-generation
	app.RegisterQueries(
		// Auto-generated: GET /api/v1/users
		QueryConfig{
			Query:   &GetUsersQuery{},
			Handler: service.GetUsersHandler,
			Metadata: Metadata{
				Version:     "1.0",
				Description: "Get all users with pagination",
				Tags:        []string{"user", "list", "pagination"},
				Annotations: map[string]interface{}{
					"cacheable":    true,
					"cacheTimeout": "5m",
					"publicAccess": true,
				},
				// REST: auto-generated as GET /api/v1/users
			},
		},

		// Auto-generated: GET /api/v1/users/:id
		QueryConfig{
			Query:   &GetUserByIDQuery{},
			Handler: service.GetUserByIDHandler,
			Metadata: Metadata{
				Version:     "1.0",
				Description: "Get user by ID",
				Tags:        []string{"user", "get", "detail"},
				Annotations: map[string]interface{}{
					"cacheable":    true,
					"cacheTimeout": "10m",
				},
				// REST: auto-generated as GET /api/v1/users/:id
			},
		},
	)

	// Start with comprehensive logging
	ctx := context.Background()
	app.Start(ctx)

	fmt.Println("\n✨ Key Benefits Demonstrated:")
	fmt.Println("  • Zero boilerplate - no factory interfaces")
	fmt.Println("  • Concrete handlers - no type assertions")
	fmt.Println("  • Auto-generated endpoints from naming conventions")
	fmt.Println("  • Multi-source parameter binding (path + query + body)")
	fmt.Println("  • Rich metadata with versions, descriptions, and annotations")
	fmt.Println("  • Comprehensive startup logging")
	fmt.Println("  • Backwards compatibility with Server/NewServer")
}

// MCP Integration Example

func ExampleMCPIntegration() {
	fmt.Println("🤖 CQRS SDK with MCP Integration Example")
	fmt.Println("========================================")

	// Create service
	service := NewUserService(nil)

	// Create engine with HTTP transport
	engine := NewEngine("mcp-user-service", WithHTTP(8080))

	// Enable CORS for browser-based AI tools
	g := engine.Gin()
	g.Use(cors.New(cors.Config{
		AllowAllOrigins:  true,
		AllowHeaders:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowCredentials: false,
	}))

	// Register commands with MCP metadata
	engine.RegisterCommands(CommandConfig{
		Command: &CreateUserCommand{},
		Handler: service.CreateUserHandler,
		Metadata: Metadata{
			Version:     "1.0",
			Description: "Creates a user aggregate",
			MCP: &MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "command",
				RequestSchemaRef:  "#/components/schemas/CreateUserRequest",
				ResponseSchemaRef: "#/components/schemas/CreateUserResponse",
			},
		},
	})

	engine.RegisterCommands(CommandConfig{
		Command: &UpdateUserCommand{},
		Handler: service.UpdateUserHandler,
		Metadata: Metadata{
			Version:     "1.0",
			Description: "Updates an existing user",
			MCP: &MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "command",
				RequestSchemaRef:  "#/components/schemas/UpdateUserRequest",
				ResponseSchemaRef: "#/components/schemas/UpdateUserResponse",
			},
		},
	})

	// Register queries with MCP metadata
	engine.RegisterQueries(QueryConfig{
		Query:   &GetUsersQuery{},
		Handler: service.GetUsersHandler,
		Metadata: Metadata{
			Version:     "1.0",
			Description: "Get all users with pagination",
			MCP: &MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "query",
				RequestSchemaRef:  "#/components/schemas/GetUsersRequest",
				ResponseSchemaRef: "#/components/schemas/GetUsersResponse",
			},
		},
	})

	engine.RegisterQueries(QueryConfig{
		Query:   &GetUserByIDQuery{},
		Handler: service.GetUserByIDHandler,
		Metadata: Metadata{
			Version:     "1.0",
			Description: "Get user by ID",
			MCP: &MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "query",
				RequestSchemaRef:  "#/components/schemas/GetUserByIDRequest",
				ResponseSchemaRef: "#/components/schemas/GetUserByIDResponse",
			},
		},
	})

	// MCP endpoints are automatically created by the SDK
	// Available at:
	// - POST /mcp/jsonrpc (MCP JSON-RPC endpoint)
	// - GET /mcp/tools/list (Tool discovery)
	// - POST /mcp/tools/call (Tool execution)

	ctx := context.Background()
	engine.Start(ctx)

	fmt.Println("\n🤖 MCP Features Demonstrated:")
	fmt.Println("  • Automatic MCP endpoint creation")
	fmt.Println("  • JSON schema generation from Go structs")
	fmt.Println("  • AI tool discovery via tools/list")
	fmt.Println("  • Tool execution via tools/call")
	fmt.Println("  • CORS enabled for browser-based AI tools")
	fmt.Println("  • Type-safe command/query execution")
}

// Advanced example with custom paths and multi-transport

func ExampleAdvancedFeatures() {
	service := NewUserService(nil)

	app := NewApp("advanced-service", WithHTTP(8080))

	// Custom REST configuration
	app.RegisterCommands(
		CommandConfig{
			Command: &CreateUserCommand{},
			Handler: service.CreateUserHandler,
			Metadata: Metadata{
				Version:     "2.0",
				Description: "Advanced user creation",
				REST: &REST{
					Method: "POST",
					Path:   "/api/v2/advanced/users", // Custom path
				},
				PubSub: &PubSub{
					Topic: "user-commands",
					Event: "user.created", // Event filtering
				},
				MCP: &MCP{
					ContextType:       "aggregate",
					Version:           "2.0.0",
					OperationCategory: "command",
					RequestSchemaRef:  "#/components/schemas/CreateUserRequest",
					ResponseSchemaRef: "#/components/schemas/CreateUserResponse",
					AuthHint:          "jwt",
					TimeoutSeconds:    30,
				},
				Annotations: map[string]interface{}{
					"feature":      "advanced",
					"experimental": true,
				},
			},
		},
	)

	fmt.Println("🔧 Advanced Features:")
	fmt.Println("  • Custom REST paths")
	fmt.Println("  • Multi-transport (HTTP + PubSub + MCP)")
	fmt.Println("  • Event filtering")
	fmt.Println("  • MCP with authentication hints")
	fmt.Println("  • Extensible annotations")
}
