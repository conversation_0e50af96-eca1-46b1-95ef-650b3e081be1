package cqrs

import (
	"context"
	"fmt"

	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
)

// DaprSubscriber implements Subscriber interface for Dapr
type DaprSubscriber struct {
	pubsubName    string
	port          int
	service       common.Service
	subscriptions []subscriptionInfo
}

type subscriptionInfo struct {
	topic   string
	route   string
	handler func(ctx context.Context, data []byte) error
}

// NewDaprSubscriber creates a new Dapr subscriber
func NewDaprSubscriber(pubsubName string, port int) *DaprSubscriber {
	return &DaprSubscriber{
		pubsubName:    pubsubName,
		port:          port,
		subscriptions: make([]subscriptionInfo, 0),
	}
}

// Subscribe registers a subscription for a topic and route
func (s *DaprSubscriber) Subscribe(topic, route string, handler func(ctx context.Context, data []byte) error) error {
	s.subscriptions = append(s.subscriptions, subscriptionInfo{
		topic:   topic,
		route:   route,
		handler: handler,
	})

	fmt.Printf("Registered subscription: topic=%s, route=%s\n", topic, route)
	return nil
}

// Start starts the Dapr subscriber service
func (s *DaprSubscriber) Start(ctx context.Context) error {
	// Create Dapr HTTP service on specified port
	service := daprd.NewService(fmt.Sprintf(":%d", s.port))
	s.service = service

	// Register all subscriptions
	for _, sub := range s.subscriptions {
		subscription := &common.Subscription{
			PubsubName: s.pubsubName,
			Topic:      sub.topic,
			Route:      sub.route,
		}

		handler := s.createTopicHandler(sub.handler)
		if err := service.AddTopicEventHandler(subscription, handler); err != nil {
			return fmt.Errorf("failed to add topic handler for %s: %w", sub.topic, err)
		}

		fmt.Printf("Added topic handler: topic=%s, route=%s\n", sub.topic, sub.route)
	}

	fmt.Printf("Starting Dapr subscriber on :%d\n", s.port)
	return service.Start()
}

// createTopicHandler creates a Dapr topic event handler
func (s *DaprSubscriber) createTopicHandler(handler func(ctx context.Context, data []byte) error) common.TopicEventHandler {
	return func(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
		fmt.Printf("Received event on topic %s: %s\n", e.Topic, string(e.RawData))

		if err := handler(ctx, e.RawData); err != nil {
			fmt.Printf("Error handling event: %v\n", err)
			return false, err // Don't retry by default
		}

		fmt.Printf("Successfully handled event on topic %s\n", e.Topic)
		return false, nil
	}
}
