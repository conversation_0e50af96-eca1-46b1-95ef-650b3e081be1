package cqrs

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// DaprPublisher implements Publisher interface for Dapr
type DaprPublisher struct {
	pubsubName  string
	daprAddress string
	httpClient  *http.Client
}

// NewDaprPublisher creates a new Dapr publisher
func NewDaprPublisher(pubsubName, daprAddress string) *DaprPublisher {
	if daprAddress == "" {
		daprAddress = "http://localhost:3500"
	}

	return &DaprPublisher{
		pubsubName:  pubsubName,
		daprAddress: daprAddress,
		httpClient:  &http.Client{},
	}
}

// PublishEvent publishes an event to a topic using Dapr
func (p *DaprPublisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	data, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event: %w", err)
	}

	url := fmt.Sprintf("%s/v1.0/publish/%s/%s", p.da<PERSON><PERSON><PERSON><PERSON><PERSON>, p.pubsub<PERSON>, topic)

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := p.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to publish event: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to publish event: %s - %s", resp.Status, string(body))
	}

	return nil
}
