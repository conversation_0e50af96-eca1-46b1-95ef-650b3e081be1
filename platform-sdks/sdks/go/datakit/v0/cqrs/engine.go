package cqrs

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"regexp"
	"sort"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
)

// Engine manages commands and queries with automatic endpoint generation and multi-transport support
type Engine struct {
	mu sync.RWMutex

	serviceName string
	httpPort    int

	// Command and query storage
	commands map[string]*CommandConfig // keyed by command.Name()
	queries  map[string]*QueryConfig   // keyed by query.Name()

	// HTTP server
	gin        *gin.Engine
	httpServer *http.Server

	// PubSub components
	publisher  Publisher
	subscriber Subscriber

	// Configuration
	httpPathPrefix string

	// Route registry to detect collisions (METHOD path -> cmdName)
	routes map[string]string

	// MCP manager
	mcpManager *MCPManager
}

// EngineOption configures the engine
type EngineOption func(*Engine)

// NewEngine creates an engine with the specified service name and options
func NewEngine(serviceName string, opts ...EngineOption) *Engine {
	gin.SetMode(gin.ReleaseMode) // Reduce logs

	e := &Engine{
		serviceName:    serviceName,
		commands:       make(map[string]*CommandConfig),
		queries:        make(map[string]*QueryConfig),
		gin:            gin.New(),
		httpPathPrefix: "/api/v1",
		routes:         make(map[string]string),
	}

	// Initialize MCP manager
	e.mcpManager = NewMCPManager(e)

	// Apply options
	for _, opt := range opts {
		opt(e)
	}

	// Setup middleware
	e.gin.Use(gin.Recovery())
	e.setupHealthCheck()

	return e
}

// WithHTTP enables HTTP transport on the specified port
func WithHTTP(port int) EngineOption {
	return func(e *Engine) {
		e.httpPort = port
	}
}

// WithPublisher sets a custom publisher
func WithPublisher(publisher Publisher) EngineOption {
	return func(e *Engine) {
		e.publisher = publisher
	}
}

// WithSubscriber sets a custom subscriber with default port
func WithSubscriber(pubsubName, daprAddress string, port int) EngineOption {
	return func(e *Engine) {
		// Use the provided port parameter
		e.subscriber = NewDaprSubscriber(pubsubName, port)

		// Create publisher with daprAddress for communication with Dapr
		if e.publisher == nil {
			e.publisher = NewDaprPublisher(pubsubName, daprAddress)
		}
	}
}

// WithSubscriberPort sets a custom subscriber with explicit port
func WithSubscriberPort(pubsubName, daprAddress string, port int) EngineOption {
	return func(e *Engine) {
		e.subscriber = NewDaprSubscriber(pubsubName, port)

		// Create publisher with daprAddress for communication with Dapr
		if e.publisher == nil {
			e.publisher = NewDaprPublisher(pubsubName, daprAddress)
		}
	}
}

// WithHTTPPathPrefix sets the HTTP path prefix for endpoints
func WithHTTPPathPrefix(prefix string) EngineOption {
	return func(e *Engine) {
		e.httpPathPrefix = prefix
	}
}

// RegisterCommands registers multiple commands with comprehensive logging
func (e *Engine) RegisterCommands(configs ...CommandConfig) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	for _, config := range configs {
		if err := e.registerCommand(config); err != nil {
			return err
		}
	}

	return nil
}

// registerCommand processes a single command with auto-generation and concrete handler detection
func (e *Engine) registerCommand(config CommandConfig) error {
	cmdName := config.Command.Name()

	// If no factory is provided, create one using reflection
	if config.Factory == nil {
		cmdType := reflect.TypeOf(config.Command)
		config.Factory = func() Command {
			cmdValue := reflect.New(cmdType.Elem()).Interface()
			return cmdValue.(Command)
		}
	}

	// Validate
	if config.Command == nil {
		return fmt.Errorf("command cannot be nil")
	}
	if config.Handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	if cmdName == "" {
		return fmt.Errorf("command name cannot be empty")
	}

	// Check for duplicates
	if _, exists := e.commands[cmdName]; exists {
		return fmt.Errorf("command already registered: %s", cmdName)
	}

	// Evaluate and validate MCP metadata - throw error if MCP is intended but incomplete
	if err := e.mcpManager.validateMCP(cmdName, config.Metadata.MCP); err != nil {
		return fmt.Errorf("failed to validate MCP for %s: %w", cmdName, err)
	}

	// Detect and wrap concrete handlers at registration time (one-time reflection)
	wrappedHandler, err := e.wrapCommandHandler(config.Handler, config.Command)
	if err != nil {
		return fmt.Errorf("failed to wrap command handler for %s: %w", cmdName, err)
	}

	// Create new config with wrapped handler
	wrappedConfig := config
	wrappedConfig.Handler = wrappedHandler

	// Auto-generate REST configuration if not provided
	if wrappedConfig.Metadata.REST == nil && e.httpPort > 0 {
		wrappedConfig.Metadata.REST = e.generateRESTConfig(cmdName, true)
	}

	// Setup MCP-derived REST endpoint if MCP metadata exists
	if m := wrappedConfig.Metadata.MCP; m != nil && e.httpPort > 0 {
		mcpPath := e.mcpManager.deriveMCPPath(cmdName, m)

		// Clone config to reuse setupHTTPEndpoint without touching primary REST config
		mcpClone := wrappedConfig                  // shallow copy is fine
		mcpClone.Metadata = wrappedConfig.Metadata // copy struct value
		mcpClone.Metadata.REST = &REST{Method: "POST", Path: mcpPath}

		// Record the path for introspection
		m.PathOverride = mcpPath

		// Setup route via existing helper
		e.setupHTTPEndpoint(cmdName, &mcpClone)
	}

	// Store command
	e.commands[cmdName] = &wrappedConfig

	// Setup HTTP endpoint if configured
	if wrappedConfig.Metadata.REST != nil && e.httpPort > 0 {
		e.setupHTTPEndpoint(cmdName, &wrappedConfig)
	}

	// Setup PubSub subscription if configured
	if wrappedConfig.Metadata.PubSub != nil && e.subscriber != nil {
		e.setupPubSubSubscription(cmdName, &wrappedConfig)
	}

	return nil
}

// wrapCommandHandler detects handler type and creates zero-reflection wrapper
func (e *Engine) wrapCommandHandler(handler interface{}, cmd Command) (CommandHandler, error) {
	handlerValue := reflect.ValueOf(handler)
	handlerType := handlerValue.Type()

	// Check if it's already a CommandHandler
	if cmdHandler, ok := handler.(CommandHandler); ok {
		return cmdHandler, nil
	}

	// Check if it's a concrete handler function
	if handlerType.Kind() == reflect.Func && handlerType.NumIn() == 2 {
		// Check first parameter is context.Context
		if handlerType.In(0) == reflect.TypeOf((*context.Context)(nil)).Elem() {
			// Check if second parameter matches the concrete command type
			cmdType := reflect.TypeOf(cmd)
			if handlerType.In(1) == cmdType {
				// Create zero-reflection wrapper (one-time reflection setup)
				return func(ctx context.Context, command Command) error {
					// Direct function call with type assertion - no runtime reflection
					args := []reflect.Value{
						reflect.ValueOf(ctx),
						reflect.ValueOf(command),
					}
					results := handlerValue.Call(args)
					if len(results) > 0 && !results[0].IsNil() {
						return results[0].Interface().(error)
					}
					return nil
				}, nil
			}
		}
	}

	return nil, fmt.Errorf("invalid handler type: expected func(context.Context, *%T) error or CommandHandler", cmd)
}

// generateRESTConfig creates REST configuration based on naming conventions
func (e *Engine) generateRESTConfig(name string, isCommand bool) *REST {
	// Extract resource name from command/query name
	resourceName := e.extractResourceName(name)

	if isCommand {
		// Commands generate state-changing endpoints
		if strings.Contains(strings.ToLower(name), "create") {
			return &REST{
				Method: "POST",
				Path:   fmt.Sprintf("%s/%s", e.httpPathPrefix, resourceName),
			}
		} else if strings.Contains(strings.ToLower(name), "update") {
			return &REST{
				Method: "PUT",
				Path:   fmt.Sprintf("%s/%s/:id", e.httpPathPrefix, resourceName),
			}
		} else if strings.Contains(strings.ToLower(name), "delete") || strings.Contains(strings.ToLower(name), "deactivate") {
			return &REST{
				Method: "DELETE",
				Path:   fmt.Sprintf("%s/%s/:id", e.httpPathPrefix, resourceName),
			}
		} else {
			// Default command -> POST
			return &REST{
				Method: "POST",
				Path:   fmt.Sprintf("%s/%s", e.httpPathPrefix, resourceName),
			}
		}
	} else {
		// Queries generate GET endpoints
		if strings.Contains(strings.ToLower(name), "byid") || strings.Contains(strings.ToLower(name), "by_id") {
			return &REST{
				Method: "GET",
				Path:   fmt.Sprintf("%s/%s/:id", e.httpPathPrefix, resourceName),
			}
		} else {
			// Default query -> GET collection
			return &REST{
				Method: "GET",
				Path:   fmt.Sprintf("%s/%s", e.httpPathPrefix, resourceName),
			}
		}
	}
}

// extractResourceName extracts resource name from command/query name and pluralizes it
func (e *Engine) extractResourceName(name string) string {
	// Remove common suffixes
	name = regexp.MustCompile(`(?i)(Command|Query)$`).ReplaceAllString(name, "")

	// Remove action prefixes
	name = regexp.MustCompile(`^(?i)(Create|Update|Delete|Get|Search|Find|Process|Deactivate)`).ReplaceAllString(name, "")

	// Convert to kebab-case
	resource := e.toKebabCase(name)
	if resource == "" {
		resource = "items" // fallback
	}

	// Only pluralize if not already plural
	if !e.isPlural(resource) {
		resource = e.pluralize(resource)
	}

	return resource
}

// isPlural checks if a word is already plural
func (e *Engine) isPlural(s string) bool {
	if s == "" {
		return false
	}

	// Common plural patterns
	if strings.HasSuffix(s, "s") && !strings.HasSuffix(s, "ss") {
		return true
	}
	if strings.HasSuffix(s, "ies") {
		return true
	}
	if strings.HasSuffix(s, "ves") {
		return true
	}

	return false
}

// toKebabCase converts PascalCase to kebab-case
func (e *Engine) toKebabCase(s string) string {
	// Insert hyphens before uppercase letters (except the first one)
	re := regexp.MustCompile(`([a-z])([A-Z])`)
	s = re.ReplaceAllString(s, `${1}-${2}`)
	return strings.ToLower(s)
}

// pluralize adds 's' to make resource names plural (simple implementation)
func (e *Engine) pluralize(s string) string {
	if s == "" {
		return s
	}

	// Handle some common cases
	if strings.HasSuffix(s, "tion") {
		return s + "s" // notification -> notifications
	}
	if strings.HasSuffix(s, "y") {
		return strings.TrimSuffix(s, "y") + "ies"
	}
	if strings.HasSuffix(s, "s") || strings.HasSuffix(s, "x") {
		return s + "es"
	}
	if strings.HasSuffix(s, "z") {
		return s + "zes" // quiz -> quizzes
	}

	return s + "s"
}

// setupHTTPEndpoint creates HTTP routes for the command with zero-reflection execution
func (e *Engine) setupHTTPEndpoint(cmdName string, config *CommandConfig) {
	rest := config.Metadata.REST

	handler := func(c *gin.Context) {
		// Get the stored config to access the factory
		e.mu.RLock()
		storedConfig, exists := e.commands[cmdName]
		e.mu.RUnlock()

		if !exists {
			c.JSON(500, gin.H{"error": fmt.Sprintf("Command not found: %s", cmdName)})
			return
		}

		// Create command instance using the config's factory
		cmd := storedConfig.Factory()
		if cmd == nil {
			c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to create command: %s", cmdName)})
			return
		}

		// Multi-source binding: URI parameters, query parameters, and JSON body
		if err := c.ShouldBindUri(cmd); err != nil {
			// URI binding failed, continue with other bindings
		}
		if err := c.ShouldBindQuery(cmd); err != nil {
			// Query binding failed, continue with other bindings
		}
		if err := c.ShouldBindJSON(cmd); err != nil && c.Request.ContentLength > 0 {
			c.JSON(400, gin.H{"error": fmt.Sprintf("Invalid request data: %v", err)})
			return
		}

		// Execute command using zero-reflection wrapped handler
		if err := e.HandleCommand(c.Request.Context(), cmd); err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}

		c.JSON(200, gin.H{"status": "success"})
	}

	// Register route
	if err := e.mustRegisterRoute(rest.Method, rest.Path, cmdName); err != nil {
		// Collision: panic or print to log? Return 500 to user at runtime but here we just panic because registration time should have prevented but for safety
		fmt.Printf("registration error: %v\n", err)
	}

	switch strings.ToUpper(rest.Method) {
	case "POST":
		e.gin.POST(rest.Path, handler)
	case "PUT":
		e.gin.PUT(rest.Path, handler)
	case "PATCH":
		e.gin.PATCH(rest.Path, handler)
	case "DELETE":
		e.gin.DELETE(rest.Path, handler)
	default:
		e.gin.POST(rest.Path, handler)
	}

	// Log registration with version and description
	version := config.Metadata.Version
	if version == "" {
		version = "1.0"
	}
	description := config.Metadata.Description
	if description == "" {
		description = "No description"
	}

	fmt.Printf("  ✓ %s %s -> %s (v%s) - %s\n", rest.Method, rest.Path, cmdName, version, description)
}

// setupPubSubSubscription creates PubSub subscription for the command
func (e *Engine) setupPubSubSubscription(cmdName string, config *CommandConfig) {
	pubsub := config.Metadata.PubSub

	// Generate route if not provided
	route := fmt.Sprintf("/commands/%s", strings.ToLower(cmdName))

	handler := func(ctx context.Context, data []byte) error {
		// Get the stored config to access the factory
		e.mu.RLock()
		storedConfig, exists := e.commands[cmdName]
		e.mu.RUnlock()

		if !exists {
			return fmt.Errorf("command not found: %s", cmdName)
		}

		// Create command instance using the config's factory
		cmd := storedConfig.Factory()
		if cmd == nil {
			return fmt.Errorf("failed to create command instance for %s", cmdName)
		}

		// Parse CloudEvent or direct event data
		var eventData interface{}
		var cloudEvent struct {
			Data interface{} `json:"data"`
			Type string      `json:"type"`
		}

		if err := json.Unmarshal(data, &cloudEvent); err == nil && cloudEvent.Data != nil {
			eventData = cloudEvent.Data

			// Check event type filtering if specified
			if pubsub.Event != "" && cloudEvent.Type != pubsub.Event {
				return nil // Skip this event
			}
		} else {
			var directEvent map[string]interface{}
			if err := json.Unmarshal(data, &directEvent); err != nil {
				return fmt.Errorf("failed to unmarshal event data: %w", err)
			}
			eventData = directEvent
		}

		// Marshal and unmarshal to convert data to command
		dataBytes, err := json.Marshal(eventData)
		if err != nil {
			return fmt.Errorf("failed to marshal event data: %w", err)
		}

		if err := json.Unmarshal(dataBytes, cmd); err != nil {
			return fmt.Errorf("failed to unmarshal event data: %w", err)
		}

		// Execute command using zero-reflection wrapped handler
		return e.HandleCommand(ctx, cmd)
	}

	e.subscriber.Subscribe(pubsub.Topic, route, handler)

	// Log registration
	version := config.Metadata.Version
	if version == "" {
		version = "1.0"
	}
	eventFilter := ""
	if pubsub.Event != "" {
		eventFilter = fmt.Sprintf("/%s", pubsub.Event)
	}
	fmt.Printf("  ✓ PubSub: %s%s -> %s (v%s)\n", pubsub.Topic, eventFilter, cmdName, version)
}

// RegisterQueries registers multiple queries with auto-generation
func (e *Engine) RegisterQueries(configs ...QueryConfig) error {
	e.mu.Lock()
	defer e.mu.Unlock()

	for _, config := range configs {
		if err := e.registerQuery(config); err != nil {
			return err
		}
	}

	return nil
}

// registerQuery processes a single query with auto-generation and concrete handler detection
func (e *Engine) registerQuery(config QueryConfig) error {
	queryName := config.Query.Name()

	// If no factory is provided, create one using reflection
	if config.Factory == nil {
		queryType := reflect.TypeOf(config.Query)
		config.Factory = func() Query {
			queryValue := reflect.New(queryType.Elem()).Interface()
			return queryValue.(Query)
		}
	}

	// Validate
	if config.Query == nil {
		return fmt.Errorf("query cannot be nil")
	}
	if config.Handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}
	if queryName == "" {
		return fmt.Errorf("query name cannot be empty")
	}

	// Check for duplicates
	if _, exists := e.queries[queryName]; exists {
		return fmt.Errorf("query already registered: %s", queryName)
	}

	// Detect and wrap concrete handlers at registration time (one-time reflection)
	wrappedHandler, err := e.wrapQueryHandler(config.Handler, config.Query)
	if err != nil {
		return fmt.Errorf("failed to wrap query handler for %s: %w", queryName, err)
	}

	// Create new config with wrapped handler
	wrappedConfig := config
	wrappedConfig.Handler = wrappedHandler

	// Auto-generate REST configuration if not provided
	if wrappedConfig.Metadata.REST == nil && e.httpPort > 0 {
		wrappedConfig.Metadata.REST = e.generateRESTConfig(queryName, false)
	}

	// Store query
	e.queries[queryName] = &wrappedConfig

	// Setup HTTP endpoint for query if configured
	if wrappedConfig.Metadata.REST != nil && e.httpPort > 0 {
		e.setupHTTPQueryEndpoint(queryName, &wrappedConfig)
	}

	return nil
}

// wrapQueryHandler detects handler type and creates zero-reflection wrapper
func (e *Engine) wrapQueryHandler(handler interface{}, qry Query) (QueryHandler, error) {
	handlerValue := reflect.ValueOf(handler)
	handlerType := handlerValue.Type()

	// Check if it's already a QueryHandler
	if qryHandler, ok := handler.(QueryHandler); ok {
		return qryHandler, nil
	}

	// Check if it's a concrete handler function
	if handlerType.Kind() == reflect.Func && handlerType.NumIn() == 2 {
		// Check first parameter is context.Context
		if handlerType.In(0) == reflect.TypeOf((*context.Context)(nil)).Elem() {
			// Check if second parameter matches the concrete query type
			qryType := reflect.TypeOf(qry)
			if handlerType.In(1) == qryType {
				// Create zero-reflection wrapper (one-time reflection setup)
				return func(ctx context.Context, query Query) (interface{}, error) {
					// Direct function call with type assertion - no runtime reflection
					args := []reflect.Value{
						reflect.ValueOf(ctx),
						reflect.ValueOf(query),
					}
					results := handlerValue.Call(args)

					var result interface{}
					var err error

					if len(results) > 0 && !results[0].IsNil() {
						result = results[0].Interface()
					}
					if len(results) > 1 && !results[1].IsNil() {
						err = results[1].Interface().(error)
					}

					return result, err
				}, nil
			}
		}
	}

	return nil, fmt.Errorf("invalid handler type: expected func(context.Context, *%T) (interface{}, error) or QueryHandler", qry)
}

// setupHTTPQueryEndpoint creates HTTP routes for queries with zero-reflection execution
func (e *Engine) setupHTTPQueryEndpoint(queryName string, config *QueryConfig) {
	rest := config.Metadata.REST

	handler := func(c *gin.Context) {
		// Get the stored config to access the factory
		e.mu.RLock()
		storedConfig, exists := e.queries[queryName]
		e.mu.RUnlock()

		if !exists {
			c.JSON(500, gin.H{"error": fmt.Sprintf("Query not found: %s", queryName)})
			return
		}

		// Create query instance using the config's factory
		qry := storedConfig.Factory()
		if qry == nil {
			c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to create query: %s", queryName)})
			return
		}

		// Multi-source binding: URI parameters and query parameters
		if err := c.ShouldBindUri(qry); err != nil {
			// URI binding failed, continue
		}
		if err := c.ShouldBindQuery(qry); err != nil {
			c.JSON(400, gin.H{"error": fmt.Sprintf("Invalid query parameters: %v", err)})
			return
		}

		// Execute query using zero-reflection wrapped handler
		result, err := e.HandleQuery(c.Request.Context(), qry)
		if err != nil {
			c.JSON(500, gin.H{"error": err.Error()})
			return
		}

		c.JSON(200, result)
	}

	// Register GET route in gin, but first check collision registry
	if err := e.mustRegisterRoute("GET", rest.Path, queryName); err != nil {
		fmt.Printf("registration error: %v\n", err)
	}
	e.gin.GET(rest.Path, handler)

	// Log registration
	version := config.Metadata.Version
	if version == "" {
		version = "1.0"
	}
	description := config.Metadata.Description
	if description == "" {
		description = "No description"
	}

	fmt.Printf("  ✓ GET %s -> %s (v%s) - %s\n", rest.Path, queryName, version, description)
}

// HandleCommand executes a command using the wrapped handler (zero reflection)
func (e *Engine) HandleCommand(ctx context.Context, cmd Command) error {
	cmdName := cmd.Name()

	e.mu.RLock()
	config, exists := e.commands[cmdName]
	e.mu.RUnlock()

	if !exists {
		return fmt.Errorf("command handler not found: %s", cmdName)
	}

	// Call wrapped handler - zero reflection at runtime
	wrappedHandler := config.Handler.(CommandHandler)
	return wrappedHandler(ctx, cmd)
}

// HandleQuery executes a query using the wrapped handler (zero reflection)
func (e *Engine) HandleQuery(ctx context.Context, qry Query) (interface{}, error) {
	queryName := qry.Name()

	e.mu.RLock()
	config, exists := e.queries[queryName]
	e.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("query handler not found: %s", queryName)
	}

	// Call wrapped handler - zero reflection at runtime
	wrappedHandler := config.Handler.(QueryHandler)
	return wrappedHandler(ctx, qry)
}

// Publisher returns the configured publisher
func (e *Engine) Publisher() Publisher {
	return e.publisher
}

// Start starts the engine with comprehensive logging
func (e *Engine) Start(ctx context.Context) error {
	fmt.Printf("\n🚀 Starting %s...\n", e.serviceName)

	// Log configuration
	if e.httpPort > 0 {
		fmt.Printf("✓ HTTP server configured on :%d\n", e.httpPort)
	}
	if e.subscriber != nil {
		fmt.Printf("✓ PubSub subscriber configured\n")
	}

	// Log registered commands
	if len(e.commands) > 0 {
		fmt.Printf("\nRegistered Commands:\n")
		for _, config := range e.commands {
			cmdName := config.Command.Name()
			version := config.Metadata.Version
			if version == "" {
				version = "1.0"
			}

			if config.Metadata.REST != nil {
				rest := config.Metadata.REST
				fmt.Printf("  - %s -> %s %s (v%s)\n", cmdName, rest.Method, rest.Path, version)
			}
			if config.Metadata.PubSub != nil {
				pubsub := config.Metadata.PubSub
				eventFilter := ""
				if pubsub.Event != "" {
					eventFilter = fmt.Sprintf("/%s", pubsub.Event)
				}
				fmt.Printf("  - %s -> PubSub: %s%s (v%s)\n", cmdName, pubsub.Topic, eventFilter, version)
			}
			if config.Metadata.MCP != nil {
				mcp := config.Metadata.MCP
				fmt.Printf("  - %s -> MCP %s (v%s)\n", cmdName, mcp.PathOverride, mcp.Version)
			}
		}
	}

	// Log registered queries
	if len(e.queries) > 0 {
		fmt.Printf("\nRegistered Queries:\n")
		for _, config := range e.queries {
			queryName := config.Query.Name()
			version := config.Metadata.Version
			if version == "" {
				version = "1.0"
			}

			if config.Metadata.REST != nil {
				rest := config.Metadata.REST
				fmt.Printf("  - %s -> %s %s (v%s)\n", queryName, rest.Method, rest.Path, version)
			}
		}
	}

	// Setup MCP endpoints if any commands/queries have MCP metadata
	if e.httpPort > 0 {
		e.mcpManager.SetupMCPEndpoints()
	}

	// Start HTTP server
	if e.httpPort > 0 {
		e.httpServer = &http.Server{
			Addr:    fmt.Sprintf(":%d", e.httpPort),
			Handler: e.gin,
		}

		go func() {
			fmt.Printf("\n🌐 HTTP server listening on :%d\n", e.httpPort)
			if err := e.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				fmt.Printf("❌ HTTP server error: %v\n", err)
			}
		}()
	}

	// Start subscriber
	if e.subscriber != nil {
		go func() {
			fmt.Printf("📡 Starting PubSub subscriber...\n")
			if err := e.subscriber.Start(ctx); err != nil {
				fmt.Printf("❌ Subscriber error: %v\n", err)
			}
		}()
	}

	fmt.Printf("\n🎉 %s started successfully!\n\n", e.serviceName)
	return nil
}

// setupHealthCheck adds health check endpoint
func (e *Engine) setupHealthCheck() {
	e.gin.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": e.serviceName,
		})
	})
}

func (e *Engine) mustRegisterRoute(method, path, owner string) error {
	key := strings.ToUpper(method) + " " + path
	if existing, ok := e.routes[key]; ok {
		return fmt.Errorf("route %s already registered by %s", key, existing)
	}
	e.routes[key] = owner
	return nil
}

// expose gin router and tool list for custom integrations
// Gin returns the underlying *gin.Engine so callers can register additional routes
func (e *Engine) Gin() *gin.Engine {
	return e.gin
}

// ToolNames returns a sorted slice with the names of all registered commands and queries.
func (e *Engine) ToolNames() []string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	names := make([]string, 0, len(e.commands)+len(e.queries))
	for n := range e.commands {
		names = append(names, n)
	}
	for n := range e.queries {
		names = append(names, n)
	}
	sort.Strings(names)
	return names
}

func (e *Engine) CommandConfigByName(name string) (*CommandConfig, bool) {
	e.mu.RLock()
	defer e.mu.RUnlock()
	cfg, ok := e.commands[name]
	return cfg, ok
}

// QueryConfigByName returns the registered QueryConfig, or false if unknown.
func (e *Engine) QueryConfigByName(name string) (*QueryConfig, bool) {
	e.mu.RLock()
	defer e.mu.RUnlock()
	cfg, ok := e.queries[name]
	return cfg, ok
}

// GetServiceName returns the service name
func (e *Engine) GetServiceName() string {
	return e.serviceName
}

// GetGin returns the gin router
func (e *Engine) GetGin() MCPGinRouter {
	return e.gin
}

// ToKebabCase converts PascalCase to kebab-case
func (e *Engine) ToKebabCase(s string) string {
	return e.toKebabCase(s)
}

// HasMCPCommands checks if any commands have MCP metadata
func (e *Engine) HasMCPCommands() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()

	for _, config := range e.commands {
		if config.Metadata.MCP != nil {
			return true
		}
	}
	return false
}

// HasMCPQueries checks if any queries have MCP metadata
func (e *Engine) HasMCPQueries() bool {
	e.mu.RLock()
	defer e.mu.RUnlock()

	for _, config := range e.queries {
		if config.Metadata.MCP != nil {
			return true
		}
	}
	return false
}
