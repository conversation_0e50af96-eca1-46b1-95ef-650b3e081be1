package cqrs

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
)

// JSON-RPC types for MCP communication are defined in engine.go

// MCP endpoint paths and configuration
const (
	MCPJSONRPCPath     = "/jsonrpc"
	MCPToolsPath       = "/tools"
	MCPDebugPath       = "/debug/mcp"
	MCPProtocolVersion = "2024-11-05"
)

// JSON-RPC error codes
const (
	JSONRPCParseError     = -32700
	JSONRPCInvalidRequest = -32600
	JSONRPCMethodNotFound = -32601
	JSONRPCInvalidParams  = -32602
	JSONRPCInternalError  = -32603
	JSONRPCServerError    = -32000
)

// NewMCPManager creates a new MCP manager
func NewMCPManager(engine MCPEngine) *MCPManager {
	return &MCPManager{engine: engine}
}

// SetupMCPEndpoints creates JSON-RPC endpoints for MCP communication
func (m *MCPManager) SetupMCPEndpoints() {
	// Check if any commands or queries have MCP metadata
	hasMCPCommands := m.engine.HasMCPCommands()
	hasMCPQueries := m.engine.HasMCPQueries()

	// Only setup MCP endpoints if there are MCP-enabled commands or queries
	if !hasMCPCommands && !hasMCPQueries {
		return
	}

	ginRouter := m.engine.GetGin()

	// JSON-RPC endpoint for MCP communication
	ginRouter.POST(MCPJSONRPCPath, m.handleJSONRPC)

	// Programmatic MCP Tools Discovery Endpoint
	ginRouter.GET(MCPToolsPath, m.handleToolsList)

	// Debug endpoint for MCP introspection
	ginRouter.GET(MCPDebugPath, m.handleDebugMCP)
}

// handleJSONRPC handles JSON-RPC requests for MCP communication
func (m *MCPManager) handleJSONRPC(c *gin.Context) {
	var req JSONRPCRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(200, JSONRPCResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &JSONRPCError{
				Code:    JSONRPCParseError,
				Message: "Parse error",
			},
		})
		return
	}

	switch req.Method {
	case "initialize":
		m.handleInitialize(c, req)
	case "tools/list":
		m.handleToolsListRPC(c, req)
	case "tools/call":
		m.handleToolsCall(c, req)
	default:
		c.JSON(200, JSONRPCResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &JSONRPCError{
				Code:    JSONRPCMethodNotFound,
				Message: "Method not found",
			},
		})
	}
}

// handleInitialize handles the MCP initialize request
func (m *MCPManager) handleInitialize(c *gin.Context, req JSONRPCRequest) {
	c.JSON(200, JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"protocolVersion": MCPProtocolVersion,
			"capabilities": map[string]interface{}{
				"tools": map[string]interface{}{},
			},
			"serverInfo": map[string]interface{}{
				"name":    m.engine.GetServiceName(),
				"version": m.getServerVersion(),
			},
		},
	})
}

// handleToolsListRPC handles the tools/list JSON-RPC request
func (m *MCPManager) handleToolsListRPC(c *gin.Context, req JSONRPCRequest) {
	tools := []map[string]interface{}{}

	// Add commands as tools
	for _, name := range m.engine.ToolNames() {
		if cfg, ok := m.engine.CommandConfigByName(name); ok && cfg.Metadata.MCP != nil {
			tool := map[string]interface{}{
				"name":        name,
				"description": cfg.Metadata.Description,
				"inputSchema": m.buildInputSchema(cfg.Command),
			}
			tools = append(tools, tool)
		} else if cfg, ok := m.engine.QueryConfigByName(name); ok && cfg.Metadata.MCP != nil {
			tool := map[string]interface{}{
				"name":        name,
				"description": cfg.Metadata.Description,
				"inputSchema": m.buildInputSchema(cfg.Query),
			}
			tools = append(tools, tool)
		}
	}

	c.JSON(200, JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"tools": tools,
		},
	})
}

// handleToolsCall handles the tools/call JSON-RPC request
func (m *MCPManager) handleToolsCall(c *gin.Context, req JSONRPCRequest) {
	var params struct {
		Name      string                 `json:"name"`
		Arguments map[string]interface{} `json:"arguments"`
	}

	if err := json.Unmarshal(req.Params, &params); err != nil {
		c.JSON(200, JSONRPCResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &JSONRPCError{
				Code:    JSONRPCInvalidParams,
				Message: "Invalid params",
			},
		})
		return
	}

	// Try to execute as command
	if cfg, ok := m.engine.CommandConfigByName(params.Name); ok && cfg.Metadata.MCP != nil {
		m.executeCommand(c, req, params, cfg)
		return
	}

	// Try to execute as query
	if cfg, ok := m.engine.QueryConfigByName(params.Name); ok && cfg.Metadata.MCP != nil {
		m.executeQuery(c, req, params, cfg)
		return
	}

	c.JSON(200, JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Error: &JSONRPCError{
			Code:    JSONRPCMethodNotFound,
			Message: "Method not found",
		},
	})
}

// executeCommand executes a command and returns the result
func (m *MCPManager) executeCommand(c *gin.Context, req JSONRPCRequest, params struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}, cfg *CommandConfig) {
	cmd := cfg.Factory()

	// Map arguments to command
	if err := m.mapArgumentsToStruct(params.Arguments, cmd); err != nil {
		c.JSON(200, JSONRPCResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &JSONRPCError{
				Code:    JSONRPCInvalidParams,
				Message: fmt.Sprintf("Invalid arguments: %v", err),
			},
		})
		return
	}

	// Execute command
	if err := m.engine.HandleCommand(c.Request.Context(), cmd); err != nil {
		c.JSON(200, JSONRPCResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &JSONRPCError{
				Code:    JSONRPCServerError,
				Message: fmt.Sprintf("Command execution failed: %v", err),
			},
		})
		return
	}

	// Generate response based on command type
	resultText := m.generateCommandResponse(cmd)

	c.JSON(200, JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": resultText,
				},
			},
		},
	})
}

// executeQuery executes a query and returns the result
func (m *MCPManager) executeQuery(c *gin.Context, req JSONRPCRequest, params struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}, cfg *QueryConfig) {
	qry := cfg.Factory()

	// Map arguments to query
	if err := m.mapArgumentsToStruct(params.Arguments, qry); err != nil {
		c.JSON(200, JSONRPCResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &JSONRPCError{
				Code:    JSONRPCInvalidParams,
				Message: fmt.Sprintf("Invalid arguments: %v", err),
			},
		})
		return
	}

	// Execute query
	result, err := m.engine.HandleQuery(c.Request.Context(), qry)
	if err != nil {
		c.JSON(200, JSONRPCResponse{
			JSONRPC: "2.0",
			ID:      req.ID,
			Error: &JSONRPCError{
				Code:    JSONRPCServerError,
				Message: fmt.Sprintf("Query execution failed: %v", err),
			},
		})
		return
	}

	resultText := "Query executed successfully"
	if result != nil {
		resultBytes, _ := json.Marshal(result)
		resultText = string(resultBytes)
	}

	c.JSON(200, JSONRPCResponse{
		JSONRPC: "2.0",
		ID:      req.ID,
		Result: map[string]interface{}{
			"content": []map[string]interface{}{
				{
					"type": "text",
					"text": resultText,
				},
			},
		},
	})
}

// generateCommandResponse generates a generic response message for commands
func (m *MCPManager) generateCommandResponse(cmd Command) string {
	cmdName := cmd.Name()

	// Try to get ID if available
	if cmdWithID, ok := cmd.(interface{ GetID() string }); ok {
		if id := cmdWithID.GetID(); id != "" {
			// Try to get a name field for better response formatting
			if nameField := m.extractNameField(cmd); nameField != "" {
				// Determine the entity type from command name
				entityType := m.extractEntityType(cmdName)
				if strings.Contains(strings.ToLower(cmdName), "create") {
					return fmt.Sprintf("%s '%s' created successfully with ID: %s", entityType, nameField, id)
				}
				return fmt.Sprintf("Command executed successfully for %s '%s' with ID: %s", entityType, nameField, id)
			}
			return fmt.Sprintf("Command executed successfully with ID: %s", id)
		}
	}

	return "Command executed successfully"
}

// extractNameField tries to extract a name-like field from the command using reflection
func (m *MCPManager) extractNameField(cmd Command) string {
	v := reflect.ValueOf(cmd)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		fieldName := strings.ToLower(field.Name)

		// Look for common name patterns
		if strings.Contains(fieldName, "name") && v.Field(i).Kind() == reflect.String {
			return v.Field(i).String()
		}
	}

	return ""
}

// extractEntityType extracts the entity type from command name
func (m *MCPManager) extractEntityType(cmdName string) string {
	// Remove common suffixes and prefixes
	name := cmdName
	name = strings.TrimSuffix(name, "Command")
	name = strings.TrimSuffix(name, "Query")

	// Remove action prefixes
	for _, prefix := range []string{"Create", "Update", "Delete", "Get", "Process"} {
		if strings.HasPrefix(name, prefix) {
			name = strings.TrimPrefix(name, prefix)
			break
		}
	}

	if name == "" {
		return "Entity"
	}

	return name
}

// handleToolsList handles the GET /tools endpoint
func (m *MCPManager) handleToolsList(c *gin.Context) {
	tools := []map[string]interface{}{}

	for _, name := range m.engine.ToolNames() {
		if cfg, ok := m.engine.CommandConfigByName(name); ok {
			tool := map[string]interface{}{
				"name":        name,
				"type":        "command",
				"description": cfg.Metadata.Description,
				"version":     cfg.Metadata.Version,
			}
			if cfg.Metadata.MCP != nil {
				tool["mcp_path"] = cfg.Metadata.MCP.PathOverride
				tool["mcp_version"] = cfg.Metadata.MCP.Version
				tool["context_type"] = cfg.Metadata.MCP.ContextType
				tool["operation_category"] = cfg.Metadata.MCP.OperationCategory
			}
			if cfg.Metadata.REST != nil {
				tool["rest_method"] = cfg.Metadata.REST.Method
				tool["rest_path"] = cfg.Metadata.REST.Path
			}
			tools = append(tools, tool)
		} else if cfg, ok := m.engine.QueryConfigByName(name); ok {
			tool := map[string]interface{}{
				"name":        name,
				"type":        "query",
				"description": cfg.Metadata.Description,
				"version":     cfg.Metadata.Version,
			}
			if cfg.Metadata.REST != nil {
				tool["rest_method"] = cfg.Metadata.REST.Method
				tool["rest_path"] = cfg.Metadata.REST.Path
			}
			tools = append(tools, tool)
		}
	}

	c.JSON(200, map[string]interface{}{
		"tools":   tools,
		"count":   len(tools),
		"service": m.engine.GetServiceName(),
		"version": m.getServerVersion(),
	})
}

// handleDebugMCP handles the GET /debug/mcp endpoint
func (m *MCPManager) handleDebugMCP(c *gin.Context) {
	debug := map[string]interface{}{
		"service":    m.engine.GetServiceName(),
		"tool_count": len(m.engine.ToolNames()),
		"tools":      m.engine.ToolNames(),
		"server":     "running",
		"endpoints": map[string]string{
			"mcp_jsonrpc": "POST " + MCPJSONRPCPath,
			"tools_list":  "GET " + MCPToolsPath,
			"debug":       "GET " + MCPDebugPath,
			"health":      "GET /health",
		},
	}
	c.JSON(200, debug)
}

// buildInputSchema generates JSON schema from Go struct for MCP tools
func (m *MCPManager) buildInputSchema(obj interface{}) map[string]interface{} {
	t := reflect.TypeOf(obj)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	properties := make(map[string]interface{})
	required := []string{}

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		jsonTag := field.Tag.Get("json")
		bindingTag := field.Tag.Get("binding")

		if jsonTag == "" || jsonTag == "-" {
			continue
		}

		// Extract field name from json tag
		fieldName := strings.Split(jsonTag, ",")[0]
		if fieldName == "" {
			fieldName = strings.ToLower(field.Name)
		}

		// Determine type
		fieldType := "string"
		switch field.Type.Kind() {
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			fieldType = "integer"
		case reflect.Float32, reflect.Float64:
			fieldType = "number"
		case reflect.Bool:
			fieldType = "boolean"
		}

		properties[fieldName] = map[string]interface{}{
			"type": fieldType,
		}

		// Check if required
		if strings.Contains(bindingTag, "required") {
			required = append(required, fieldName)
		}
	}

	schema := map[string]interface{}{
		"type":       "object",
		"properties": properties,
	}

	if len(required) > 0 {
		schema["required"] = required
	}

	return schema
}

// mapArgumentsToStruct maps JSON arguments to Go struct for MCP tool execution
func (m *MCPManager) mapArgumentsToStruct(args map[string]interface{}, target interface{}) error {
	data, err := json.Marshal(args)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, target)
}

// validateMCP ensures required fields follow conventions.
func (m *MCPManager) validateMCP(cmdName string, mcp *MCP) error {
	if mcp == nil {
		return nil
	}
	if mcp.ContextType == "" {
		return fmt.Errorf("%s: MCP contextType is required", cmdName)
	}
	if !semverRx.MatchString(mcp.Version) {
		return fmt.Errorf("%s: MCP version must be semver (major.minor.patch) got %q", cmdName, mcp.Version)
	}
	if mcp.OperationCategory == "" {
		return fmt.Errorf("%s: MCP operationCategory is required", cmdName)
	}
	if mcp.RequestSchemaRef == "" {
		return fmt.Errorf("%s: MCP requestSchemaRef is required", cmdName)
	}
	if mcp.ResponseSchemaRef == "" {
		return fmt.Errorf("%s: MCP responseSchemaRef is required", cmdName)
	}
	return nil
}

// deriveMCPPath returns the REST path for the MCP endpoint following conventions
func (m *MCPManager) deriveMCPPath(cmdName string, mcp *MCP) string {
	if mcp.PathOverride != "" {
		return mcp.PathOverride
	}
	// Use only major version for routing (v1, v2 ...)
	parts := strings.Split(mcp.Version, ".")
	major := parts[0]
	return fmt.Sprintf("/mcp/%s/v%s/%s", m.engine.ToKebabCase(mcp.ContextType), major, m.engine.ToKebabCase(cmdName))
}

// getServerVersion returns the server version, with a fallback to default
func (m *MCPManager) getServerVersion() string {
	// Could be made configurable in the future via engine options
	return "1.0.0"
}
