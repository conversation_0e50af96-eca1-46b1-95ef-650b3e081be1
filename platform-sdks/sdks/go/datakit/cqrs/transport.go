package cqrs

import (
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strings"
)

// GRPC represents gRPC service invocation configuration
type GRPC struct {
	Method string // optional - defaults to command/query name without suffix
}

// REST represents REST API endpoint configuration
type REST struct {
	Method string // optional - defaults to POST for commands, GET for queries
	Path   string // optional - defaults to kebab-case of command/query name without suffix
}

// PubSub represents pub/sub topic configuration
type PubSub struct {
	Topic string // optional - defaults to kebab-case of command/query name without suffix
	Route string // optional, for Dapr route override
}

// Transport defines the available transport mechanisms for a command or query
type Transport struct {
	GRPC   *GRPC
	REST   *REST
	PubSub *PubSub
	MCP    *MCP
}

// extractTypeName extracts the type name from a command or query struct
func extractTypeName(obj any) string {
	if obj == nil {
		return ""
	}

	t := reflect.TypeOf(obj)
	// Handle pointer types
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	return t.Name()
}

// removeCommandQuerySuffix removes "Command" or "Query" suffix from type name
func removeCommandQuerySuffix(name string) string {
	if strings.HasSuffix(name, "Command") {
		return strings.TrimSuffix(name, "Command")
	}
	if strings.HasSuffix(name, "Query") {
		return strings.TrimSuffix(name, "Query")
	}
	return name
}

// toKebabCase converts PascalCase to kebab-case
func toKebabCase(s string) string {
	// Insert hyphens before uppercase letters (except the first one)
	re := regexp.MustCompile(`([a-z])([A-Z])`)
	s = re.ReplaceAllString(s, `${1}-${2}`)
	return strings.ToLower(s)
}

// applyTransportDefaults applies default values to transport configuration
func applyTransportDefaults(transport *Transport, obj any, isQuery bool) {
	typeName := extractTypeName(obj)
	baseName := removeCommandQuerySuffix(typeName)
	kebabName := toKebabCase(baseName)

	// Apply gRPC defaults
	if transport.GRPC != nil && transport.GRPC.Method == "" {
		transport.GRPC.Method = baseName
	}

	// Apply REST defaults
	if transport.REST != nil {
		if transport.REST.Method == "" {
			if isQuery {
				transport.REST.Method = "GET"
			} else {
				transport.REST.Method = "POST"
			}
		}
		if transport.REST.Path == "" {
			transport.REST.Path = "/" + kebabName
		}
	}

	// Apply PubSub defaults
	if transport.PubSub != nil && transport.PubSub.Topic == "" {
		transport.PubSub.Topic = kebabName
	}
}

// validateTransport validates transport configuration according to rules V-1 through V-6
func validateTransport(transport Transport, obj any, isQuery bool) error {
	// V-1: Transport must contain ≥1 non-nil sub-block
	if transport.GRPC == nil && transport.REST == nil && transport.PubSub == nil && transport.MCP == nil {
		return errors.New("transport must contain at least one non-nil sub-block (GRPC, REST, PubSub, or MCP)")
	}

	// V-2: gRPC Method is required when GRPC transport is specified
	if transport.GRPC != nil && transport.GRPC.Method == "" {
		return errors.New("gRPC method is required when GRPC transport is specified")
	}

	// V-3: REST Method and Path are required when REST transport is specified
	if transport.REST != nil {
		if transport.REST.Method == "" {
			return errors.New("REST method is required when REST transport is specified")
		}
		if transport.REST.Path == "" {
			return errors.New("REST path is required when REST transport is specified")
		}
	}

	// V-4: PubSub Topic is required when PubSub transport is specified
	if transport.PubSub != nil && transport.PubSub.Topic == "" {
		return errors.New("PubSub topic is required when PubSub transport is specified")
	}

	// V-5: MCP Description is required when MCP transport is specified
	if transport.MCP != nil && transport.MCP.Description == "" {
		return errors.New("MCP description is required when MCP transport is specified")
	}

	// V-6: Queries must expose at least GRPC, REST, or MCP (subscriber-only queries are disallowed)
	if isQuery {
		if transport.GRPC == nil && transport.REST == nil && transport.MCP == nil {
			return errors.New("queries must expose at least GRPC, REST, or MCP transport (subscriber-only queries are disallowed)")
		}
	}

	return nil
}

// validateTransportWithDefaults applies defaults before validation (for unit tests)
func validateTransportWithDefaults(transport Transport, obj any, isQuery bool) error {
	// Apply defaults before validation
	applyTransportDefaults(&transport, obj, isQuery)

	// Use the same validation logic
	return validateTransport(transport, obj, isQuery)
}

// hasGRPC returns true if the transport includes gRPC configuration
func (t Transport) hasGRPC() bool {
	return t.GRPC != nil
}

// hasREST returns true if the transport includes REST configuration
func (t Transport) hasREST() bool {
	return t.REST != nil
}

// hasPubSub returns true if the transport includes PubSub configuration
func (t Transport) hasPubSub() bool {
	return t.PubSub != nil
}

// hasMCP returns true if the transport includes MCP configuration
func (t Transport) hasMCP() bool {
	return t.MCP != nil
}

// String returns a string representation of the transport configuration
func (t Transport) String() string {
	var transports []string
	if t.GRPC != nil {
		method := t.GRPC.Method
		if method == "" {
			method = "<default>"
		}
		transports = append(transports, fmt.Sprintf("GRPC(method=%s)", method))
	}
	if t.REST != nil {
		method := t.REST.Method
		if method == "" {
			method = "<default>"
		}
		path := t.REST.Path
		if path == "" {
			path = "<default>"
		}
		transports = append(transports, fmt.Sprintf("REST(%s %s)", method, path))
	}
	if t.PubSub != nil {
		topic := t.PubSub.Topic
		if topic == "" {
			topic = "<default>"
		}
		transports = append(transports, fmt.Sprintf("PubSub(topic=%s)", topic))
	}
	if t.MCP != nil {
		desc := t.MCP.Description
		if desc == "" {
			desc = "<no description>"
		}
		transports = append(transports, fmt.Sprintf("MCP(desc=%s)", desc))
	}
	return fmt.Sprintf("Transport[%v]", transports)
}
