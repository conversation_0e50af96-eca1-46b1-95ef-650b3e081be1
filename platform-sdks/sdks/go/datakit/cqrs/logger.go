package cqrs

import (
	"log/slog"
	"os"
	"strings"
)

// LogLevel represents the logging level
type LogLevel string

const (
	LogLevelDebug LogLevel = "DEBUG"
	LogLevelInfo  LogLevel = "INFO"
	LogLevelWarn  LogLevel = "WARN"
	LogLevelError LogLevel = "ERROR"
)

// LogFormat represents the log output format
type LogFormat string

const (
	LogFormatJSON LogFormat = "JSON"
	LogFormatText LogFormat = "TEXT"
)

// LogConfig defines the logging configuration for the CQRS engine
type LogConfig struct {
	Level  LogLevel  // Minimum log level to output
	Format LogFormat // Output format (JSON or TEXT)
}

// DefaultLogConfig returns a sensible default logging configuration
func DefaultLogConfig() *LogConfig {
	return &LogConfig{
		Level:  LogLevelInfo,
		Format: LogFormatText,
	}
}

// InitializeLogging configures the global slog logger for the CQRS package
func InitializeLogging(config *LogConfig) {
	if config == nil {
		config = DefaultLogConfig()
	}

	// Apply environment variable override
	if envLevel := getLogLevelFromEnv(); envLevel != "" {
		config.Level = envLevel
	}

	// Set up slog options
	opts := &slog.HandlerOptions{
		Level: convertLogLevel(config.Level),
	}

	// Create handler based on format
	var handler slog.Handler
	switch config.Format {
	case LogFormatJSON:
		handler = slog.NewJSONHandler(os.Stdout, opts)
	default:
		handler = slog.NewTextHandler(os.Stdout, opts)
	}

	// Set as default logger
	slog.SetDefault(slog.New(handler))
}

// getLogLevelFromEnv reads the log level from environment variable
func getLogLevelFromEnv() LogLevel {
	level := os.Getenv("CQRS_LOG_LEVEL")
	if level == "" {
		return ""
	}

	switch strings.ToUpper(level) {
	case "DEBUG":
		return LogLevelDebug
	case "INFO":
		return LogLevelInfo
	case "WARN", "WARNING":
		return LogLevelWarn
	case "ERROR":
		return LogLevelError
	default:
		return LogLevelInfo
	}
}

// convertLogLevel converts our LogLevel to slog.Level
func convertLogLevel(level LogLevel) slog.Level {
	switch level {
	case LogLevelDebug:
		return slog.LevelDebug
	case LogLevelInfo:
		return slog.LevelInfo
	case LogLevelWarn:
		return slog.LevelWarn
	case LogLevelError:
		return slog.LevelError
	default:
		return slog.LevelInfo
	}
}

// init automatically initializes logging with default configuration
func init() {
	InitializeLogging(nil)
}
