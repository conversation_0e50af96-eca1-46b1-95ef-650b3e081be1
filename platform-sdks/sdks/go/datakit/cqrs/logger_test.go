package cqrs

import (
	"bytes"
	"log/slog"
	"os"
	"strings"
	"testing"
)

func TestLoggerConfiguration(t *testing.T) {
	// Save original environment
	originalLevel := os.Getenv("CQRS_LOG_LEVEL")
	defer os.Setenv("CQRS_LOG_LEVEL", originalLevel)

	tests := []struct {
		name       string
		config     *LogConfig
		envLevel   string
		wantLevel  LogLevel
		wantFormat LogFormat
	}{
		{
			name:       "default config",
			config:     nil,
			wantLevel:  LogLevelInfo,
			wantFormat: LogFormatText,
		},
		{
			name: "custom config",
			config: &LogConfig{
				Level:  LogLevelDebug,
				Format: LogFormatJSON,
			},
			wantLevel:  LogLevelDebug,
			wantFormat: LogFormatJSON,
		},
		{
			name:       "env override",
			config:     nil,
			envLevel:   "ERROR",
			wantLevel:  LogLevelError,
			wantFormat: LogFormatText,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.envLevel != "" {
				os.Setenv("CQRS_LOG_LEVEL", tt.envLevel)
			} else {
				os.Unsetenv("CQRS_LOG_LEVEL")
			}

			// Test config creation
			if tt.config == nil {
				tt.config = DefaultLogConfig()
			}

			// Test environment variable reading
			envLevel := getLogLevelFromEnv()
			if tt.envLevel != "" {
				if envLevel != tt.wantLevel {
					t.Errorf("getLogLevelFromEnv() = %v, want %v", envLevel, tt.wantLevel)
				}
			}

			// Test log level conversion
			slogLevel := convertLogLevel(tt.wantLevel)
			expectedSlogLevel := slog.LevelInfo
			switch tt.wantLevel {
			case LogLevelDebug:
				expectedSlogLevel = slog.LevelDebug
			case LogLevelInfo:
				expectedSlogLevel = slog.LevelInfo
			case LogLevelWarn:
				expectedSlogLevel = slog.LevelWarn
			case LogLevelError:
				expectedSlogLevel = slog.LevelError
			}

			if slogLevel != expectedSlogLevel {
				t.Errorf("convertLogLevel(%v) = %v, want %v", tt.wantLevel, slogLevel, expectedSlogLevel)
			}
		})
	}
}

func TestEngineLogging(t *testing.T) {
	// Test basic engine functionality with logging config
	config := &LogConfig{
		Level:  LogLevelDebug,
		Format: LogFormatJSON,
	}

	// Create engine with logging
	engine := NewEngine("test-app", WithLogging(config))

	// Test basic engine functionality
	if engine == nil {
		t.Error("Expected engine to be created")
	}

	// Test that InitializeLogging works without panicking
	InitializeLogging(config)

	// Test log level conversion
	level := convertLogLevel(LogLevelDebug)
	if level != slog.LevelDebug {
		t.Errorf("Expected debug level, got %v", level)
	}
}

func TestLogLevelFiltering(t *testing.T) {
	// Test that log levels are properly filtered
	var buf bytes.Buffer

	// Set to WARN level
	config := &LogConfig{
		Level:  LogLevelWarn,
		Format: LogFormatJSON,
	}

	opts := &slog.HandlerOptions{
		Level: convertLogLevel(config.Level),
	}
	handler := slog.NewJSONHandler(&buf, opts)
	logger := slog.New(handler)

	// Test various log levels
	logger.Debug("debug message") // Should not appear
	logger.Info("info message")   // Should not appear
	logger.Warn("warn message")   // Should appear
	logger.Error("error message") // Should appear

	logOutput := buf.String()

	// Debug and Info should not appear
	if strings.Contains(logOutput, "debug message") {
		t.Error("Debug message should not appear at WARN level")
	}
	if strings.Contains(logOutput, "info message") {
		t.Error("Info message should not appear at WARN level")
	}

	// Warn and Error should appear
	if !strings.Contains(logOutput, "warn message") {
		t.Error("Warn message should appear at WARN level")
	}
	if !strings.Contains(logOutput, "error message") {
		t.Error("Error message should appear at WARN level")
	}
}

// Test types are already defined in other test files
 