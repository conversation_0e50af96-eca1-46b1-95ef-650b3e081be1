package cqrs

// Option represents a configuration option for the Engine
type Option func(*Engine)

// WithGRPC configures the engine to handle gRPC service invocations
func WithGRPC(port int) Option {
	return func(e *Engine) {
		e.grpcPort = port
	}
}

// WithREST configures the engine to handle REST API endpoints
func WithREST(port int) Option {
	return func(e *Engine) {
		e.restPort = port
	}
}

// WithPubSub configures the engine to handle PubSub events via gRPC
func WithPubSub(pubsub string, port int) Option {
	return func(e *Engine) {
		e.pubsubName = pubsub
		e.subPort = port
	}
}

// WithVersion sets the service version
func WithVersion(version string) Option {
	return func(e *Engine) {
		e.version = version
	}
}

// MCPConfig defines configuration for Model Context Protocol support
type MCPConfig struct {
	// Path is the endpoint where the JSON-RPC handler will be mounted (required)
	Path string

	// EnableWS enables WebSocket support at <Path>/ws (optional)
	EnableWS bool
}

// WithMCP configures the engine to handle MCP (Model Context Protocol) requests
func WithMCP(cfg *MCPConfig) Option {
	return func(e *Engine) {
		// Apply defaults
		if cfg.Path == "" {
			cfg.Path = "/mcp"
		}
		e.mcpCfg = cfg
	}
}

// WithLogging configures the global slog logger for the CQRS package
func WithLogging(cfg *LogConfig) Option {
	return func(e *Engine) {
		InitializeLogging(cfg)
	}
}

// WithLogLevel configures the global logger with a specific log level (convenience method)
func WithLogLevel(level LogLevel) Option {
	return func(e *Engine) {
		config := DefaultLogConfig()
		config.Level = level
		InitializeLogging(config)
	}
}
