package cqrs

import (
	"strings"
	"testing"
)

// Test DTOs for transport testing
type CreateUserCommand struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type GetUserQuery struct {
	ID string `json:"id"`
}

func TestValidateTransport(t *testing.T) {
	tests := []struct {
		name        string
		transport   Transport
		obj         any
		isQuery     bool
		expectError bool
		errorMsg    string
	}{
		{
			name:        "empty transport should fail",
			transport:   Transport{},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: true,
			errorMsg:    "transport must contain at least one non-nil sub-block",
		},
		{
			name: "valid gRPC transport with explicit method",
			transport: Transport{
				GRPC: &GRPC{Method: "TestMethod"},
			},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: false,
		},
		{
			name: "gRPC transport with empty method should use default",
			transport: Transport{
				GRPC: &GRPC{Method: ""},
			},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: false,
		},
		{
			name: "valid REST transport with explicit values",
			transport: Transport{
				REST: &REST{Method: "GET", Path: "/api/test"},
			},
			obj:         &GetUserQuery{},
			isQuery:     true,
			expectError: false,
		},
		{
			name: "REST transport with empty method should use default (command)",
			transport: Transport{
				REST: &REST{Method: "", Path: "/api/test"},
			},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: false,
		},
		{
			name: "REST transport with empty method should use default (query)",
			transport: Transport{
				REST: &REST{Method: "", Path: "/api/test"},
			},
			obj:         &GetUserQuery{},
			isQuery:     true,
			expectError: false,
		},
		{
			name: "REST transport with empty path should use default",
			transport: Transport{
				REST: &REST{Method: "GET", Path: ""},
			},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: false,
		},
		{
			name: "valid PubSub transport with explicit topic",
			transport: Transport{
				PubSub: &PubSub{Topic: "test-topic"},
			},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: false,
		},
		{
			name: "PubSub transport with empty topic should use default",
			transport: Transport{
				PubSub: &PubSub{Topic: ""},
			},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: false,
		},
		{
			name: "query with only PubSub should fail",
			transport: Transport{
				PubSub: &PubSub{Topic: "test-topic"},
			},
			obj:         &GetUserQuery{},
			isQuery:     true,
			expectError: true,
			errorMsg:    "queries must expose at least GRPC, REST, or MCP transport",
		},
		{
			name: "query with gRPC should pass",
			transport: Transport{
				GRPC: &GRPC{Method: "TestQuery"},
			},
			obj:         &GetUserQuery{},
			isQuery:     true,
			expectError: false,
		},
		{
			name: "query with REST should pass",
			transport: Transport{
				REST: &REST{Method: "GET", Path: "/api/query"},
			},
			obj:         &GetUserQuery{},
			isQuery:     true,
			expectError: false,
		},
		{
			name: "multiple transports should pass",
			transport: Transport{
				GRPC:   &GRPC{Method: "TestMethod"},
				REST:   &REST{Method: "POST", Path: "/api/test"},
				PubSub: &PubSub{Topic: "test-topic"},
			},
			obj:         &CreateUserCommand{},
			isQuery:     false,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateTransportWithDefaults(tt.transport, tt.obj, tt.isQuery)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got '%s'", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestTransportDefaults(t *testing.T) {
	tests := []struct {
		name                string
		transport           Transport
		obj                 any
		isQuery             bool
		expectedGRPCMethod  string
		expectedRESTMethod  string
		expectedRESTPath    string
		expectedPubSubTopic string
	}{
		{
			name: "CreateUserCommand defaults",
			transport: Transport{
				GRPC:   &GRPC{},
				REST:   &REST{},
				PubSub: &PubSub{},
			},
			obj:                 &CreateUserCommand{},
			isQuery:             false,
			expectedGRPCMethod:  "CreateUser",
			expectedRESTMethod:  "POST",
			expectedRESTPath:    "/create-user",
			expectedPubSubTopic: "create-user",
		},
		{
			name: "GetUserQuery defaults",
			transport: Transport{
				GRPC: &GRPC{},
				REST: &REST{},
			},
			obj:                &GetUserQuery{},
			isQuery:            true,
			expectedGRPCMethod: "GetUser",
			expectedRESTMethod: "GET",
			expectedRESTPath:   "/get-user",
		},
		{
			name: "Partial defaults - only REST path",
			transport: Transport{
				REST: &REST{Method: "PUT"},
			},
			obj:              &CreateUserCommand{},
			isQuery:          false,
			expectedRESTPath: "/create-user",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a copy to test defaults application
			transportCopy := tt.transport
			applyTransportDefaults(&transportCopy, tt.obj, tt.isQuery)

			if transportCopy.GRPC != nil && tt.expectedGRPCMethod != "" {
				if transportCopy.GRPC.Method != tt.expectedGRPCMethod {
					t.Errorf("expected gRPC method '%s', got '%s'", tt.expectedGRPCMethod, transportCopy.GRPC.Method)
				}
			}

			if transportCopy.REST != nil {
				if tt.expectedRESTMethod != "" && transportCopy.REST.Method != tt.expectedRESTMethod {
					t.Errorf("expected REST method '%s', got '%s'", tt.expectedRESTMethod, transportCopy.REST.Method)
				}
				if tt.expectedRESTPath != "" && transportCopy.REST.Path != tt.expectedRESTPath {
					t.Errorf("expected REST path '%s', got '%s'", tt.expectedRESTPath, transportCopy.REST.Path)
				}
			}

			if transportCopy.PubSub != nil && tt.expectedPubSubTopic != "" {
				if transportCopy.PubSub.Topic != tt.expectedPubSubTopic {
					t.Errorf("expected PubSub topic '%s', got '%s'", tt.expectedPubSubTopic, transportCopy.PubSub.Topic)
				}
			}
		})
	}
}

func TestNamingUtilities(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected string
	}{
		{
			name:     "CreateUserCommand -> CreateUser",
			input:    &CreateUserCommand{},
			expected: "CreateUser",
		},
		{
			name:     "GetUserQuery -> GetUser",
			input:    &GetUserQuery{},
			expected: "GetUser",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			typeName := extractTypeName(tt.input)
			baseName := removeCommandQuerySuffix(typeName)
			if baseName != tt.expected {
				t.Errorf("expected '%s', got '%s'", tt.expected, baseName)
			}
		})
	}
}

func TestKebabCase(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"CreateUser", "create-user"},
		{"GetUser", "get-user"},
		{"UpdateUserProfile", "update-user-profile"},
		{"Test", "test"},
		{"HTTPRequest", "httprequest"},
		{"XMLParser", "xmlparser"},
	}

	for _, tt := range tests {
		t.Run(tt.input, func(t *testing.T) {
			result := toKebabCase(tt.input)
			if result != tt.expected {
				t.Errorf("expected '%s', got '%s'", tt.expected, result)
			}
		})
	}
}

func TestTransportHelperMethods(t *testing.T) {
	transport := Transport{
		GRPC:   &GRPC{Method: "TestMethod"},
		REST:   &REST{Method: "GET", Path: "/api/test"},
		PubSub: &PubSub{Topic: "test-topic"},
	}

	if !transport.hasGRPC() {
		t.Error("expected hasGRPC() to return true")
	}

	if !transport.hasREST() {
		t.Error("expected hasREST() to return true")
	}

	if !transport.hasPubSub() {
		t.Error("expected hasPubSub() to return true")
	}

	emptyTransport := Transport{}

	if emptyTransport.hasGRPC() {
		t.Error("expected hasGRPC() to return false for empty transport")
	}

	if emptyTransport.hasREST() {
		t.Error("expected hasREST() to return false for empty transport")
	}

	if emptyTransport.hasPubSub() {
		t.Error("expected hasPubSub() to return false for empty transport")
	}
}

func TestTransportString(t *testing.T) {
	tests := []struct {
		name      string
		transport Transport
		expected  string
	}{
		{
			name:      "empty transport",
			transport: Transport{},
			expected:  "Transport[[]]",
		},
		{
			name: "gRPC only",
			transport: Transport{
				GRPC: &GRPC{Method: "TestMethod"},
			},
			expected: "Transport[[GRPC(method=TestMethod)]]",
		},
		{
			name: "gRPC with empty method",
			transport: Transport{
				GRPC: &GRPC{Method: ""},
			},
			expected: "Transport[[GRPC(method=<default>)]]",
		},
		{
			name: "REST only",
			transport: Transport{
				REST: &REST{Method: "GET", Path: "/api/test"},
			},
			expected: "Transport[[REST(GET /api/test)]]",
		},
		{
			name: "REST with empty values",
			transport: Transport{
				REST: &REST{Method: "", Path: ""},
			},
			expected: "Transport[[REST(<default> <default>)]]",
		},
		{
			name: "PubSub only",
			transport: Transport{
				PubSub: &PubSub{Topic: "test-topic"},
			},
			expected: "Transport[[PubSub(topic=test-topic)]]",
		},
		{
			name: "PubSub with empty topic",
			transport: Transport{
				PubSub: &PubSub{Topic: ""},
			},
			expected: "Transport[[PubSub(topic=<default>)]]",
		},
		{
			name: "all transports",
			transport: Transport{
				GRPC:   &GRPC{Method: "TestMethod"},
				REST:   &REST{Method: "POST", Path: "/api/test"},
				PubSub: &PubSub{Topic: "test-topic"},
			},
			expected: "Transport[[GRPC(method=TestMethod) REST(POST /api/test) PubSub(topic=test-topic)]]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.transport.String()
			if result != tt.expected {
				t.Errorf("expected '%s', got '%s'", tt.expected, result)
			}
		})
	}
}
