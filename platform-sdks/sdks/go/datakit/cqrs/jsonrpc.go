package cqrs

import (
	"encoding/json"
	"reflect"
)

// ToolDef stores the data that will be returned in tools/list
type ToolDef struct {
	Name         string
	Title        string
	Description  string
	InputSchema  any
	OutputSchema any
	Annotations  map[string]any
	Handler      any          // Command or Query handler
	IsQ<PERSON>y      bool         // true for queries, false for commands
	DTOType      reflect.Type // Type of the DTO for creating instances
}

// jsonrpcRequest represents a JSON-RPC 2.0 request
type jsonrpcRequest struct {
	JSONRPC string          `json:"jsonrpc"`
	ID      json.RawMessage `json:"id"`
	Method  string          `json:"method"`
	Params  json.RawMessage `json:"params,omitempty"`
}

// jsonrpcResponse represents a JSON-RPC 2.0 response
type jsonrpcResponse struct {
	JSONRPC string          `json:"jsonrpc"`
	ID      json.RawMessage `json:"id"`
	Result  any             `json:"result,omitempty"`
	Error   *jsonrpcError   `json:"error,omitempty"`
}

// jsonrpcError represents a JSON-RPC 2.0 error
type jsonrpcError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data,omitempty"`
}

// JSON-RPC error codes
const (
	errParseError     = -32700 // Parse error
	errInvalidRequest = -32600 // Invalid Request
	errMethodNotFound = -32601 // Method not found
	errInvalidParams  = -32602 // Invalid params
	errInternalError  = -32603 // Internal error
)
