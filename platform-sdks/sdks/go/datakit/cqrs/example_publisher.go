package cqrs

import (
	"context"
	"fmt"
	"log/slog"
	"time"
)

// Example domain entities
type User struct {
	ID        string    `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
}

// Example domain events that implement the Event interface
type UserCreatedEvent struct {
	UserID    string    `json:"user_id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
}

// Type returns the canonical event type name
func (e UserCreatedEvent) Type() string {
	return "user.created"
}

type UserUpdatedEvent struct {
	UserID    string    `json:"user_id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Type returns the canonical event type name
func (e UserUpdatedEvent) Type() string {
	return "user.updated"
}

// Example commands
type ExampleCreateUserCommand struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

type ExampleUpdateUserCommand struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
}

// Example service with publisher
type UserService struct {
	publisher *Publisher
}

// NewUserService creates a new user service with a publisher
func NewUserService(pubsubName string, daprGRPCPort int) (*UserService, error) {
	publisher, err := NewPublisher(pubsubName, daprGRPCPort)
	if err != nil {
		return nil, fmt.Errorf("failed to create publisher: %w", err)
	}

	return &UserService{
		publisher: publisher,
	}, nil
}

// Close closes the service and its publisher
func (s *UserService) Close() error {
	return s.publisher.Close()
}

// CreateUser handles user creation and publishes domain event
func (s *UserService) CreateUser(ctx context.Context, cmd *ExampleCreateUserCommand) error {
	// Business logic - create user
	user := &User{
		ID:        cmd.ID,
		Name:      cmd.Name,
		Email:     cmd.Email,
		CreatedAt: time.Now(),
	}

	// Save user to database (simulated)
	if err := s.saveUser(ctx, user); err != nil {
		return fmt.Errorf("failed to save user: %w", err)
	}

	// Publish domain event using typed event interface
	event := UserCreatedEvent{
		UserID:    user.ID,
		Name:      user.Name,
		Email:     user.Email,
		CreatedAt: user.CreatedAt,
	}

	additionalMetadata := map[string]string{
		"source":  "user-service",
		"version": "1.0",
	}

	if err := s.publisher.PublishTypedEvent(ctx, "users", event, additionalMetadata); err != nil {
		// Log error but don't fail the operation
		slog.Error("Failed to publish user created event", "error", err)
	}

	return nil
}

// UpdateUser handles user updates and publishes domain event
func (s *UserService) UpdateUser(ctx context.Context, cmd *ExampleUpdateUserCommand) error {
	// Business logic - update user
	user := &User{
		ID:        cmd.ID,
		Name:      cmd.Name,
		Email:     cmd.Email,
		CreatedAt: time.Now(), // In real app, you'd load existing user
	}

	// Update user in database (simulated)
	if err := s.updateUser(ctx, user); err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}

	// Publish domain event using typed event interface
	event := UserUpdatedEvent{
		UserID:    user.ID,
		Name:      user.Name,
		Email:     user.Email,
		UpdatedAt: time.Now(),
	}

	additionalMetadata := map[string]string{
		"source":  "user-service",
		"version": "1.0",
	}

	if err := s.publisher.PublishTypedEvent(ctx, "users", event, additionalMetadata); err != nil {
		// Log error but don't fail the operation
		slog.Error("Failed to publish user updated event", "error", err)
	}

	return nil
}

// Simulated database operations
func (s *UserService) saveUser(ctx context.Context, user *User) error {
	slog.Info("Saving user", "user", fmt.Sprintf("%+v", user))
	// Simulate saving to database
	return nil
}

func (s *UserService) updateUser(ctx context.Context, user *User) error {
	slog.Info("Updating user", "user", fmt.Sprintf("%+v", user))
	// Simulate updating in database
	return nil
}

// ExamplePublisherUsage demonstrates how to use the publisher
func ExamplePublisherUsage() {
	ctx := context.Background()

	// Create user service with publisher
	userService, err := NewUserService("test-pubsub", 3500)
	if err != nil {
		slog.Error("Failed to create user service", "error", err)
		return
	}
	defer userService.Close()

	// Create a user
	createCmd := &ExampleCreateUserCommand{
		ID:    "user-123",
		Name:  "John Doe",
		Email: "<EMAIL>",
	}

	if err := userService.CreateUser(ctx, createCmd); err != nil {
		slog.Error("Failed to create user", "error", err)
	}

	// Update the user
	updateCmd := &ExampleUpdateUserCommand{
		ID:    "user-123",
		Name:  "John Smith",
		Email: "<EMAIL>",
	}

	if err := userService.UpdateUser(ctx, updateCmd); err != nil {
		slog.Error("Failed to update user", "error", err)
	}
}

// ExampleStandalonePublisher shows how to use publisher independently
func ExampleStandalonePublisher() {
	ctx := context.Background()

	// Create standalone publisher
	publisher, err := NewPublisher("test-pubsub", 3500)
	if err != nil {
		slog.Error("Failed to create standalone publisher", "error", err)
		return
	}
	defer publisher.Close()

	// Publish various events
	events := []struct {
		topic string
		event interface{}
	}{
		{"users", UserCreatedEvent{UserID: "1", Name: "Alice", CreatedAt: time.Now()}},
		{"users", UserCreatedEvent{UserID: "2", Name: "Bob", CreatedAt: time.Now()}},
		{"users", UserUpdatedEvent{UserID: "1", Name: "Alice Smith", UpdatedAt: time.Now()}},
	}

	for _, e := range events {
		if err := publisher.PublishEvent(ctx, e.topic, e.event); err != nil {
			slog.Error("Failed to publish event to topic", "topic", e.topic, "error", err)
		} else {
			slog.Info("Successfully published event to topic", "topic", e.topic)
		}
	}
}
