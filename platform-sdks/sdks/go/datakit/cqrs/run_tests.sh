#!/bin/bash

# CQRS Integration Test Runner
# This script provides easy commands for running different types of tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Dapr is available
check_dapr() {
    if command -v dapr &> /dev/null; then
        log_info "Dapr CLI is available"
        dapr --version
        return 0
    else
        log_warning "Dapr CLI is not available"
        return 1
    fi
}

# Kill any processes that might be using our test ports
cleanup_ports() {
    log_info "Cleaning up test ports..."

    # Kill processes on common test ports
    for port in 8081 8082 8083 8084 8085 8086 8087 8088 8090 8091 8092 8093 8094 8095 9090 9091 9092 9093 9094 9095 9096 9097 9098 9099 9100; do
        if lsof -ti:$port >/dev/null 2>&1; then
            log_info "Killing process on port $port"
            lsof -ti:$port | xargs kill -9 2>/dev/null || true
        fi
    done

    # Kill any Dapr processes
    pkill -f "dapr run" 2>/dev/null || true
    pkill -f "daprd" 2>/dev/null || true

    sleep 1
}

# Run unit tests
run_unit_tests() {
    log_info "Running unit tests..."
    go test -v -short ./...
    log_success "Unit tests completed"
}

# Run standalone integration tests (no Dapr required)
run_standalone_tests() {
    log_info "Running standalone integration tests..."
    cleanup_ports
    go test -v -run "TestStandalone|TestMultiTransport|TestValidation|TestGraceful|TestError|TestLifecycle" ./...
    log_success "Standalone integration tests completed"
}

# Run Dapr integration tests
run_dapr_tests() {
    if ! check_dapr; then
        log_error "Dapr CLI is required for Dapr integration tests"
        log_info "Install Dapr CLI: https://docs.dapr.io/getting-started/install-dapr-cli/"
        exit 1
    fi

    log_info "Running Dapr integration tests..."
    cleanup_ports
    go test -v -timeout 5m -run "TestDapr" ./...
    log_success "Dapr integration tests completed"
}

# Run benchmark tests
run_benchmarks() {
    log_info "Running benchmark tests..."
    cleanup_ports
    go test -v -bench=. -benchmem ./...
    log_success "Benchmark tests completed"
}

# Run all tests
run_all_tests() {
    log_info "Running all tests..."

    # Unit tests first
    run_unit_tests

    # Standalone integration tests
    run_standalone_tests

    # Dapr tests if available
    if check_dapr; then
        run_dapr_tests
    else
        log_warning "Skipping Dapr tests - Dapr CLI not available"
    fi

    # Benchmarks
    run_benchmarks

    log_success "All tests completed successfully!"
}

# Show test coverage
run_coverage() {
    log_info "Running tests with coverage..."
    cleanup_ports
    go test -v -coverprofile=coverage.out ./...
    go tool cover -html=coverage.out -o coverage.html
    log_success "Coverage report generated: coverage.html"
}

# Show help
show_help() {
    echo "CQRS Integration Test Runner"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  unit          Run unit tests only"
    echo "  standalone    Run standalone integration tests (no Dapr required)"
    echo "  dapr          Run Dapr integration tests (requires Dapr CLI)"
    echo "  bench         Run benchmark tests"
    echo "  coverage      Run tests with coverage report"
    echo "  all           Run all available tests"
    echo "  cleanup       Clean up test ports and processes"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 unit                    # Quick unit tests"
    echo "  $0 standalone              # Integration tests without Dapr"
    echo "  $0 dapr                    # Full Dapr integration tests"
    echo "  $0 all                     # Run everything"
    echo ""
    echo "Prerequisites:"
    echo "  - Go 1.19+ installed"
    echo "  - For Dapr tests: Dapr CLI installed"
    echo ""
    echo "Test Categories:"
    echo "  Unit Tests:        Fast validation of library logic"
    echo "  Standalone Tests:  REST/gRPC without Dapr dependencies"
    echo "  Dapr Tests:        Full Dapr service invocation and PubSub"
    echo "  Benchmarks:        Performance testing"
}

# Main script logic
case "${1:-help}" in
    "unit")
        run_unit_tests
        ;;
    "standalone")
        run_standalone_tests
        ;;
    "dapr")
        run_dapr_tests
        ;;
    "bench")
        run_benchmarks
        ;;
    "coverage")
        run_coverage
        ;;
    "all")
        run_all_tests
        ;;
    "cleanup")
        cleanup_ports
        log_success "Cleanup completed"
        ;;
    "help"|*)
        show_help
        ;;
esac
