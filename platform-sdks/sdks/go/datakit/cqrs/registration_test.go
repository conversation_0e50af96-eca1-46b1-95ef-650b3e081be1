package cqrs

import (
	"context"
	"strings"
	"testing"
)

// Test DTOs
type TestCommand struct {
	Name string `json:"name"`
}

type TestQuery struct {
	ID string `json:"id"`
}

// Test handlers
func testCommandHandler(ctx context.Context, cmd any) error {
	return nil
}

func testQueryHandler(ctx context.Context, q any) (any, error) {
	return map[string]string{"result": "ok"}, nil
}

func TestRegisterCommands(t *testing.T) {
	tests := []struct {
		name        string
		engine      *Engine
		config      CommandConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid command registration",
			engine: &Engine{
				grpcPort: 9090,
			},
			config: CommandConfig{
				Command: &TestCommand{},
				Handler: testCommandHandler,
				Transport: Transport{
					GRPC: &GRPC{Method: "TestCommand"},
				},
			},
			expectError: false,
		},
		{
			name:   "nil command should fail",
			engine: &Engine{},
			config: CommandConfig{
				Command: nil,
				Handler: testCommandHandler,
				Transport: Transport{
					GRPC: &GRPC{Method: "TestCommand"},
				},
			},
			expectError: true,
			errorMsg:    "command cannot be nil",
		},
		{
			name:   "nil handler should fail",
			engine: &Engine{},
			config: CommandConfig{
				Command: &TestCommand{},
				Handler: nil,
				Transport: Transport{
					GRPC: &GRPC{Method: "TestCommand"},
				},
			},
			expectError: true,
			errorMsg:    "handler cannot be nil",
		},
		{
			name:   "invalid transport should fail",
			engine: &Engine{},
			config: CommandConfig{
				Command:   &TestCommand{},
				Handler:   testCommandHandler,
				Transport: Transport{}, // empty transport
			},
			expectError: true,
			errorMsg:    "invalid transport configuration",
		},
		{
			name:   "missing required service should fail",
			engine: &Engine{}, // no gRPC port configured
			config: CommandConfig{
				Command: &TestCommand{},
				Handler: testCommandHandler,
				Transport: Transport{
					GRPC: &GRPC{Method: "TestCommand"},
				},
			},
			expectError: true,
			errorMsg:    "missing required service configuration",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.engine.RegisterCommands(tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got '%s'", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got: %v", err)
				}
				// Verify command was added
				if len(tt.engine.commands) != 1 {
					t.Errorf("expected 1 command to be registered, got %d", len(tt.engine.commands))
				}
			}
		})
	}
}

func TestRegisterQueries(t *testing.T) {
	tests := []struct {
		name        string
		engine      *Engine
		config      QueryConfig
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid query registration",
			engine: &Engine{
				grpcPort: 9090,
			},
			config: QueryConfig{
				Query:   &TestQuery{},
				Handler: testQueryHandler,
				Transport: Transport{
					GRPC: &GRPC{Method: "TestQuery"},
				},
			},
			expectError: false,
		},
		{
			name:   "nil query should fail",
			engine: &Engine{},
			config: QueryConfig{
				Query:   nil,
				Handler: testQueryHandler,
				Transport: Transport{
					GRPC: &GRPC{Method: "TestQuery"},
				},
			},
			expectError: true,
			errorMsg:    "query cannot be nil",
		},
		{
			name:   "nil handler should fail",
			engine: &Engine{},
			config: QueryConfig{
				Query:   &TestQuery{},
				Handler: nil,
				Transport: Transport{
					GRPC: &GRPC{Method: "TestQuery"},
				},
			},
			expectError: true,
			errorMsg:    "handler cannot be nil",
		},
		{
			name:   "query with only PubSub should fail",
			engine: &Engine{},
			config: QueryConfig{
				Query:   &TestQuery{},
				Handler: testQueryHandler,
				Transport: Transport{
					PubSub: &PubSub{Topic: "test-topic"},
				},
			},
			expectError: true,
			errorMsg:    "invalid transport configuration",
		},
		{
			name: "query with REST should pass",
			engine: &Engine{
				restPort: 8080,
			},
			config: QueryConfig{
				Query:   &TestQuery{},
				Handler: testQueryHandler,
				Transport: Transport{
					REST: &REST{Method: "GET", Path: "/api/test"},
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.engine.RegisterQueries(tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got '%s'", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got: %v", err)
				}
				// Verify query was added
				if len(tt.engine.queries) != 1 {
					t.Errorf("expected 1 query to be registered, got %d", len(tt.engine.queries))
				}
			}
		})
	}
}

func TestValidateRequiredServices(t *testing.T) {
	tests := []struct {
		name        string
		engine      *Engine
		transport   Transport
		expectError bool
		errorMsg    string
	}{
		{
			name: "gRPC transport with configured engine",
			engine: &Engine{
				grpcPort: 9090,
			},
			transport: Transport{
				GRPC: &GRPC{Method: "TestMethod"},
			},
			expectError: false,
		},
		{
			name:   "gRPC transport without configured engine",
			engine: &Engine{},
			transport: Transport{
				GRPC: &GRPC{Method: "TestMethod"},
			},
			expectError: true,
			errorMsg:    "gRPC transport specified but engine not configured with WithGRPC",
		},
		{
			name: "REST transport with configured engine",
			engine: &Engine{
				restPort: 8080,
			},
			transport: Transport{
				REST: &REST{Method: "GET", Path: "/api/test"},
			},
			expectError: false,
		},
		{
			name:   "REST transport without configured engine",
			engine: &Engine{},
			transport: Transport{
				REST: &REST{Method: "GET", Path: "/api/test"},
			},
			expectError: true,
			errorMsg:    "REST transport specified but engine not configured with WithREST",
		},
		{
			name: "PubSub transport with configured engine",
			engine: &Engine{
				subPort:    9081,
				pubsubName: "test-pubsub",
			},
			transport: Transport{
				PubSub: &PubSub{Topic: "test-topic"},
			},
			expectError: false,
		},
		{
			name:   "PubSub transport without configured engine",
			engine: &Engine{},
			transport: Transport{
				PubSub: &PubSub{Topic: "test-topic"},
			},
			expectError: true,
			errorMsg:    "PubSub transport specified but engine not configured with WithGRPCSubscriber",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.engine.validateRequiredServices(tt.transport)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					t.Errorf("expected error message '%s', got '%s'", tt.errorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestEngineOptions(t *testing.T) {
	engine := NewEngine("test-service",
		WithGRPC(9090),
		WithREST(8080),
		WithPubSub("test-pubsub", 9081),
	)

	if engine.appID != "test-service" {
		t.Errorf("expected appID 'test-service', got '%s'", engine.appID)
	}

	if engine.grpcPort != 9090 {
		t.Errorf("expected grpcPort 9090, got %d", engine.grpcPort)
	}

	if engine.restPort != 8080 {
		t.Errorf("expected restPort 8080, got %d", engine.restPort)
	}

	if engine.subPort != 9081 {
		t.Errorf("expected subPort 9081, got %d", engine.subPort)
	}

	if engine.pubsubName != "test-pubsub" {
		t.Errorf("expected pubsubName 'test-pubsub', got '%s'", engine.pubsubName)
	}
}

func TestGetRegisteredCommandsAndQueries(t *testing.T) {
	engine := NewEngine("test-service", WithGRPC(9090))

	// Register a command
	cmdConfig := CommandConfig{
		Command: &TestCommand{},
		Handler: testCommandHandler,
		Transport: Transport{
			GRPC: &GRPC{Method: "TestCommand"},
		},
	}
	err := engine.RegisterCommands(cmdConfig)
	if err != nil {
		t.Fatalf("failed to register command: %v", err)
	}

	// Register a query
	queryConfig := QueryConfig{
		Query:   &TestQuery{},
		Handler: testQueryHandler,
		Transport: Transport{
			GRPC: &GRPC{Method: "TestQuery"},
		},
	}
	err = engine.RegisterQueries(queryConfig)
	if err != nil {
		t.Fatalf("failed to register query: %v", err)
	}

	// Test GetRegisteredCommands
	commands := engine.GetRegisteredCommands()
	if len(commands) != 1 {
		t.Errorf("expected 1 registered command, got %d", len(commands))
	}

	// Test GetRegisteredQueries
	queries := engine.GetRegisteredQueries()
	if len(queries) != 1 {
		t.Errorf("expected 1 registered query, got %d", len(queries))
	}

	// Verify that modifying returned slices doesn't affect the engine
	commands[0].Handler = nil
	if engine.commands[0].Handler == nil {
		t.Error("modifying returned commands slice affected the engine's internal state")
	}
}

func TestEngineSummary(t *testing.T) {
	engine := NewEngine("test-service",
		WithGRPC(9090),
		WithREST(8080),
		WithPubSub("test-pubsub", 9081),
	)

	summary := engine.Summary()

	// Just check that summary is not empty and contains key information
	if len(summary) == 0 || summary == "" {
		t.Errorf("summary is empty")
	}

	if len(summary) < 50 {
		t.Errorf("summary seems too short: %s", summary)
	}
}
