package cqrs_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os/exec"
	"strings"
	"testing"
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
)

// Test DTOs
type TestCommand struct {
	ID   string `json:"id" binding:"required"`
	Name string `json:"name" binding:"required"`
}

type TestQuery struct {
	ID string `json:"id" uri:"id" form:"id" binding:"required"`
}

type TestResponse struct {
	ID     string `json:"id"`
	Name   string `json:"name"`
	Status string `json:"status"`
}

// Test handlers
func testCommandHandler(ctx context.Context, cmd any) error {
	c := cmd.(*TestCommand)
	fmt.Printf("Command executed: ID=%s, Name=%s\n", c.ID, c.Name)
	return nil
}

func testQueryHandler(ctx context.Context, q any) (any, error) {
	query := q.(*TestQuery)
	return &TestResponse{
		ID:     query.ID,
		Name:   "Test User",
		Status: "active",
	}, nil
}

// Helper functions
func waitForService(url string, timeout time.Duration) error {
	client := &http.Client{Timeout: 5 * time.Second}
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		resp, err := client.Get(url)
		if err == nil {
			resp.Body.Close()
			return nil
		}
		time.Sleep(100 * time.Millisecond)
	}
	return fmt.Errorf("service not ready after %v", timeout)
}

func isDaprAvailable() bool {
	cmd := exec.Command("dapr", "--version")
	return cmd.Run() == nil
}

func TestStandaloneMode(t *testing.T) {

	if testing.Short() {
		t.Skip("Skipping standalone mode test in short mode")
	}

	t.Log("Testing CQRS in standalone mode (REST only)")

	// Create engine for standalone mode (REST only)
	engine := cqrs.NewEngine("test-service-standalone",
		cqrs.WithREST(8081), // Use different port to avoid conflicts
	)

	// Register a command
	err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &TestCommand{},
		Handler: testCommandHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "POST", Path: "/api/v1/commands/test"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register command: %v", err)
	}

	// Register a query
	err = engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &TestQuery{},
		Handler: testQueryHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/api/v1/queries/test/:id"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register query: %v", err)
	}

	// Start engine in background
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	engineErr := make(chan error, 1)
	go func() {
		if err := engine.Start(ctx); err != nil && err != context.Canceled {
			engineErr <- err
		}
	}()

	// Wait for service to be ready
	if err := waitForService("http://localhost:8081/api/v1/queries/test/123?id=123", 10*time.Second); err != nil {
		t.Fatalf("Service not ready: %v", err)
	}

	t.Run("REST_Command", func(t *testing.T) {
		// Test REST command
		cmdData := TestCommand{ID: "test-123", Name: "Test Command"}
		jsonData, _ := json.Marshal(cmdData)

		resp, err := http.Post("http://localhost:8081/api/v1/commands/test", "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			t.Fatalf("Failed to send command: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		var result map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			t.Errorf("Failed to decode response: %v", err)
		}

		if result["status"] != "ok" {
			t.Errorf("Expected status 'ok', got %v", result["status"])
		}
	})

	t.Run("REST_Query", func(t *testing.T) {
		// Test REST query
		resp, err := http.Get("http://localhost:8081/api/v1/queries/test/test-456?id=test-456")
		if err != nil {
			t.Fatalf("Failed to send query: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		var result TestResponse
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			t.Errorf("Failed to decode response: %v", err)
		}

		if result.ID != "test-456" {
			t.Errorf("Expected ID 'test-456', got %s", result.ID)
		}
		if result.Name != "Test User" {
			t.Errorf("Expected Name 'Test User', got %s", result.Name)
		}
		if result.Status != "active" {
			t.Errorf("Expected Status 'active', got %s", result.Status)
		}
	})

	// Check for engine errors
	select {
	case err := <-engineErr:
		t.Fatalf("Engine error: %v", err)
	default:
		// No error, continue
	}
}

func TestMultiTransportMode(t *testing.T) {
	t.Log("Testing CQRS with multiple transports (gRPC + REST)")

	// Create engine with multiple transports
	engine := cqrs.NewEngine("test-service-multi",
		cqrs.WithGRPC(9091), // Use different ports
		cqrs.WithREST(8082),
		cqrs.WithPubSub("test-pubsub", 9092),
	)

	// Register command with multiple transports
	err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &TestCommand{},
		Handler: testCommandHandler,
		Transport: cqrs.Transport{
			GRPC: &cqrs.GRPC{Method: "TestCommand"},
			REST: &cqrs.REST{Method: "POST", Path: "/api/v1/multi/command"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register multi-transport command: %v", err)
	}

	// Register query with REST only
	err = engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &TestQuery{},
		Handler: testQueryHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/api/v1/multi/query/:id"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register query: %v", err)
	}

	// Start engine
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	engineErr := make(chan error, 1)
	go func() {
		if err := engine.Start(ctx); err != nil && err != context.Canceled {
			engineErr <- err
		}
	}()

	// Wait for service to be ready
	if err := waitForService("http://localhost:8082/api/v1/multi/query/123?id=123", 10*time.Second); err != nil {
		t.Fatalf("Service not ready: %v", err)
	}

	t.Run("Multi_Transport_REST", func(t *testing.T) {
		// Test REST endpoint of multi-transport command
		cmdData := TestCommand{ID: "multi-123", Name: "Multi Transport Command"}
		jsonData, _ := json.Marshal(cmdData)

		resp, err := http.Post("http://localhost:8082/api/v1/multi/command", "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			t.Fatalf("Failed to send multi-transport command: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
	})

	t.Run("Multi_Transport_Query", func(t *testing.T) {
		// Test query endpoint
		resp, err := http.Get("http://localhost:8082/api/v1/multi/query/multi-789?id=multi-789")
		if err != nil {
			t.Fatalf("Failed to send query: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		var result TestResponse
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			t.Errorf("Failed to decode response: %v", err)
		}

		if result.ID != "multi-789" {
			t.Errorf("Expected ID 'multi-789', got %s", result.ID)
		}
	})
}

func TestDaprIntegration(t *testing.T) {
	if !isDaprAvailable() {
		t.Skip("Dapr CLI not available, skipping Dapr integration tests")
	}

	if testing.Short() {
		t.Skip("Skipping Dapr integration test in short mode")
	}

	t.Log("Testing CQRS with Dapr integration")

	// Create engine with Dapr-compatible configuration
	engine := cqrs.NewEngine("test-service-dapr",
		cqrs.WithGRPC(9093),
		cqrs.WithREST(8083),
		cqrs.WithPubSub("test-pubsub", 9094),
	)

	// Register gRPC command for Dapr service invocation
	err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &TestCommand{},
		Handler: testCommandHandler,
		Transport: cqrs.Transport{
			GRPC: &cqrs.GRPC{Method: "DaprTestCommand"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register Dapr command: %v", err)
	}

	// Register REST query
	err = engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &TestQuery{},
		Handler: testQueryHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/api/v1/dapr/query/:id"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register Dapr query: %v", err)
	}

	// Start engine
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	engineErr := make(chan error, 1)
	go func() {
		if err := engine.Start(ctx); err != nil && err != context.Canceled {
			engineErr <- err
		}
	}()

	// Wait for service to be ready
	if err := waitForService("http://localhost:8083/api/v1/dapr/query/123?id=123", 10*time.Second); err != nil {
		t.Fatalf("Service not ready: %v", err)
	}

	t.Run("Dapr_Ready_Check", func(t *testing.T) {
		// Test that the service is ready for Dapr integration
		// This tests the gRPC port is listening for Dapr to connect
		resp, err := http.Get("http://localhost:8083/api/v1/dapr/query/dapr-test?id=dapr-test")
		if err != nil {
			t.Fatalf("Service not ready for Dapr: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		var result TestResponse
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			t.Errorf("Failed to decode response: %v", err)
		}

		if result.ID != "dapr-test" {
			t.Errorf("Expected ID 'dapr-test', got %s", result.ID)
		}
	})
}

func TestValidationRules(t *testing.T) {

	if testing.Short() {
		t.Skip("Skipping validation rules test in short mode")
	}

	t.Log("Testing CQRS validation rules")

	t.Run("V1_Transport_Required", func(t *testing.T) {
		engine := cqrs.NewEngine("test-validation", cqrs.WithREST(8084))

		// Should fail - empty transport
		err := engine.RegisterCommands(cqrs.CommandConfig{
			Command:   &TestCommand{},
			Handler:   testCommandHandler,
			Transport: cqrs.Transport{}, // Empty transport
		})

		if err == nil {
			t.Error("Expected validation error for empty transport")
		}
		if !strings.Contains(err.Error(), "transport") {
			t.Errorf("Expected transport validation error, got: %v", err)
		}
	})

	t.Run("V2_GRPC_Method_Required", func(t *testing.T) {
		engine := cqrs.NewEngine("test-validation", cqrs.WithGRPC(9095))

		// Should fail - gRPC without method
		err := engine.RegisterCommands(cqrs.CommandConfig{
			Command: &TestCommand{},
			Handler: testCommandHandler,
			Transport: cqrs.Transport{
				GRPC: &cqrs.GRPC{}, // Missing method
			},
		})

		if err == nil {
			t.Error("Expected validation error for missing gRPC method")
		}
	})

	t.Run("V3_REST_Method_Path_Required", func(t *testing.T) {
		engine := cqrs.NewEngine("test-validation", cqrs.WithREST(8085))

		// Should fail - REST without method
		err := engine.RegisterCommands(cqrs.CommandConfig{
			Command: &TestCommand{},
			Handler: testCommandHandler,
			Transport: cqrs.Transport{
				REST: &cqrs.REST{Path: "/test"}, // Missing method
			},
		})

		if err == nil {
			t.Error("Expected validation error for missing REST method")
		}

		// Should fail - REST without path
		err = engine.RegisterCommands(cqrs.CommandConfig{
			Command: &TestCommand{},
			Handler: testCommandHandler,
			Transport: cqrs.Transport{
				REST: &cqrs.REST{Method: "POST"}, // Missing path
			},
		})

		if err == nil {
			t.Error("Expected validation error for missing REST path")
		}
	})

	t.Run("V4_PubSub_Topic_Required", func(t *testing.T) {
		engine := cqrs.NewEngine("test-validation",
			cqrs.WithPubSub("test-pubsub", 9096))

		// Should fail - PubSub without topic
		err := engine.RegisterCommands(cqrs.CommandConfig{
			Command: &TestCommand{},
			Handler: testCommandHandler,
			Transport: cqrs.Transport{
				PubSub: &cqrs.PubSub{}, // Missing topic
			},
		})

		if err == nil {
			t.Error("Expected validation error for missing PubSub topic")
		}
	})

	t.Run("V5_Query_PubSub_Only_Forbidden", func(t *testing.T) {
		engine := cqrs.NewEngine("test-validation",
			cqrs.WithPubSub("test-pubsub", 9097))

		// Should fail - Query with only PubSub transport
		err := engine.RegisterQueries(cqrs.QueryConfig{
			Query:   &TestQuery{},
			Handler: testQueryHandler,
			Transport: cqrs.Transport{
				PubSub: &cqrs.PubSub{Topic: "test-topic"}, // Only PubSub
			},
		})

		if err == nil {
			t.Error("Expected validation error for query with only PubSub transport")
		}
	})

	t.Run("V6_Service_Configuration_Required", func(t *testing.T) {
		// Should fail - gRPC transport without gRPC service configured
		engine := cqrs.NewEngine("test-validation") // No transports configured

		err := engine.RegisterCommands(cqrs.CommandConfig{
			Command: &TestCommand{},
			Handler: testCommandHandler,
			Transport: cqrs.Transport{
				GRPC: &cqrs.GRPC{Method: "TestMethod"},
			},
		})

		if err == nil {
			t.Error("Expected validation error for gRPC transport without gRPC service")
		}
	})
}

func TestGracefulShutdown(t *testing.T) {

	if testing.Short() {
		t.Skip("Skipping graceful shutdown test in short mode")
	}

	t.Log("Testing graceful shutdown")

	engine := cqrs.NewEngine("test-shutdown", cqrs.WithREST(8086))

	// Register a simple query for testing
	err := engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &TestQuery{},
		Handler: testQueryHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/shutdown/test/:id"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register query: %v", err)
	}

	// Start engine
	ctx, cancel := context.WithCancel(context.Background())

	engineErr := make(chan error, 1)
	go func() {
		if err := engine.Start(ctx); err != nil && err != context.Canceled {
			engineErr <- err
		}
	}()

	// Wait for service to be ready
	if err := waitForService("http://localhost:8086/shutdown/test/123?id=123", 10*time.Second); err != nil {
		t.Fatalf("Service not ready: %v", err)
	}

	// Test that service is working
	resp, err := http.Get("http://localhost:8086/shutdown/test/123?id=123")
	if err != nil {
		t.Fatalf("Service not responding: %v", err)
	}
	resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	// Trigger graceful shutdown
	cancel()

	// Wait a bit for shutdown to complete
	time.Sleep(500 * time.Millisecond)

	// Test that service is no longer responding
	client := &http.Client{Timeout: 1 * time.Second}
	_, err = client.Get("http://localhost:8086/shutdown/test/123?id=123")
	if err == nil {
		t.Error("Expected service to be shut down, but it's still responding")
	}

	// Check for engine errors
	select {
	case err := <-engineErr:
		if err != context.Canceled {
			t.Errorf("Unexpected engine error: %v", err)
		}
	case <-time.After(2 * time.Second):
		// Engine should have stopped by now
	}
}

func TestErrorHandling(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping error handling test in short mode")
	}

	t.Log("Testing error handling scenarios")

	engine := cqrs.NewEngine("test-errors", cqrs.WithREST(8087))

	// Handler that returns an error
	errorHandler := func(ctx context.Context, cmd any) error {
		return fmt.Errorf("simulated handler error")
	}

	err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &TestCommand{},
		Handler: errorHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "POST", Path: "/error/command"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register error command: %v", err)
	}

	// Start engine
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		if err := engine.Start(ctx); err != nil && err != context.Canceled {
			t.Errorf("Engine error: %v", err)
		}
	}()

	// Wait for service to be ready
	time.Sleep(500 * time.Millisecond)

	t.Run("Handler_Error_Response", func(t *testing.T) {
		// Test that handler errors are properly returned as HTTP errors
		cmdData := TestCommand{ID: "error-test", Name: "Error Command"}
		jsonData, _ := json.Marshal(cmdData)

		resp, err := http.Post("http://localhost:8087/error/command", "application/json", bytes.NewBuffer(jsonData))
		if err != nil {
			t.Fatalf("Failed to send command: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusInternalServerError {
			t.Errorf("Expected status 500, got %d", resp.StatusCode)
		}

		var result map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			t.Errorf("Failed to decode response: %v", err)
		}

		if result["error"] == nil {
			t.Error("Expected error field in response")
		}
	})
}
