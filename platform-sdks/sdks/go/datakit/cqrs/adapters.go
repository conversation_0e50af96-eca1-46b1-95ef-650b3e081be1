package cqrs

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"reflect"

	"github.com/dapr/go-sdk/service/common"
	"github.com/gin-gonic/gin"
)

// wrapGRPCCommand creates a Dapr gRPC service invocation handler for a command
func wrapGRPCCommand(commandType any, handler func(ctx context.Context, cmd any) error) common.ServiceInvocationHandler {
	return func(ctx context.Context, in *common.InvocationEvent) (out *common.Content, err error) {
		// Create a new instance of the command type
		cmdValue := reflect.New(reflect.TypeOf(commandType).Elem()).Interface()

		// Unmarshal the input data
		if err := json.Unmarshal(in.Data, cmdValue); err != nil {
			slog.Warn("Failed to unmarshal gRPC command", "error", err)
			return nil, fmt.Errorf("invalid command data: %w", err)
		}

		// Execute the handler
		if err := handler(ctx, cmdValue); err != nil {
			slog.Error("Command handler failed", "transport", "gRPC", "error", err)
			return nil, fmt.<PERSON><PERSON><PERSON>("command execution failed: %w", err)
		}

		// Return empty response for commands
		return &common.Content{
			ContentType: "application/json",
			Data:        []byte(`{"status":"ok"}`),
		}, nil
	}
}

// wrapGRPCQuery creates a Dapr gRPC service invocation handler for a query
func wrapGRPCQuery(queryType any, handler func(ctx context.Context, q any) (any, error)) common.ServiceInvocationHandler {
	return func(ctx context.Context, in *common.InvocationEvent) (out *common.Content, err error) {
		// Create a new instance of the query type
		queryValue := reflect.New(reflect.TypeOf(queryType).Elem()).Interface()

		// Unmarshal the input data
		if err := json.Unmarshal(in.Data, queryValue); err != nil {
			slog.Warn("Failed to unmarshal gRPC query", "error", err)
			return nil, fmt.Errorf("invalid query data: %w", err)
		}

		// Execute the handler
		result, err := handler(ctx, queryValue)
		if err != nil {
			slog.Error("Query handler failed", "transport", "gRPC", "error", err)
			return nil, fmt.Errorf("query execution failed: %w", err)
		}

		// Marshal the result
		resultData, err := json.Marshal(result)
		if err != nil {
			slog.Warn("Failed to marshal query result", "error", err)
			return nil, fmt.Errorf("failed to marshal result: %w", err)
		}

		return &common.Content{
			ContentType: "application/json",
			Data:        resultData,
		}, nil
	}
}

// wrapRESTCommand creates a Gin HTTP handler for a command
func wrapRESTCommand(commandType any, handler func(ctx context.Context, cmd any) error) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a new instance of the command type
		cmdValue := reflect.New(reflect.TypeOf(commandType).Elem()).Interface()

		// Bind path parameters first (if any) - only fail if there are actual path parameters
		if len(c.Params) > 0 {
			if err := c.ShouldBindUri(cmdValue); err != nil {
				slog.Warn("Failed to bind REST path parameters", "error", err)
				c.JSON(http.StatusBadRequest, gin.H{"error": "invalid path parameters", "details": err.Error()})
				return
			}
		}

		// Then bind the request body
		if err := c.ShouldBindJSON(cmdValue); err != nil {
			slog.Warn("Failed to bind REST command", "error", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request data", "details": err.Error()})
			return
		}

		// Execute the handler
		if err := handler(c.Request.Context(), cmdValue); err != nil {
			slog.Error("Command handler failed", "transport", "REST", "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "command execution failed", "details": err.Error()})
			return
		}

		// Return success response
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	}
}

// wrapRESTQuery creates a Gin HTTP handler for a query
func wrapRESTQuery(queryType any, handler func(ctx context.Context, q any) (any, error)) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a new instance of the query type
		queryValue := reflect.New(reflect.TypeOf(queryType).Elem()).Interface()

		// Bind the request data (support JSON body, query parameters, and path parameters)
		if c.Request.Method == "GET" {
			// For GET requests, bind path parameters first (if any)
			if len(c.Params) > 0 {
				if err := c.ShouldBindUri(queryValue); err != nil {
					slog.Warn("Failed to bind REST path parameters", "error", err)
					c.JSON(http.StatusBadRequest, gin.H{"error": "invalid path parameters", "details": err.Error()})
					return
				}
			}
			// Then bind query parameters
			if err := c.ShouldBindQuery(queryValue); err != nil {
				slog.Warn("Failed to bind REST query parameters", "error", err)
				c.JSON(http.StatusBadRequest, gin.H{"error": "invalid query parameters", "details": err.Error()})
				return
			}
		} else {
			// For other methods, bind JSON body
			if err := c.ShouldBindJSON(queryValue); err != nil {
				slog.Warn("Failed to bind REST query", "error", err)
				c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request data", "details": err.Error()})
				return
			}
		}

		// Execute the handler
		result, err := handler(c.Request.Context(), queryValue)
		if err != nil {
			slog.Error("Query handler failed", "transport", "REST", "error", err)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "query execution failed", "details": err.Error()})
			return
		}

		// Return the result
		c.JSON(http.StatusOK, result)
	}
}

// wrapPubSubCommand creates a Dapr PubSub event handler for a command
func wrapPubSubCommand(commandType any, handler func(ctx context.Context, cmd any) error) common.TopicEventHandler {
	return func(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
		// Create a new instance of the command type
		cmdValue := reflect.New(reflect.TypeOf(commandType).Elem()).Interface()

		// Unmarshal the event data
		if err := json.Unmarshal(e.RawData, cmdValue); err != nil {
			slog.Warn("Failed to unmarshal PubSub command", "topic", e.Topic, "error", err)
			// Don't retry for unmarshal errors
			return false, fmt.Errorf("invalid event data: %w", err)
		}

		// Execute the handler
		if err := handler(ctx, cmdValue); err != nil {
			slog.Error("PubSub command handler failed", "topic", e.Topic, "error", err)
			// Retry on handler errors
			return true, fmt.Errorf("command execution failed: %w", err)
		}

		slog.Debug("Successfully processed PubSub command", "topic", e.Topic)
		return false, nil
	}
}
