package cqrs

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test DTOs for MCP
type MCPUpdateCommand struct {
	ID    string `json:"id"`
	Value string `json:"value"`
}

type MCPGetQuery struct {
	ID string `json:"id"`
}

func TestMCPIntegration(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Create engine with MCP support
	engine := NewEngine("test-service",
		WithREST(8080), // Need a valid port for gin router initialization
		WithMCP(&MCPConfig{
			Path:     "/mcp",
			EnableWS: false,
		}),
	)

	// Command handler
	commandExecuted := false
	commandHandler := func(ctx context.Context, cmd any) error {
		commandExecuted = true
		c := cmd.(*MCPUpdateCommand)
		assert.Equal(t, "123", c.<PERSON>)
		assert.Equal(t, "test-value", c.Value)
		return nil
	}

	// Query handler
	queryHandler := func(ctx context.Context, q any) (any, error) {
		query := q.(*MCPGetQuery)
		return map[string]string{
			"id":     query.ID,
			"result": "query-result",
		}, nil
	}

	// Register command with MCP transport
	err := engine.RegisterCommands(CommandConfig{
		Command: &MCPUpdateCommand{},
		Handler: commandHandler,
		Transport: Transport{
			MCP: &MCP{
				Title:       "Test Command",
				Description: "A test command for MCP",
			},
		},
	})
	require.NoError(t, err)

	// Register query with MCP transport
	err = engine.RegisterQueries(QueryConfig{
		Query:   &MCPGetQuery{},
		Handler: queryHandler,
		Transport: Transport{
			MCP: &MCP{
				Description: "A test query for MCP",
				OutputSchema: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"id":     map[string]string{"type": "string"},
						"result": map[string]string{"type": "string"},
					},
				},
			},
		},
	})
	require.NoError(t, err)

	// Initialize services and wire transports
	err = engine.initializeServices()
	require.NoError(t, err)
	err = engine.wireTransports()
	require.NoError(t, err)

	// Test 1: Initialize
	t.Run("Initialize", func(t *testing.T) {
		req := jsonrpcRequest{
			JSONRPC: "2.0",
			ID:      json.RawMessage(`1`),
			Method:  "initialize",
			Params:  json.RawMessage(`{}`),
		}

		w := performJSONRPCRequest(engine, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var resp jsonrpcResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(t, err)
		assert.Nil(t, resp.Error)
		assert.NotNil(t, resp.Result)

		result := resp.Result.(map[string]interface{})
		assert.Equal(t, "2025-06-18", result["protocolVersion"])
		assert.NotNil(t, result["serverInfo"])
	})

	// Test 2: Tools List
	t.Run("ToolsList", func(t *testing.T) {
		req := jsonrpcRequest{
			JSONRPC: "2.0",
			ID:      json.RawMessage(`2`),
			Method:  "tools/list",
		}

		w := performJSONRPCRequest(engine, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var resp jsonrpcResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(t, err)
		assert.Nil(t, resp.Error)

		result := resp.Result.(map[string]interface{})
		tools := result["tools"].([]interface{})
		assert.Len(t, tools, 2) // One command and one query

		// Check tools contain expected fields
		for _, tool := range tools {
			toolMap := tool.(map[string]interface{})
			assert.NotEmpty(t, toolMap["name"])
			assert.NotEmpty(t, toolMap["description"])
			assert.NotNil(t, toolMap["inputSchema"])
		}
	})

	// Test 3: Call Command Tool
	t.Run("CallCommandTool", func(t *testing.T) {
		commandExecuted = false
		req := jsonrpcRequest{
			JSONRPC: "2.0",
			ID:      json.RawMessage(`3`),
			Method:  "tools/call",
			Params: json.RawMessage(`{
				"name": "mcpupdate",
				"arguments": {"id": "123", "value": "test-value"}
			}`),
		}

		w := performJSONRPCRequest(engine, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var resp jsonrpcResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(t, err)
		assert.Nil(t, resp.Error)
		assert.True(t, commandExecuted)

		result := resp.Result.(map[string]interface{})
		// Check the new MCP content format
		content := result["content"].([]interface{})
		assert.Len(t, content, 1)
		contentItem := content[0].(map[string]interface{})
		assert.Equal(t, "text", contentItem["type"])
		assert.Equal(t, "Operation completed successfully", contentItem["text"])
	})

	// Test 4: Call Query Tool
	t.Run("CallQueryTool", func(t *testing.T) {
		req := jsonrpcRequest{
			JSONRPC: "2.0",
			ID:      json.RawMessage(`4`),
			Method:  "tools/call",
			Params: json.RawMessage(`{
				"name": "mcpget",
				"arguments": {"id": "456"}
			}`),
		}

		w := performJSONRPCRequest(engine, req)
		assert.Equal(t, http.StatusOK, w.Code)

		var resp jsonrpcResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(t, err)
		assert.Nil(t, resp.Error)

		result := resp.Result.(map[string]interface{})
		// Check the new MCP content format
		content := result["content"].([]interface{})
		assert.Len(t, content, 1)
		contentItem := content[0].(map[string]interface{})
		assert.Equal(t, "text", contentItem["type"])

		// Parse the JSON text content to verify the query result
		var queryResult map[string]string
		err = json.Unmarshal([]byte(contentItem["text"].(string)), &queryResult)
		require.NoError(t, err)
		assert.Equal(t, "456", queryResult["id"])
		assert.Equal(t, "query-result", queryResult["result"])
	})

	// Test 5: Unknown Method
	t.Run("UnknownMethod", func(t *testing.T) {
		req := jsonrpcRequest{
			JSONRPC: "2.0",
			ID:      json.RawMessage(`5`),
			Method:  "unknown/method",
		}

		w := performJSONRPCRequest(engine, req)
		assert.Equal(t, http.StatusNotFound, w.Code)

		var resp jsonrpcResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(t, err)
		assert.NotNil(t, resp.Error)
		assert.Equal(t, errMethodNotFound, resp.Error.Code)
	})

	// Test 6: Unknown Tool
	t.Run("UnknownTool", func(t *testing.T) {
		req := jsonrpcRequest{
			JSONRPC: "2.0",
			ID:      json.RawMessage(`6`),
			Method:  "tools/call",
			Params: json.RawMessage(`{
				"name": "non-existent",
				"arguments": {}
			}`),
		}

		w := performJSONRPCRequest(engine, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)

		var resp jsonrpcResponse
		err := json.Unmarshal(w.Body.Bytes(), &resp)
		require.NoError(t, err)
		assert.NotNil(t, resp.Error)
		assert.Equal(t, errInvalidParams, resp.Error.Code)
	})
}

func TestMCPValidation(t *testing.T) {
	// Test that MCP description is required
	t.Run("MCPDescriptionRequired", func(t *testing.T) {
		engine := NewEngine("test", WithMCP(&MCPConfig{}))

		err := engine.RegisterCommands(CommandConfig{
			Command: &MCPUpdateCommand{},
			Handler: func(ctx context.Context, cmd any) error { return nil },
			Transport: Transport{
				MCP: &MCP{
					// Description is missing
				},
			},
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "MCP description is required")
	})

	// Test that MCP requires WithMCP configuration
	t.Run("MCPRequiresConfiguration", func(t *testing.T) {
		engine := NewEngine("test") // No WithMCP

		err := engine.RegisterCommands(CommandConfig{
			Command: &MCPUpdateCommand{},
			Handler: func(ctx context.Context, cmd any) error { return nil },
			Transport: Transport{
				MCP: &MCP{
					Description: "Test command",
				},
			},
		})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "engine not configured with WithMCP")
	})
}

// Helper function to perform JSON-RPC request
func performJSONRPCRequest(engine *Engine, req jsonrpcRequest) *httptest.ResponseRecorder {
	body, _ := json.Marshal(req)
	r := httptest.NewRequest("POST", "/mcp", bytes.NewBuffer(body))
	r.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	engine.ginRouter.ServeHTTP(w, r)

	return w
}

// Benchmark test
func BenchmarkMCPToolsCall(b *testing.B) {
	gin.SetMode(gin.ReleaseMode)

	engine := NewEngine("bench-service",
		WithREST(8081), // Need a valid port for gin router initialization
		WithMCP(&MCPConfig{Path: "/mcp"}),
	)

	_ = engine.RegisterCommands(CommandConfig{
		Command: &MCPUpdateCommand{},
		Handler: func(ctx context.Context, cmd any) error {
			time.Sleep(1 * time.Microsecond) // Simulate work
			return nil
		},
		Transport: Transport{
			MCP: &MCP{Description: "Benchmark command"},
		},
	})

	_ = engine.initializeServices()
	_ = engine.wireTransports()

	req := jsonrpcRequest{
		JSONRPC: "2.0",
		ID:      json.RawMessage(`1`),
		Method:  "tools/call",
		Params:  json.RawMessage(`{"name":"mcpupdate","arguments":{"id":"1","value":"test"}}`),
	}

	body, _ := json.Marshal(req)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		r := httptest.NewRequest("POST", "/mcp", bytes.NewBuffer(body))
		r.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		engine.ginRouter.ServeHTTP(w, r)
	}
}

func TestFormatResultAsText(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected string
	}{
		{
			name:     "nil input",
			input:    nil,
			expected: "null",
		},
		{
			name:     "string input",
			input:    "hello world",
			expected: "hello world",
		},
		{
			name:     "number input",
			input:    42,
			expected: "42",
		},
		{
			name:     "struct input",
			input:    struct{ Name string }{Name: "test"},
			expected: "{\n  \"Name\": \"test\"\n}",
		},
		{
			name:     "map input",
			input:    map[string]string{"key": "value"},
			expected: "{\n  \"key\": \"value\"\n}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatResultAsText(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestMCPResponseFormat(t *testing.T) {
	// Test that we create the correct MCP response structure
	result := map[string]any{
		"content": []map[string]any{
			{
				"type": "text",
				"text": "test response",
			},
		},
	}

	// Verify the structure
	assert.Contains(t, result, "content")
	content := result["content"].([]map[string]any)
	assert.Len(t, content, 1)
	assert.Equal(t, "text", content[0]["type"])
	assert.Equal(t, "test response", content[0]["text"])
}
