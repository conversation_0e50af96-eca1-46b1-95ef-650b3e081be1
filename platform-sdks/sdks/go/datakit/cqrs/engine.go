package cqrs

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strconv"
	"strings"

	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/grpc"
	"github.com/gin-gonic/gin"
)

// Engine is the core CQRS engine that manages transport configuration and routing
type Engine struct {
	appID      string
	version    string // Service version
	grpcPort   int
	restPort   int
	subPort    int
	pubsubName string

	commands []CommandConfig
	queries  []QueryConfig

	// Internal services
	grpcService       common.Service
	subscriberService common.Service
	ginRouter         *gin.Engine
	httpServer        *http.Server

	// MCP support
	mcpCfg *MCPConfig          // nil => MCP disabled
	tools  map[string]*ToolDef // name → tool definition + handler
}

// NewEngine creates a new CQRS engine with the specified app ID and options
func NewEngine(appID string, options ...Option) *Engine {
	engine := &Engine{
		appID:    appID,
		version:  "1.0.0", // Default version
		commands: make([]CommandConfig, 0),
		queries:  make([]QueryConfig, 0),
		tools:    make(map[string]*ToolDef),
	}

	// Apply options
	for _, option := range options {
		option(engine)
	}

	return engine
}

// Start initializes and starts all configured transports
func (e *Engine) Start(ctx context.Context) error {
	slog.Info("Starting CQRS engine",
		"app_id", e.appID,
		"grpc_port", e.grpcPort,
		"rest_port", e.restPort,
		"pubsub_port", e.subPort,
		"pubsub_component", e.pubsubName,
	)

	// Initialize services based on configuration
	if err := e.initializeServices(); err != nil {
		return fmt.Errorf("failed to initialize services: %w", err)
	}

	// Wire all registered commands and queries
	if err := e.wireTransports(); err != nil {
		return fmt.Errorf("failed to wire transports: %w", err)
	}

	// Start services concurrently
	errCh := make(chan error, 3)

	// Start gRPC service
	if e.grpcService != nil {
		go func() {
			slog.Info("Starting gRPC service", "port", e.grpcPort)
			if err := e.grpcService.Start(); err != nil {
				errCh <- fmt.Errorf("gRPC service failed: %w", err)
			}
		}()
	}

	// Start subscriber service
	if e.subscriberService != nil {
		go func() {
			slog.Info("Starting subscriber service", "port", e.subPort)
			if err := e.subscriberService.Start(); err != nil {
				errCh <- fmt.Errorf("subscriber service failed: %w", err)
			}
		}()
	}

	// Start REST service
	if e.ginRouter != nil {
		e.httpServer = &http.Server{
			Addr:    ":" + strconv.Itoa(e.restPort),
			Handler: e.ginRouter,
		}
		go func() {
			slog.Info("Starting REST service", "port", e.restPort)
			if err := e.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				errCh <- fmt.Errorf("REST service failed: %w", err)
			}
		}()
	}

	// Wait for context cancellation or service error
	select {
	case <-ctx.Done():
		slog.Info("Engine shutting down due to context cancellation")
		e.shutdown()
		return ctx.Err()
	case err := <-errCh:
		e.shutdown()
		return err
	}
}

// initializeServices creates the necessary Dapr services and Gin router based on configuration
func (e *Engine) initializeServices() error {
	// Initialize gRPC service if port is configured
	if e.grpcPort > 0 {
		service, err := daprd.NewService(":" + strconv.Itoa(e.grpcPort))
		if err != nil {
			return fmt.Errorf("failed to create gRPC service: %w", err)
		}
		e.grpcService = service
	}

	// Initialize subscriber service if configured and we have PubSub handlers
	if e.subPort > 0 && e.pubsubName != "" && e.hasPubSubHandlers() {
		service, err := daprd.NewService(":" + strconv.Itoa(e.subPort))
		if err != nil {
			return fmt.Errorf("failed to create subscriber service: %w", err)
		}
		e.subscriberService = service
	}

	// Initialize Gin router if REST port is configured
	if e.restPort > 0 {
		// Set Gin mode based on log level
		if slog.Default().Enabled(context.Background(), slog.LevelDebug) {
			gin.SetMode(gin.DebugMode)
			slog.Debug("Gin set to debug mode for detailed request logging")
		} else {
			gin.SetMode(gin.ReleaseMode)
		}

		e.ginRouter = gin.New()

		// Add middleware
		// Always use beautiful colored Gin logs
		e.ginRouter.Use(gin.Logger())
		e.ginRouter.Use(gin.Recovery())

		// Add detailed structured logging middleware only in debug mode
		if slog.Default().Enabled(context.Background(), slog.LevelDebug) {
			// Add request body capture middleware for MCP debugging
			e.ginRouter.Use(func(c *gin.Context) {
				// Only capture body for POST requests to /mcp
				if c.Request.Method == "POST" && strings.Contains(c.Request.URL.Path, "/mcp") {
					// Read and store the body
					bodyBytes, err := io.ReadAll(c.Request.Body)
					if err == nil {
						// Store original body in context for debugging
						c.Set("raw_request_body", string(bodyBytes))
						// Replace the body so Gin can still read it
						c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
					}
				}
				c.Next()
			})
		}

		// Add health check endpoint
		e.setupHealthCheck()
	}

	return nil
}

// setupHealthCheck adds a health check endpoint
func (e *Engine) setupHealthCheck() {
	e.ginRouter.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": e.appID,
			"version": e.version,
		})
	})
	slog.Debug("Registered health check endpoint", "method", "GET", "path", "/health")
}

// hasPubSubHandlers returns true if any registered commands or queries use PubSub transport
func (e *Engine) hasPubSubHandlers() bool {
	for _, cmd := range e.commands {
		if cmd.Transport.hasPubSub() {
			return true
		}
	}
	for _, query := range e.queries {
		if query.Transport.hasPubSub() {
			return true
		}
	}
	return false
}

// wireTransports configures all transport handlers for registered commands and queries
func (e *Engine) wireTransports() error {
	// Wire command transports
	for _, cmd := range e.commands {
		if err := e.wireCommandTransports(cmd); err != nil {
			return fmt.Errorf("failed to wire command transports: %w", err)
		}
	}

	// Wire query transports
	for _, query := range e.queries {
		if err := e.wireQueryTransports(query); err != nil {
			return fmt.Errorf("failed to wire query transports: %w", err)
		}
	}

	// Wire MCP routes after all commands/queries are registered
	e.wireRoutes()

	return nil
}

// wireCommandTransports configures transport handlers for a single command
func (e *Engine) wireCommandTransports(cmd CommandConfig) error {
	transport := cmd.Transport
	// Apply defaults to ensure all values are set
	applyTransportDefaults(&transport, cmd.Command, false)

	// Wire gRPC transport
	if transport.hasGRPC() && e.grpcService != nil {
		handler := wrapGRPCCommand(cmd.Command, cmd.Handler)
		if err := e.grpcService.AddServiceInvocationHandler(transport.GRPC.Method, handler); err != nil {
			return fmt.Errorf("failed to add gRPC handler for method %s: %w", transport.GRPC.Method, err)
		}
		slog.Debug("Registered gRPC command handler", "method", transport.GRPC.Method)
	}

	// Wire REST transport
	if transport.hasREST() && e.ginRouter != nil {
		handler := wrapRESTCommand(cmd.Command, cmd.Handler)
		e.ginRouter.Handle(transport.REST.Method, transport.REST.Path, handler)
		slog.Debug("Registered REST command handler", "method", transport.REST.Method, "path", transport.REST.Path)
	}

	// Wire PubSub transport
	if transport.hasPubSub() && e.subscriberService != nil {
		handler := wrapPubSubCommand(cmd.Command, cmd.Handler)
		subscription := &common.Subscription{
			PubsubName: e.pubsubName,
			Topic:      transport.PubSub.Topic,
			Route:      transport.PubSub.Route,
		}
		if err := e.subscriberService.AddTopicEventHandler(subscription, handler); err != nil {
			return fmt.Errorf("failed to add PubSub handler for topic %s: %w", transport.PubSub.Topic, err)
		}
		slog.Debug("Registered PubSub command handler", "pubsub", e.pubsubName, "topic", transport.PubSub.Topic)
	}

	// Wire MCP transport
	if transport.hasMCP() && e.mcpCfg != nil {
		toolName := generateToolName(cmd.Command)
		if err := e.registerTool(toolName, transport.MCP, cmd.Handler, cmd.Command, false); err != nil {
			return fmt.Errorf("failed to register MCP tool %s: %w", toolName, err)
		}
	}

	return nil
}

// wireQueryTransports configures transport handlers for a single query
func (e *Engine) wireQueryTransports(query QueryConfig) error {
	transport := query.Transport
	// Apply defaults to ensure all values are set
	applyTransportDefaults(&transport, query.Query, true)

	// Wire gRPC transport
	if transport.hasGRPC() && e.grpcService != nil {
		handler := wrapGRPCQuery(query.Query, query.Handler)
		if err := e.grpcService.AddServiceInvocationHandler(transport.GRPC.Method, handler); err != nil {
			return fmt.Errorf("failed to add gRPC handler for method %s: %w", transport.GRPC.Method, err)
		}
		slog.Debug("Registered gRPC query handler", "method", transport.GRPC.Method)
	}

	// Wire REST transport
	if transport.hasREST() && e.ginRouter != nil {
		handler := wrapRESTQuery(query.Query, query.Handler)
		e.ginRouter.Handle(transport.REST.Method, transport.REST.Path, handler)
		slog.Debug("Registered REST query handler", "method", transport.REST.Method, "path", transport.REST.Path)
	}

	// Note: PubSub for queries is handled in validation - queries cannot be subscriber-only

	// Wire MCP transport
	if transport.hasMCP() && e.mcpCfg != nil {
		toolName := generateToolName(query.Query)
		if err := e.registerTool(toolName, transport.MCP, query.Handler, query.Query, true); err != nil {
			return fmt.Errorf("failed to register MCP tool %s: %w", toolName, err)
		}
	}

	return nil
}

// shutdown gracefully shuts down all services
func (e *Engine) shutdown() {
	slog.Info("Shutting down services")

	// Shutdown HTTP server
	if e.httpServer != nil {
		if err := e.httpServer.Shutdown(context.Background()); err != nil {
			slog.Error("Error shutting down HTTP server", "error", err)
		}
	}

	// Shutdown Dapr services
	if e.grpcService != nil {
		if err := e.grpcService.Stop(); err != nil {
			slog.Error("Error shutting down gRPC service", "error", err)
		}
	}

	if e.subscriberService != nil {
		if err := e.subscriberService.Stop(); err != nil {
			slog.Error("Error shutting down subscriber service", "error", err)
		}
	}

	slog.Info("All services shut down")
}
