package cqrs

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"path"
	"reflect"
	"sync"

	"github.com/gin-gonic/gin"
)

// MCP describes how a command or query becomes an MCP tool.
// Fields are strictly according to the MCP specification - no extra fields.
type MCP struct {
	// Title is the short name of the tool (optional)
	Title string `json:"title,omitempty"`

	// Description is a human-readable description of what the tool does (recommended)
	Description string `json:"description"`

	// InputSchema is the JSON Schema for the tool's input parameters (optional)
	InputSchema any `json:"inputSchema,omitempty"`

	// OutputSchema is the JSON Schema of the result (optional)
	OutputSchema any `json:"outputSchema,omitempty"`

	// Annotations provides future-proof extension points (optional)
	Annotations map[string]any `json:"annotations,omitempty"`
}

// MCP protocol version we support - but we'll be compatible with older versions
const supportedProtocolVersion = "2025-06-18"

// List of protocol versions we can work with (newest first)
var compatibleProtocolVersions = []string{
	"2025-06-18",
	"2025-03-26", // Cursor's version
	"2024-11-05", // Legacy support
}

// ANSI color codes
const (
	colorReset  = "\033[0m"
	colorBlue   = "\033[34m"
	colorGreen  = "\033[32m"
	colorYellow = "\033[33m"
	colorCyan   = "\033[36m"
	colorGray   = "\033[90m"
)

// isTerminal checks if output is going to a terminal (for color support)
func isTerminal() bool {
	fileInfo, _ := os.Stdout.Stat()
	return (fileInfo.Mode() & os.ModeCharDevice) != 0
}

// formatAndPrintJSON formats and prints JSON with colors to stdout
func formatAndPrintJSON(label string, data interface{}) {
	if !slog.Default().Enabled(context.Background(), slog.LevelDebug) {
		return
	}

	jsonBytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		fmt.Printf("%s%s%s (json marshal error: %v)\n", colorYellow, label, colorReset, err)
		return
	}

	// Add syntax highlighting if terminal supports colors
	jsonStr := string(jsonBytes)
	if isTerminal() {
		// Simple syntax highlighting
		jsonStr = addJSONColors(jsonStr)
		fmt.Printf("%s%s%s\n%s\n", colorCyan, label, colorReset, jsonStr)
	} else {
		fmt.Printf("%s\n%s\n", label, jsonStr)
	}
}

// addJSONColors adds basic color highlighting to JSON
func addJSONColors(jsonStr string) string {
	// This is a simple implementation - in production you might want a more sophisticated JSON colorizer
	// For now, we'll just highlight strings and numbers
	result := jsonStr

	// You could add more sophisticated coloring here
	// For simplicity, we'll keep it basic for now
	return result
}

// isProtocolVersionCompatible checks if we can work with the requested version
func isProtocolVersionCompatible(requestedVersion string) string {
	if requestedVersion == "" {
		return supportedProtocolVersion
	}

	// Check if we support this version
	for _, supported := range compatibleProtocolVersions {
		if requestedVersion == supported {
			return requestedVersion // Use client's preferred version
		}
	}

	// Version not supported, use our latest
	return supportedProtocolVersion
}

// TODO: Add back when implementing WebSocket support
// wsUpgrader is used to upgrade HTTP connections to WebSocket
// var wsUpgrader = websocket.Upgrader{
// 	CheckOrigin: func(r *http.Request) bool {
// 		// TODO: In production, implement proper origin checking
// 		return true
// 	},
// }

// logRequestStart logs the start of an MCP request with separator
func logRequestStart(method, id, userAgent string) {
	slog.Debug("━━━━━━━━━━━━━━━━━━━━")
	slog.Debug("→ MCP Request START",
		"method", method,
		"id", id,
		"user_agent", userAgent,
	)
}

// logRequestEnd logs the end of an MCP request
func logRequestEnd(method string) {
	slog.Debug("← MCP Request END", "method", method)
	slog.Debug("━━━━━━━━━━━━━━━━━━━━")
}

// handleMCP handles JSON-RPC requests for MCP
func (e *Engine) handleMCP(c *gin.Context) {
	var req jsonrpcRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Get the raw body from context (captured by middleware)
		rawBody := ""
		if body, exists := c.Get("raw_request_body"); exists {
			if bodyStr, ok := body.(string); ok {
				rawBody = bodyStr
			}
		}

		logRequestStart("PARSE_ERROR", "unknown", c.GetHeader("User-Agent"))
		slog.Warn("→ MCP parse error",
			"error", err,
			"raw_body", rawBody,
			"content_length", c.Request.ContentLength,
		)
		e.writeJSONRPCError(c, nil, errParseError, "Invalid JSON", http.StatusBadRequest)
		logRequestEnd("PARSE_ERROR")
		return
	}

	logRequestStart(req.Method, string(req.ID), c.GetHeader("User-Agent"))

	// Log formatted request in debug mode
	formatAndPrintJSON("📥 MCP Request", req)

	e.dispatchJSONRPC(c, &req)
	logRequestEnd(req.Method)
}

// handleMCPGet handles GET requests to MCP endpoint for discovery
func (e *Engine) handleMCPGet(c *gin.Context) {
	slog.Debug("→ MCP discovery request",
		"user_agent", c.GetHeader("User-Agent"),
	)

	// Return MCP server information for discovery
	info := map[string]any{
		"name":      e.appID,
		"version":   e.version,
		"protocol":  "mcp",
		"transport": []string{"http", "websocket"},
		"endpoints": map[string]any{
			"jsonrpc":   e.mcpCfg.Path,
			"websocket": e.mcpCfg.Path + "/ws",
		},
		"capabilities": map[string]any{
			"tools": map[string]any{
				"count": len(e.tools),
			},
		},
	}

	// Log formatted response in debug mode
	formatAndPrintJSON("📤 MCP Discovery Response", info)

	slog.Debug("← MCP discovery response", "tools_count", len(e.tools))
	c.JSON(http.StatusOK, info)
}

// dispatchJSONRPC routes JSON-RPC requests to appropriate handlers
func (e *Engine) dispatchJSONRPC(c *gin.Context, req *jsonrpcRequest) {
	switch req.Method {
	case "initialize":
		e.handleInitialize(c, req)
	case "tools/list":
		e.handleToolsList(c, req)
	case "tools/call":
		e.handleToolsCall(c, req)
	case "notifications/initialized":
		// This is a notification from the client, no response needed
		slog.Debug("← MCP client initialized notification")
		c.JSON(http.StatusOK, gin.H{})
		return
	default:
		slog.Debug("← MCP error response", "method", req.Method, "error", "method not found")
		e.writeJSONRPCError(c, req.ID, errMethodNotFound, fmt.Sprintf("Unknown method: %s", req.Method), http.StatusNotFound)
	}
}

// initializeParams represents the parameters for the initialize method
type initializeParams struct {
	ProtocolVersion string         `json:"protocolVersion"`
	Capabilities    map[string]any `json:"capabilities"`
	ClientInfo      map[string]any `json:"clientInfo"`
}

// handleInitialize handles the initialize method
func (e *Engine) handleInitialize(c *gin.Context, req *jsonrpcRequest) {
	// Parse initialize parameters
	var params initializeParams
	if req.Params != nil {
		if err := json.Unmarshal(req.Params, &params); err == nil {
			if params.ClientInfo != nil {
				slog.Debug("  client info",
					"name", params.ClientInfo["name"],
					"version", params.ClientInfo["version"],
				)
			}
		}
	}

	// MCP version negotiation with compatibility
	protocolVersion := isProtocolVersionCompatible(params.ProtocolVersion)
	if params.ProtocolVersion != "" && params.ProtocolVersion != protocolVersion {
		slog.Info("Using compatible protocol version",
			"requested", params.ProtocolVersion,
			"using", protocolVersion)
	}

	result := map[string]any{
		"capabilities": map[string]any{
			"tools": map[string]any{
				"listChanged": true,
			},
		},
		"protocolVersion": protocolVersion,
		"serverInfo": map[string]any{
			"name":    e.appID,
			"version": e.version,
		},
	}

	slog.Debug("← MCP initialize response",
		"protocol", protocolVersion,
		"server", e.appID,
	)
	e.writeJSONRPCResult(c, req.ID, result)
}

// handleToolsList handles the tools/list method
func (e *Engine) handleToolsList(c *gin.Context, req *jsonrpcRequest) {
	tools := make([]any, 0, len(e.tools))

	// Create a sorted list for consistent output
	for _, tool := range e.tools {
		toolInfo := map[string]any{
			"name":        tool.Name,
			"description": tool.Description,
		}

		if tool.Title != "" {
			toolInfo["title"] = tool.Title
		}
		if tool.InputSchema != nil {
			toolInfo["inputSchema"] = tool.InputSchema
		}
		if tool.OutputSchema != nil {
			toolInfo["outputSchema"] = tool.OutputSchema
		}
		if len(tool.Annotations) > 0 {
			toolInfo["annotations"] = tool.Annotations
		}

		tools = append(tools, toolInfo)
	}

	result := map[string]any{
		"tools": tools,
	}

	slog.Debug("← MCP tools/list response", "tools_count", len(tools))
	e.writeJSONRPCResult(c, req.ID, result)
}

// toolsCallParams represents the parameters for tools/call
type toolsCallParams struct {
	Name      string          `json:"name"`
	Arguments json.RawMessage `json:"arguments"`
}

// handleToolsCall handles the tools/call method
func (e *Engine) handleToolsCall(c *gin.Context, req *jsonrpcRequest) {
	// Parse params
	var params toolsCallParams
	if req.Params != nil {
		if err := json.Unmarshal(req.Params, &params); err != nil {
			slog.Debug("← MCP error response", "error", "invalid parameters")
			e.writeJSONRPCError(c, req.ID, errInvalidParams, "Invalid parameters", http.StatusBadRequest)
			return
		}
	}

	slog.Debug("  calling tool", "name", params.Name)

	// Find the tool
	tool, exists := e.tools[params.Name]
	if !exists {
		slog.Debug("← MCP error response", "error", "tool not found", "tool", params.Name)
		e.writeJSONRPCError(c, req.ID, errInvalidParams, fmt.Sprintf("Tool not found: %s", params.Name), http.StatusBadRequest)
		return
	}

	// Create instance of the DTO using stored type
	dtoType := tool.DTOType
	if dtoType == nil {
		e.writeJSONRPCError(c, req.ID, errInternalError, "DTO type not available for tool", http.StatusInternalServerError)
		return
	}

	// Create new instance
	var dto any
	if dtoType.Kind() == reflect.Ptr {
		// DTO is a pointer type, create a new instance of the element type
		dto = reflect.New(dtoType.Elem()).Interface()
	} else {
		// DTO is a value type, create a pointer to a new instance
		dto = reflect.New(dtoType).Interface()
	}

	// Unmarshal arguments into DTO
	if params.Arguments != nil {
		if err := json.Unmarshal(params.Arguments, dto); err != nil {
			e.writeJSONRPCError(c, req.ID, errInvalidParams, fmt.Sprintf("Invalid arguments: %v", err), http.StatusBadRequest)
			return
		}
	}

	// Call the handler
	ctx := c.Request.Context()
	if tool.IsQuery {
		// Call query handler
		handler := tool.Handler.(func(context.Context, any) (any, error))
		result, err := handler(ctx, dto)
		if err != nil {
			slog.Debug("← MCP error response", "error", "query execution failed", "details", err.Error())
			e.writeJSONRPCError(c, req.ID, errInternalError, fmt.Sprintf("Query execution failed: %v", err), http.StatusInternalServerError)
			return
		}

		// Wrap result in MCP content format
		mcpResult := map[string]any{
			"content": []map[string]any{
				{
					"type": "text",
					"text": formatResultAsText(result),
				},
			},
		}

		slog.Debug("← MCP tool response", "tool", params.Name, "type", "query")
		e.writeJSONRPCResult(c, req.ID, mcpResult)
	} else {
		// Call command handler
		handler := tool.Handler.(func(context.Context, any) error)
		err := handler(ctx, dto)
		if err != nil {
			slog.Debug("← MCP error response", "error", "command execution failed", "details", err.Error())
			e.writeJSONRPCError(c, req.ID, errInternalError, fmt.Sprintf("Command execution failed: %v", err), http.StatusInternalServerError)
			return
		}

		// Wrap result in MCP content format
		mcpResult := map[string]any{
			"content": []map[string]any{
				{
					"type": "text",
					"text": "Operation completed successfully",
				},
			},
		}

		slog.Debug("← MCP tool response", "tool", params.Name, "type", "command")
		e.writeJSONRPCResult(c, req.ID, mcpResult)
	}
}

// upgradeWS upgrades HTTP connection to WebSocket for MCP
func (e *Engine) upgradeWS(c *gin.Context) {
	// TODO: Implement WebSocket MCP communication
	// This would involve:
	// - Reading JSON-RPC messages from WebSocket
	// - Dispatching to the same handlers as HTTP
	// - Sending responses back via WebSocket
	// - Handling connection lifecycle
}

// wireRoutes wires all routes including MCP if enabled
func (e *Engine) wireRoutes() {
	if e.ginRouter == nil {
		return
	}

	// Wire MCP routes if configured
	if e.mcpCfg != nil {
		// POST for JSON-RPC operations
		e.ginRouter.POST(e.mcpCfg.Path, e.handleMCP)
		slog.Debug("Registered MCP JSON-RPC handler", "method", "POST", "path", e.mcpCfg.Path)

		// GET for discovery and capabilities
		e.ginRouter.GET(e.mcpCfg.Path, e.handleMCPGet)
		slog.Debug("Registered MCP discovery handler", "method", "GET", "path", e.mcpCfg.Path)

		if e.mcpCfg.EnableWS {
			wsPath := path.Join(e.mcpCfg.Path, "ws")
			e.ginRouter.GET(wsPath, e.upgradeWS)
			slog.Debug("Registered MCP WebSocket handler", "method", "GET", "path", wsPath)
		}
	}
}

// writeJSONRPCResult writes a successful JSON-RPC response
func (e *Engine) writeJSONRPCResult(c *gin.Context, id json.RawMessage, result any) {
	resp := jsonrpcResponse{
		JSONRPC: "2.0",
		ID:      id,
		Result:  result,
	}

	// Log the complete JSON-RPC response for debugging
	formatAndPrintJSON("📤 Complete JSON-RPC Response", resp)

	c.JSON(http.StatusOK, resp)
}

// writeJSONRPCError writes a JSON-RPC error response
func (e *Engine) writeJSONRPCError(c *gin.Context, id json.RawMessage, code int, message string, statusCode int) {
	resp := jsonrpcResponse{
		JSONRPC: "2.0",
		ID:      id,
		Error: &jsonrpcError{
			Code:    code,
			Message: message,
		},
	}

	// Log the complete JSON-RPC error response for debugging
	formatAndPrintJSON("📤 Complete JSON-RPC Error Response", resp)

	c.JSON(statusCode, resp)
}

// formatResultAsText converts a result to a text representation for MCP content
func formatResultAsText(result any) string {
	if result == nil {
		return "null"
	}

	// If it's already a string, return it directly
	if str, ok := result.(string); ok {
		return str
	}

	// For other types, marshal to JSON for a readable representation
	jsonBytes, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Sprintf("%v", result)
	}
	return string(jsonBytes)
}

// generateToolName generates a tool name from a command/query type
func generateToolName(obj any) string {
	typeName := extractTypeName(obj)
	// Convert PascalCase to kebab-case and remove Command/Query suffix
	baseName := removeCommandQuerySuffix(typeName)
	return toKebabCase(baseName)
}

// toolNameMutex protects concurrent access to tool registration
var toolNameMutex sync.Mutex

// registerTool registers a command or query as an MCP tool
func (e *Engine) registerTool(name string, transport *MCP, handler any, dto any, isQuery bool) error {
	toolNameMutex.Lock()
	defer toolNameMutex.Unlock()

	// Check for duplicate tool names
	if _, exists := e.tools[name]; exists {
		return fmt.Errorf("tool with name '%s' already registered", name)
	}

	// Use InputSchema from transport if provided, otherwise generate from DTO
	var inputSchema any
	if transport.InputSchema != nil {
		inputSchema = transport.InputSchema
	} else {
		// Generate JSON Schema from DTO as fallback
		// For now, we'll use a placeholder
		// In production, use a library like github.com/alecthomas/jsonschema
		inputSchema = map[string]any{
			"type":                 "object",
			"additionalProperties": true,
		}
	}

	// Create tool definition
	tool := &ToolDef{
		Name:         name,
		Title:        transport.Title,
		Description:  transport.Description,
		InputSchema:  inputSchema,
		OutputSchema: transport.OutputSchema,
		Annotations:  transport.Annotations,
		Handler:      handler,
		IsQuery:      isQuery,
		DTOType:      reflect.TypeOf(dto),
	}

	e.tools[name] = tool
	slog.Debug("Registered MCP tool", "name", name)

	return nil
}
