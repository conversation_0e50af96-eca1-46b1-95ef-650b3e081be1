package cqrs_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
)

// TestDaprServiceInvocation tests actual Dapr service invocation
func TestDaprServiceInvocation(t *testing.T) {
	// TODO: fix this test
	t.Skip("Skipping integration test")

	if !isDaprAvailable() {
		t.Skip("Dapr CLI not available, skipping Dapr service invocation tests")
	}

	if testing.Short() {
		t.Skip("Skipping Dapr integration test in short mode")
	}

	t.Log("Testing actual Dapr service invocation")

	// Create a temporary directory for Dapr components
	tempDir, err := os.MkdirTemp("", "cqrs-dapr-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create minimal Dapr component files for testing
	err = createTestDaprComponents(tempDir)
	if err != nil {
		t.Fatalf("Failed to create Dapr components: %v", err)
	}

	// Create engine for Dapr testing
	engine := cqrs.NewEngine("dapr-test-service",
		cqrs.WithGRPC(9098),
		cqrs.WithREST(8088),
		cqrs.WithPubSub("test-pubsub", 9099),
	)

	// Register command for Dapr service invocation
	err = engine.RegisterCommands(cqrs.CommandConfig{
		Command: &TestCommand{},
		Handler: testCommandHandler,
		Transport: cqrs.Transport{
			GRPC: &cqrs.GRPC{Method: "ProcessCommand"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register command: %v", err)
	}

	// Register query for direct testing
	err = engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &TestQuery{},
		Handler: testQueryHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/api/test/:id"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register query: %v", err)
	}

	// Start the service
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	serviceErr := make(chan error, 1)
	go func() {
		if err := engine.Start(ctx); err != nil && err != context.Canceled {
			serviceErr <- err
		}
	}()

	// Wait for service to be ready
	if err := waitForService("http://localhost:8088/api/test/ready?id=ready", 15*time.Second); err != nil {
		t.Fatalf("Service not ready: %v", err)
	}

	// Start Dapr sidecar
	daprCmd := exec.CommandContext(ctx, "dapr", "run",
		"--app-id", "dapr-test-service",
		"--app-port", "9098",
		"--app-protocol", "grpc",
		"--dapr-http-port", "3502",
		"--dapr-grpc-port", "3503",
		"--components-path", tempDir,
		"--log-level", "warn",
		"--", "sleep", "30") // Keep Dapr running

	daprCmd.Stdout = os.Stdout
	daprCmd.Stderr = os.Stderr

	if err := daprCmd.Start(); err != nil {
		t.Fatalf("Failed to start Dapr: %v", err)
	}
	defer func() {
		if daprCmd.Process != nil {
			if err := daprCmd.Process.Kill(); err != nil {
				t.Logf("Failed to kill Dapr process: %v", err)
			}
		}
	}()

	// Wait for Dapr to be ready
	if err := waitForService("http://localhost:3502/v1.0/healthz", 30*time.Second); err != nil {
		t.Fatalf("Dapr not ready: %v", err)
	}

	t.Run("Dapr_Service_Invocation", func(t *testing.T) {
		// Test Dapr service invocation
		cmdData := TestCommand{ID: "dapr-test", Name: "Dapr Command"}
		jsonData, _ := json.Marshal(cmdData)

		// Invoke via Dapr HTTP API
		resp, err := http.Post(
			"http://localhost:3502/v1.0/invoke/dapr-test-service/method/ProcessCommand",
			"application/json",
			bytes.NewBuffer(jsonData),
		)
		if err != nil {
			t.Fatalf("Failed to invoke via Dapr: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		var result map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			t.Errorf("Failed to decode response: %v", err)
		}

		if result["status"] != "ok" {
			t.Errorf("Expected status 'ok', got %v", result["status"])
		}
	})

	t.Run("Direct_REST_Still_Works", func(t *testing.T) {
		// Ensure direct REST access still works alongside Dapr
		resp, err := http.Get("http://localhost:8088/api/test/direct-test?id=direct-test")
		if err != nil {
			t.Fatalf("Failed to access direct REST: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		var result TestResponse
		if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
			t.Errorf("Failed to decode response: %v", err)
		}

		if result.ID != "direct-test" {
			t.Errorf("Expected ID 'direct-test', got %s", result.ID)
		}
	})

	// Check for service errors
	select {
	case err := <-serviceErr:
		t.Fatalf("Service error: %v", err)
	default:
		// No error, test passed
	}
}

// TestDaprPubSub tests PubSub functionality with Dapr
func TestDaprPubSub(t *testing.T) {
	// TODO: fix this test
	t.Skip("Skipping integration test")

	if !isDaprAvailable() {
		t.Skip("Dapr CLI not available, skipping Dapr PubSub tests")
	}

	if testing.Short() {
		t.Skip("Skipping Dapr PubSub test in short mode")
	}

	t.Log("Testing Dapr PubSub integration")

	// Create temp directory for components
	tempDir, err := os.MkdirTemp("", "cqrs-pubsub-test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create PubSub components
	err = createTestDaprComponents(tempDir)
	if err != nil {
		t.Fatalf("Failed to create Dapr components: %v", err)
	}

	// Track received messages
	receivedMessages := make(chan TestCommand, 10)

	// Handler that captures messages
	pubsubHandler := func(ctx context.Context, cmd any) error {
		c := cmd.(*TestCommand)
		receivedMessages <- *c
		fmt.Printf("PubSub message received: ID=%s, Name=%s\n", c.ID, c.Name)
		return nil
	}

	// Create engine with PubSub
	engine := cqrs.NewEngine("pubsub-test-service",
		cqrs.WithPubSub("test-pubsub", 9100),
	)

	// Register PubSub command handler
	err = engine.RegisterCommands(cqrs.CommandConfig{
		Command: &TestCommand{},
		Handler: pubsubHandler,
		Transport: cqrs.Transport{
			PubSub: &cqrs.PubSub{Topic: "test-topic"},
		},
	})
	if err != nil {
		t.Fatalf("Failed to register PubSub command: %v", err)
	}

	// Start service
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		engine.Start(ctx) //nolint:errcheck // errors handled by context cancellation
	}()

	// Start Dapr for PubSub testing
	daprCmd := exec.CommandContext(ctx, "dapr", "run",
		"--app-id", "pubsub-test-service",
		"--app-port", "9100",
		"--app-protocol", "grpc",
		"--dapr-http-port", "3504",
		"--dapr-grpc-port", "3505",
		"--components-path", tempDir,
		"--log-level", "warn",
		"--", "sleep", "30")

	if err := daprCmd.Start(); err != nil {
		t.Fatalf("Failed to start Dapr for PubSub: %v", err)
	}
	defer func() {
		if daprCmd.Process != nil {
			if err := daprCmd.Process.Kill(); err != nil {
				t.Logf("Failed to kill Dapr process: %v", err)
			}
		}
	}()

	// Wait for Dapr to be ready
	if err := waitForService("http://localhost:3504/v1.0/healthz", 30*time.Second); err != nil {
		t.Fatalf("Dapr not ready for PubSub: %v", err)
	}

	// Give extra time for PubSub subscriptions to be established
	time.Sleep(5 * time.Second)

	t.Run("PubSub_Message_Delivery", func(t *testing.T) {
		// Publish a message via Dapr
		testMsg := TestCommand{ID: "pubsub-test", Name: "PubSub Message"}
		jsonData, _ := json.Marshal(testMsg)

		// Use dapr CLI to publish (more reliable than HTTP API for testing)
		publishCmd := exec.Command("dapr", "publish",
			"--publish-app-id", "pubsub-test-service",
			"--pubsub", "test-pubsub",
			"--topic", "test-topic",
			"--data", string(jsonData))

		output, err := publishCmd.CombinedOutput()
		if err != nil {
			t.Fatalf("Failed to publish message: %v, output: %s", err, output)
		}

		// Wait for message to be received
		select {
		case receivedMsg := <-receivedMessages:
			if receivedMsg.ID != testMsg.ID {
				t.Errorf("Expected ID %s, got %s", testMsg.ID, receivedMsg.ID)
			}
			if receivedMsg.Name != testMsg.Name {
				t.Errorf("Expected Name %s, got %s", testMsg.Name, receivedMsg.Name)
			}
		case <-time.After(10 * time.Second):
			t.Error("Timeout waiting for PubSub message")
		}
	})
}

// Helper function to create minimal Dapr components for testing
func createTestDaprComponents(dir string) error {
	// Create a simple in-memory PubSub component for testing
	pubsubComponent := `apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: test-pubsub
spec:
  type: pubsub.in-memory
  version: v1
  metadata: []
`

	err := os.WriteFile(filepath.Join(dir, "pubsub.yaml"), []byte(pubsubComponent), 0644)
	if err != nil {
		return fmt.Errorf("failed to write pubsub component: %w", err)
	}

	// Create a simple state store component
	stateComponent := `apiVersion: dapr.io/v1alpha1
kind: Component
metadata:
  name: statestore
spec:
  type: state.in-memory
  version: v1
  metadata: []
`

	err = os.WriteFile(filepath.Join(dir, "statestore.yaml"), []byte(stateComponent), 0644)
	if err != nil {
		return fmt.Errorf("failed to write state component: %w", err)
	}

	return nil
}

// TestEngineLifecycle tests the complete lifecycle of the engine
func TestEngineLifecycle(t *testing.T) {
	// TODO: fix this test
	t.Skip("Skipping integration test")

	if testing.Short() {
		t.Skip("Skipping Dapr integration test in short mode")
	}

	t.Log("Testing engine lifecycle management")

	// Test multiple start/stop cycles
	for i := 0; i < 3; i++ {
		t.Run(fmt.Sprintf("Cycle_%d", i+1), func(t *testing.T) {
			port := 8090 + i // Use different ports for each cycle

			engine := cqrs.NewEngine(fmt.Sprintf("lifecycle-test-%d", i),
				cqrs.WithREST(port),
			)

			err := engine.RegisterQueries(cqrs.QueryConfig{
				Query:   &TestQuery{},
				Handler: testQueryHandler,
				Transport: cqrs.Transport{
					REST: &cqrs.REST{Method: "GET", Path: "/lifecycle/:id"},
				},
			})
			if err != nil {
				t.Fatalf("Failed to register query: %v", err)
			}

			// Start engine
			ctx, cancel := context.WithCancel(context.Background())

			engineErr := make(chan error, 1)
			go func() {
				if err := engine.Start(ctx); err != nil && err != context.Canceled {
					engineErr <- err
				}
			}()

			// Wait for service to be ready
			testURL := fmt.Sprintf("http://localhost:%d/lifecycle/test?id=test", port)
			if err := waitForService(testURL, 10*time.Second); err != nil {
				t.Fatalf("Service not ready: %v", err)
			}

			// Test service is working
			resp, err := http.Get(testURL)
			if err != nil {
				t.Fatalf("Service not responding: %v", err)
			}
			resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf("Expected status 200, got %d", resp.StatusCode)
			}

			// Shutdown gracefully
			cancel()

			// Wait for shutdown
			time.Sleep(500 * time.Millisecond)

			// Verify service is stopped
			client := &http.Client{Timeout: 1 * time.Second}
			_, err = client.Get(testURL)
			if err == nil {
				t.Error("Expected service to be stopped, but it's still responding")
			}

			// Check for engine errors
			select {
			case err := <-engineErr:
				if err != context.Canceled {
					t.Errorf("Unexpected engine error: %v", err)
				}
			case <-time.After(2 * time.Second):
				// Engine should have stopped by now
			}
		})
	}
}

// Benchmark tests for performance
func BenchmarkRESTEndpoint(b *testing.B) {
	engine := cqrs.NewEngine("bench-service", cqrs.WithREST(8095))

	err := engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &TestQuery{},
		Handler: testQueryHandler,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/bench/:id"},
		},
	})
	if err != nil {
		b.Fatalf("Failed to register query: %v", err)
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	go func() {
		engine.Start(ctx) //nolint:errcheck // errors handled by context cancellation
	}()

	// Wait for service to be ready
	if err := waitForService("http://localhost:8095/bench/test?id=test", 10*time.Second); err != nil {
		b.Fatalf("Service not ready: %v", err)
	}

	b.ResetTimer()

	client := &http.Client{Timeout: 5 * time.Second}

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			resp, err := client.Get("http://localhost:8095/bench/test?id=test")
			if err != nil {
				b.Errorf("Request failed: %v", err)
				continue
			}
			resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				b.Errorf("Expected status 200, got %d", resp.StatusCode)
			}
		}
	})
}
