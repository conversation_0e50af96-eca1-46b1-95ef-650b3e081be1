package cqrs

import (
	"context"
	"fmt"
)

// CommandConfig defines the configuration for a command handler
type CommandConfig struct {
	Command   any                                      // pointer to DTO struct
	Handler   func(ctx context.Context, cmd any) error // command handler function
	Transport Transport                                // transport configuration
	Meta      map[string]interface{}                   // optional free-form metadata
}

// QueryConfig defines the configuration for a query handler
type QueryConfig struct {
	Query     any                                           // pointer to DTO struct
	Handler   func(ctx context.Context, q any) (any, error) // query handler function
	Transport Transport                                     // transport configuration
	Meta      map[string]interface{}                        // optional free-form metadata
}

// RegisterCommands registers one or more command configurations with the engine
func (e *Engine) RegisterCommands(configs ...CommandConfig) error {
	for i, config := range configs {
		if err := e.validateAndRegisterCommand(config, i); err != nil {
			return fmt.Errorf("failed to register command at index %d: %w", i, err)
		}
	}
	return nil
}

// RegisterQueries registers one or more query configurations with the engine
func (e *Engine) RegisterQueries(configs ...QueryConfig) error {
	for i, config := range configs {
		if err := e.validateAndRegisterQuery(config, i); err != nil {
			return fmt.Errorf("failed to register query at index %d: %w", i, err)
		}
	}
	return nil
}

// validateAndRegisterCommand validates a command configuration and adds it to the engine
func (e *Engine) validateAndRegisterCommand(config CommandConfig, index int) error {
	// Validate command is not nil
	if config.Command == nil {
		return fmt.Errorf("command cannot be nil")
	}

	// Validate handler is not nil
	if config.Handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}

	// Validate transport configuration (strict validation)
	if err := validateTransport(config.Transport, config.Command, false); err != nil {
		return fmt.Errorf("invalid transport configuration: %w", err)
	}

	// Apply defaults after validation (for convenience)
	applyTransportDefaults(&config.Transport, config.Command, false)

	// Validate that required services are configured
	if err := e.validateRequiredServices(config.Transport); err != nil {
		return fmt.Errorf("missing required service configuration: %w", err)
	}

	// Add to commands list
	e.commands = append(e.commands, config)

	return nil
}

// validateAndRegisterQuery validates a query configuration and adds it to the engine
func (e *Engine) validateAndRegisterQuery(config QueryConfig, index int) error {
	// Validate query is not nil
	if config.Query == nil {
		return fmt.Errorf("query cannot be nil")
	}

	// Validate handler is not nil
	if config.Handler == nil {
		return fmt.Errorf("handler cannot be nil")
	}

	// Validate transport configuration (queries have additional restrictions)
	if err := validateTransport(config.Transport, config.Query, true); err != nil {
		return fmt.Errorf("invalid transport configuration: %w", err)
	}

	// Apply defaults after validation (for convenience)
	applyTransportDefaults(&config.Transport, config.Query, true)

	// Validate that required services are configured
	if err := e.validateRequiredServices(config.Transport); err != nil {
		return fmt.Errorf("missing required service configuration: %w", err)
	}

	// Add to queries list
	e.queries = append(e.queries, config)

	return nil
}

// validateRequiredServices ensures that the engine is configured with the necessary services
// for the specified transport configuration
func (e *Engine) validateRequiredServices(transport Transport) error {
	// Check gRPC service requirement
	if transport.hasGRPC() && e.grpcPort <= 0 {
		return fmt.Errorf("gRPC transport specified but engine not configured with WithGRPC")
	}

	// Check REST service requirement
	if transport.hasREST() && e.restPort <= 0 {
		return fmt.Errorf("REST transport specified but engine not configured with WithREST")
	}

	// Check PubSub service requirement
	if transport.hasPubSub() && (e.subPort <= 0 || e.pubsubName == "") {
		return fmt.Errorf("PubSub transport specified but engine not configured with WithGRPCSubscriber")
	}

	// Check MCP service requirement
	if transport.hasMCP() && e.mcpCfg == nil {
		return fmt.Errorf("MCP transport specified but engine not configured with WithMCP")
	}

	return nil
}

// GetRegisteredCommands returns a copy of all registered commands (for debugging/inspection)
func (e *Engine) GetRegisteredCommands() []CommandConfig {
	commands := make([]CommandConfig, len(e.commands))
	copy(commands, e.commands)
	return commands
}

// GetRegisteredQueries returns a copy of all registered queries (for debugging/inspection)
func (e *Engine) GetRegisteredQueries() []QueryConfig {
	queries := make([]QueryConfig, len(e.queries))
	copy(queries, e.queries)
	return queries
}

// Summary returns a summary of the engine configuration
func (e *Engine) Summary() string {
	summary := fmt.Sprintf("Engine: %s\n", e.appID)
	summary += fmt.Sprintf("  gRPC Port: %d\n", e.grpcPort)
	summary += fmt.Sprintf("  REST Port: %d\n", e.restPort)
	summary += fmt.Sprintf("  Subscriber Port: %d\n", e.subPort)
	summary += fmt.Sprintf("  PubSub Component: %s\n", e.pubsubName)
	summary += fmt.Sprintf("  Commands: %d\n", len(e.commands))
	summary += fmt.Sprintf("  Queries: %d\n", len(e.queries))
	return summary
}
