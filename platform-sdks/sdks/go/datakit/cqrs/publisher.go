package cqrs

import (
	"context"
	"fmt"

	"github.com/dapr/go-sdk/client"
)

// DaprPublisher interface for publishing events (allows for easier testing)
type DaprPublisher interface {
	PublishEvent(ctx context.Context, pubsubName, topicName string, data interface{}, opts ...client.PublishEventOption) error
	Close()
}

// Publisher provides event publishing capabilities using Dapr
type Publisher struct {
	client     DaprPublisher
	pubsubName string
}

// NewPublisher creates a new publisher instance using the Dapr Go SDK client
// pubsubName: the name of the Dapr pubsub component
// daprGRPCPort: the Dapr gRPC port (default: 50001)
func NewPublisher(pubsubName string, daprGRPCPort int) (*Publisher, error) {
	if daprGRPCPort <= 0 {
		daprGRPCPort = 50001 // Default Dapr gRPC port
	}

	// Create Dapr client
	daprClient, err := client.NewClientWithPort(fmt.Sprintf("%d", daprGRPCPort))
	if err != nil {
		return nil, fmt.Errorf("failed to create Dapr client: %w", err)
	}

	return &Publisher{
		client:     daprClient,
		pubsubName: pubsubName,
	}, nil
}

// NewPublisherWithAddress creates a new publisher with a custom Dapr address
// pubsubName: the name of the Dapr pubsub component
// daprAddress: the full Dapr gRPC address (e.g., "localhost:50001")
func NewPublisherWithAddress(pubsubName, daprAddress string) (*Publisher, error) {
	if daprAddress == "" {
		daprAddress = "localhost:50001" // Default Dapr gRPC address
	}

	// Create Dapr client with custom address
	daprClient, err := client.NewClientWithAddress(daprAddress)
	if err != nil {
		return nil, fmt.Errorf("failed to create Dapr client: %w", err)
	}

	return &Publisher{
		client:     daprClient,
		pubsubName: pubsubName,
	}, nil
}

// Event is the interface all events must implement
type Event interface {
	Type() string
}

// PublishEvent publishes an event to the specified topic
// ctx: context for the operation
// topic: the topic name to publish to
// event: the event data to publish (will be JSON marshaled)
func (p *Publisher) PublishEvent(ctx context.Context, topic string, event interface{}) error {
	if ctx == nil {
		ctx = context.Background()
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	if event == nil {
		return fmt.Errorf("event cannot be nil")
	}

	// Publish event using Dapr client
	if err := p.client.PublishEvent(ctx, p.pubsubName, topic, event); err != nil {
		return fmt.Errorf("failed to publish event to topic %s: %w", topic, err)
	}

	return nil
}

// PublishEventWithMetadata publishes an event with custom metadata
// ctx: context for the operation
// topic: the topic name to publish to
// event: the event data to publish
// metadata: custom metadata to include with the event
func (p *Publisher) PublishEventWithMetadata(ctx context.Context, topic string, event interface{}, metadata map[string]string) error {
	if ctx == nil {
		ctx = context.Background()
	}

	if topic == "" {
		return fmt.Errorf("topic cannot be empty")
	}

	if event == nil {
		return fmt.Errorf("event cannot be nil")
	}

	// Import the client package to use PublishEventOption
	opts := []client.PublishEventOption{}
	if metadata != nil {
		opts = append(opts, client.PublishEventWithMetadata(metadata))
	}

	// Publish event with metadata using Dapr client
	if err := p.client.PublishEvent(ctx, p.pubsubName, topic, event, opts...); err != nil {
		return fmt.Errorf("failed to publish event with metadata to topic %s: %w", topic, err)
	}

	return nil
}

// PublishEventWithType publishes an event with automatic type metadata
// ctx: context for the operation
// topic: the topic name to publish to
// eventType: the event type (e.g., "user.created", "user.updated")
// event: the event data to publish
// additionalMetadata: optional additional metadata to include
func (p *Publisher) PublishEventWithType(ctx context.Context, topic string, eventType string, event interface{}, additionalMetadata map[string]string) error {
	if eventType == "" {
		return fmt.Errorf("eventType cannot be empty")
	}

	// Create metadata with event type
	metadata := map[string]string{
		"type": eventType,
	}

	// Add any additional metadata
	for k, v := range additionalMetadata {
		metadata[k] = v
	}

	return p.PublishEventWithMetadata(ctx, topic, event, metadata)
}

// PublishTypedEvent publishes an event that implements the Event interface
// The event type is automatically extracted from the event's Type() method
// ctx: context for the operation
// topic: the topic name to publish to
// event: the event that implements Event interface
// additionalMetadata: optional additional metadata to include
func (p *Publisher) PublishTypedEvent(ctx context.Context, topic string, event Event, additionalMetadata map[string]string) error {
	if event == nil {
		return fmt.Errorf("event cannot be nil")
	}

	eventType := event.Type()
	if eventType == "" {
		return fmt.Errorf("event type cannot be empty")
	}

	return p.PublishEventWithType(ctx, topic, eventType, event, additionalMetadata)
}

// Close closes the Dapr client connection
func (p *Publisher) Close() error {
	if p.client != nil {
		p.client.Close()
	}
	return nil
}

// GetPubSubName returns the configured pubsub component name
func (p *Publisher) GetPubSubName() string {
	return p.pubsubName
}
