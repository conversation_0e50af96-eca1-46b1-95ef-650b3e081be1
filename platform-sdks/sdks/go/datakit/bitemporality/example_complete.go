package bitemporality

import (
	"context"
	"fmt"
	"time"
)

type ExampleEntity struct {
	ID              string
	Version         int64
	ValidStartDate  *time.Time
	ValidEndDate    *time.Time
	SystemStartDate *time.Time
	SystemEndDate   *time.Time
	Name            string
}

func (e *ExampleEntity) GetID() string                   { return e.ID }
func (e *ExampleEntity) SetID(id string)                 { e.ID = id }
func (e *ExampleEntity) GetVersion() int64               { return e.Version }
func (e *ExampleEntity) SetVersion(v int64)              { e.Version = v }
func (e *ExampleEntity) GetValidStartDate() *time.Time   { return e.ValidStartDate }
func (e *ExampleEntity) SetValidStartDate(t *time.Time)  { e.ValidStartDate = t }
func (e *ExampleEntity) GetValidEndDate() *time.Time     { return e.ValidEndDate }
func (e *ExampleEntity) SetValidEndDate(t *time.Time)    { e.ValidEndDate = t }
func (e *ExampleEntity) GetSystemStartDate() *time.Time  { return e.SystemStartDate }
func (e *ExampleEntity) SetSystemStartDate(t *time.Time) { e.SystemStartDate = t }
func (e *ExampleEntity) GetSystemEndDate() *time.Time    { return e.SystemEndDate }
func (e *ExampleEntity) SetSystemEndDate(t *time.Time)   { e.SystemEndDate = t }

type InMemoryRepo struct {
	entities   map[string]*ExampleEntity
	historical map[string][]*ExampleEntity
}

func NewInMemoryRepo() *InMemoryRepo {
	return &InMemoryRepo{
		entities:   make(map[string]*ExampleEntity),
		historical: make(map[string][]*ExampleEntity),
	}
}

func (r *InMemoryRepo) Transactional(ctx context.Context, fn func(ctxWithTx context.Context) error) error {
	return fn(ctx)
}
func (r *InMemoryRepo) FindByID(ctx context.Context, tableName, id string) (*ExampleEntity, error) {
	e, ok := r.entities[id]
	if !ok {
		return nil, fmt.Errorf("not found")
	}
	return e, nil
}
func (r *InMemoryRepo) Insert(ctx context.Context, tableName string, entity *ExampleEntity) (*ExampleEntity, error) {
	if tableName == "entities_historical" {
		r.historical[entity.ID] = append(r.historical[entity.ID], cloneEntity(entity))
		return entity, nil
	}
	r.entities[entity.ID] = cloneEntity(entity)
	return entity, nil
}
func (r *InMemoryRepo) Update(ctx context.Context, tableName string, entity *ExampleEntity) (*ExampleEntity, error) {
	r.entities[entity.ID] = cloneEntity(entity)
	return entity, nil
}
func (r *InMemoryRepo) DeleteByID(ctx context.Context, tableName, id string) (bool, error) {
	delete(r.entities, id)
	return true, nil
}
func (r *InMemoryRepo) ListAll(ctx context.Context, tableName string) ([]*ExampleEntity, error) {
	var all []*ExampleEntity
	for _, e := range r.entities {
		all = append(all, e)
	}
	return all, nil
}

func cloneEntity(e *ExampleEntity) *ExampleEntity {
	copy := *e
	return &copy
}

func printEntity(prefix string, e *ExampleEntity) {
	fmt.Printf("%s: ID=%s, Name=%s, Version=%d, ValidStart=%v, ValidEnd=%v, SystemStart=%v, SystemEnd=%v\n",
		prefix, e.ID, e.Name, e.Version, e.ValidStartDate, e.ValidEndDate, e.SystemStartDate, e.SystemEndDate)
}

func printHistory(repo *InMemoryRepo, id string) {
	fmt.Printf("History for ID=%s:\n", id)
	for _, e := range repo.historical[id] {
		printEntity("  Historical", e)
	}
}

func Example() {
	repo := NewInMemoryRepo()
	config := NewConfig()
	mgr := NewManager[*ExampleEntity](repo, config)
	ctx := context.Background()

	// Example 1: Regular lifecycle with immediate deletion
	fmt.Println("\nExample 1: Regular lifecycle with immediate deletion")
	fmt.Println("--------------------------------------------------")

	// INSERT
	entity1 := &ExampleEntity{ID: "1", Name: "Alice"}
	inserted1, _ := mgr.Insert(ctx, "entities", entity1)
	printEntity("Inserted", inserted1)

	// UPDATE
	inserted1.Name = "Alice Updated"
	updated1, _ := mgr.Update(ctx, "entities", inserted1)
	printEntity("Updated", updated1)
	printHistory(repo, "1")

	// DELETE (immediate deletion - valid end date = system end date = now)
	mgr.Delete(ctx, "entities", "1")
	fmt.Println("After immediate deletion:")
	printHistory(repo, "1")

	// Example 2: Lifecycle with future valid end date
	fmt.Println("\nExample 2: Lifecycle with future valid end date")
	fmt.Println("---------------------------------------------")

	// INSERT
	entity2 := &ExampleEntity{ID: "2", Name: "Bob"}
	inserted2, _ := mgr.Insert(ctx, "entities", entity2)
	printEntity("Inserted", inserted2)

	// UPDATE
	inserted2.Name = "Bob Updated"
	updated2, _ := mgr.Update(ctx, "entities", inserted2)
	printEntity("Updated", updated2)
	printHistory(repo, "2")

	// DELETE with future valid end date (entity remains valid until this date)
	futureEndDate := time.Now().Add(24 * time.Hour) // Valid until tomorrow
	mgr.Delete(ctx, "entities", "2", futureEndDate)
	fmt.Println("After deletion with future valid end date:")
	printHistory(repo, "2")

	// Example 3: Error case - multiple valid end dates
	fmt.Println("\nExample 3: Error case - multiple valid end dates")
	fmt.Println("---------------------------------------------")

	// INSERT
	entity3 := &ExampleEntity{ID: "3", Name: "Charlie"}
	inserted3, _ := mgr.Insert(ctx, "entities", entity3)
	printEntity("Inserted", inserted3)

	// Try to DELETE with multiple end dates (will return error)
	endDate1 := time.Now().Add(24 * time.Hour)
	endDate2 := time.Now().Add(48 * time.Hour)
	if _, err := mgr.Delete(ctx, "entities", "3", endDate1, endDate2); err != nil {
		fmt.Printf("Error deleting with multiple end dates: %v\n", err)
	}
}
