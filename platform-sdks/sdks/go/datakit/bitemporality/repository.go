package bitemporality

import (
	"context"
)

// Repository provides a generic interface for persistence repositories.
type Repository[T any] interface {
	// Transactional runs a function in a transaction.
	Transactional(ctx context.Context, fn func(ctxWithTx context.Context) error) error

	FindByID(ctx context.Context, tableName string, id string) (T, error)

	Insert(ctx context.Context, tableName string, entity T) (T, error)

	Update(ctx context.Context, tableName string, entity T) (T, error)

	// DeleteByID deletes an entity row by its table name and id.
	// If a ValidEndDate is provided, its bitemporal validity is set to that date.
	// If no ValidEndDate is provided, the bitemporal validity is set to now.
	DeleteByID(ctx context.Context, tableName string, id string) (bool, error)

	ListAll(ctx context.Context, tableName string) ([]T, error)
}
