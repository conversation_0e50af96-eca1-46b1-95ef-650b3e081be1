// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -destination=repository_mock.go -package=bitemporality github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality Repository
//

// Package bitemporality is a generated GoMock package.
package bitemporality

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository[T any] struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder[T]
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder[T any] struct {
	mock *MockRepository[T]
}

// NewMockRepository creates a new mock instance.
func NewMockRepository[T any](ctrl *gomock.Controller) *MockRepository[T] {
	mock := &MockRepository[T]{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder[T]{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository[T]) EXPECT() *MockRepositoryMockRecorder[T] {
	return m.recorder
}

// DeleteByID mocks base method.
func (m *MockRepository[T]) DeleteByID(ctx context.Context, tableName, id string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByID", ctx, tableName, id)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteByID indicates an expected call of DeleteByID.
func (mr *MockRepositoryMockRecorder[T]) DeleteByID(ctx, tableName, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByID", reflect.TypeOf((*MockRepository[T])(nil).DeleteByID), ctx, tableName, id)
}

// FindByID mocks base method.
func (m *MockRepository[T]) FindByID(ctx context.Context, tableName, id string) (T, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, tableName, id)
	ret0, _ := ret[0].(T)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockRepositoryMockRecorder[T]) FindByID(ctx, tableName, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockRepository[T])(nil).FindByID), ctx, tableName, id)
}

// Insert mocks base method.
func (m *MockRepository[T]) Insert(ctx context.Context, tableName string, entity T) (T, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, tableName, entity)
	ret0, _ := ret[0].(T)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockRepositoryMockRecorder[T]) Insert(ctx, tableName, entity any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockRepository[T])(nil).Insert), ctx, tableName, entity)
}

// ListAll mocks base method.
func (m *MockRepository[T]) ListAll(ctx context.Context, tableName string) ([]T, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAll", ctx, tableName)
	ret0, _ := ret[0].([]T)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAll indicates an expected call of ListAll.
func (mr *MockRepositoryMockRecorder[T]) ListAll(ctx, tableName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAll", reflect.TypeOf((*MockRepository[T])(nil).ListAll), ctx, tableName)
}

// Transactional mocks base method.
func (m *MockRepository[T]) Transactional(ctx context.Context, fn func(context.Context) error) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Transactional", ctx, fn)
	ret0, _ := ret[0].(error)
	return ret0
}

// Transactional indicates an expected call of Transactional.
func (mr *MockRepositoryMockRecorder[T]) Transactional(ctx, fn any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Transactional", reflect.TypeOf((*MockRepository[T])(nil).Transactional), ctx, fn)
}

// Update mocks base method.
func (m *MockRepository[T]) Update(ctx context.Context, tableName string, entity T) (T, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, tableName, entity)
	ret0, _ := ret[0].(T)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRepositoryMockRecorder[T]) Update(ctx, tableName, entity any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRepository[T])(nil).Update), ctx, tableName, entity)
}
