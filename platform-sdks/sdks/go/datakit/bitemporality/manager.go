package bitemporality

import (
	"context"
	"fmt"
	"time"
)

const (
	historicalTableNameSuffix = "_historical"
)

// Manager is a manager for bitemporal entities. It provides
// methods to insert, update, and delete entities based on the bitemporal model.
// It is responsible for setting the bitemporal dates & version for the entities and
// delegating to the repository to perform the actual database operations.
type Manager[T Entity] struct {
	repository Repository[T]
	config     *Config
}

// NewManager creates a new Manager for the given repository.
func NewManager[T Entity](repo Repository[T], config *Config) *Manager[T] {
	return &Manager[T]{repository: repo, config: config}
}

// Insert adds a new bitemporal entity, setting the appropriate dates and version.
func (m *Manager[T]) Insert(ctx context.Context, table string, entity T) (T, error) {
	m.setBitemporalDates(entity)
	if entity.GetVersion() == 0 {
		entity.SetVersion(1)
	}
	return m.repository.Insert(ctx, table, entity)
}

// Update updates an entity by copying the current state into a historical table and updating the main table.
func (m *Manager[T]) Update(ctx context.Context, table string, entity T) (T, error) {
	var result T
	err := m.repository.Transactional(ctx, func(txCtx context.Context) error {
		current, err := m.repository.FindByID(txCtx, table, entity.GetID())
		if err != nil {
			return err
		}
		if err := m.closeBitemporalEntity(current); err != nil {
			return err
		}

		m.setBitemporalDates(entity)
		entity.SetVersion(current.GetVersion() + 1)

		if m.config.StorageStrategy == SameTableStrategy {
			// Update the old record to mark it as historical
			if _, err := m.repository.Update(txCtx, table, current); err != nil {
				return err
			}
			// Insert the new version as a new record
			result, err = m.repository.Insert(txCtx, table, entity)
			return err
		}

		// Default to SeparateTableStrategy
		historicalTableName := m.getHistoricalTableName(table)
		if _, err := m.repository.Insert(txCtx, historicalTableName, current); err != nil {
			return err
		}
		result, err = m.repository.Update(txCtx, table, entity)
		return err
	})
	if err != nil {
		return result, err
	}
	return result, nil
}

// Delete deletes an entity by copying the current state into a historical table and deleting from the main table.
func (m *Manager[T]) Delete(ctx context.Context, table string, id string, validEndDate ...time.Time) (bool, error) {
	var deleted bool
	err := m.repository.Transactional(ctx, func(txCtx context.Context) error {
		entity, err := m.repository.FindByID(txCtx, table, id)
		if err != nil {
			return err
		}
		if err := m.closeBitemporalEntity(entity, validEndDate...); err != nil {
			return err
		}

		if m.config.StorageStrategy == SameTableStrategy {
			// In same-table strategy, "deleting" means marking the current record as historical
			if _, err := m.repository.Update(txCtx, table, entity); err != nil {
				return err
			}
			deleted = true
			return nil
		}

		// Default to SeparateTableStrategy
		historicalTableName := m.getHistoricalTableName(table)
		if _, err := m.repository.Insert(txCtx, historicalTableName, entity); err != nil {
			return err
		}
		deleted, err = m.repository.DeleteByID(txCtx, table, id)
		return err
	})
	if err != nil {
		return false, err
	}
	return deleted, nil
}

// setBitemporalDates sets the bitemporal dates for an entity.
func (m *Manager[T]) setBitemporalDates(entity T) {
	now := time.Now()
	entity.SetSystemStartDate(&now)
	entity.SetSystemEndDate(nil)
	if entity.GetValidStartDate() == nil {
		entity.SetValidStartDate(&now)
	}
	entity.SetValidEndDate(nil)
}

// closeBitemporalEntity sets the end dates for an entity, optionally using a provided validEndDate.
func (m *Manager[T]) closeBitemporalEntity(entity T, validEndDate ...time.Time) error {
	now := time.Now()
	actualValidEndDate := now
	if len(validEndDate) == 1 {
		actualValidEndDate = validEndDate[0]
	} else if len(validEndDate) > 1 {
		return fmt.Errorf("only one valid end date can be provided")
	}
	entity.SetSystemEndDate(&now)
	entity.SetValidEndDate(&actualValidEndDate)
	return nil
}

// Repository returns the underlying repository used by the manager.
func (m *Manager[T]) Repository() Repository[T] {
	return m.repository
}

func (m *Manager[T]) getHistoricalTableName(table string) string {
	if m.config.StorageStrategy == SeparateTableStrategy {
		return table + historicalTableNameSuffix
	}
	return table
}
