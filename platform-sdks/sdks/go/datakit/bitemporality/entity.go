package bitemporality

import (
	"time"
)

// Entity defines the interface for all bitemporal entities
// with four dates to track both valid time (real-life) and transaction time (system)
type Entity interface {
	GetID() string
	SetID(string)

	GetVersion() int64
	SetVersion(int64)

	GetValidStartDate() *time.Time
	SetValidStartDate(*time.Time)

	GetValidEndDate() *time.Time
	SetValidEndDate(*time.Time)

	GetSystemStartDate() *time.Time
	SetSystemStartDate(*time.Time)

	GetSystemEndDate() *time.Time
	SetSystemEndDate(*time.Time)

	// Utility functions such as IsActive, IsActiveAt, and IsValidAt are available in utils.go
	// and can be used for common bitemporal logic on any Entity implementation.
}
