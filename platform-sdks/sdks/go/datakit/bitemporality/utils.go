package bitemporality

import "time"

// IsActive returns true if the entity is currently active in the system (system_end_date is nil)
func IsActive(entity Entity) bool {
	return entity.GetSystemEndDate() == nil
}

// IsActiveAt returns true if the entity was active in the system at the given time
func IsActiveAt(entity Entity, t time.Time) bool {
	if entity.GetSystemStartDate() != nil && t.Before(*entity.GetSystemStartDate()) {
		return false
	}
	if entity.GetSystemEndDate() != nil && t.After(*entity.GetSystemEndDate()) {
		return false
	}
	return true
}

// IsValidAt returns true if the entity was valid in real life at the given time
func IsValidAt(entity Entity, t time.Time) bool {
	if entity.GetValidStartDate() != nil && t.Before(*entity.GetValidStartDate()) {
		return false
	}
	if entity.GetValidEndDate() != nil && t.After(*entity.GetValidEndDate()) {
		return false
	}
	return true
}
