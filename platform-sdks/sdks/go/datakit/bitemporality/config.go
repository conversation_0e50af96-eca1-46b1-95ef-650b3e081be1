package bitemporality

// StorageStrategy defines the type of storage for bitemporal data.
type StorageStrategy string

const (
	// SameTableStrategy stores historical data in the same table as the current data.
	SameTableStrategy StorageStrategy = "same_table"
	// SeparateTableStrategy stores historical data in a separate table.
	SeparateTableStrategy StorageStrategy = "separate_table"
)

// Config holds the configuration for the bitemporal manager.
type Config struct {
	StorageStrategy StorageStrategy
}

// NewConfig creates a new Config with default values.
func NewConfig() *Config {
	return &Config{
		StorageStrategy: SameTableStrategy,
	}
}

// WithStorageStrategy sets the storage strategy for the configuration.
func (c *Config) WithStorageStrategy(strategy StorageStrategy) *Config {
	c.StorageStrategy = strategy
	return c
}
