package bitemporality

import (
	"context"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	gomock "go.uber.org/mock/gomock"
)

type testEntity struct {
	id              string
	version         int64
	validStartDate  *time.Time
	validEndDate    *time.Time
	systemStartDate *time.Time
	systemEndDate   *time.Time
}

func (e *testEntity) GetID() string                   { return e.id }
func (e *testEntity) SetID(id string)                 { e.id = id }
func (e *testEntity) GetVersion() int64               { return e.version }
func (e *testEntity) SetVersion(v int64)              { e.version = v }
func (e *testEntity) GetValidStartDate() *time.Time   { return e.validStartDate }
func (e *testEntity) SetValidStartDate(t *time.Time)  { e.validStartDate = t }
func (e *testEntity) GetValidEndDate() *time.Time     { return e.validEndDate }
func (e *testEntity) SetValidEndDate(t *time.Time)    { e.validEndDate = t }
func (e *testEntity) GetSystemStartDate() *time.Time  { return e.systemStartDate }
func (e *testEntity) SetSystemStartDate(t *time.Time) { e.systemStartDate = t }
func (e *testEntity) GetSystemEndDate() *time.Time    { return e.systemEndDate }
func (e *testEntity) SetSystemEndDate(t *time.Time)   { e.systemEndDate = t }

var _ = Describe("Manager", func() {
	var (
		ctrl               *gomock.Controller
		mockRepoHistorical *MockRepository[*testEntity]
		mockRepoSame       *MockRepository[*testEntity]
		mgrHistorical      *Manager[*testEntity]
		mgrSame            *Manager[*testEntity]
		ctx                context.Context
	)

	BeforeEach(func() {
		ctrl = gomock.NewController(GinkgoT())
		mockRepoHistorical = NewMockRepository[*testEntity](ctrl)
		mockRepoSame = NewMockRepository[*testEntity](ctrl)
		ctx = context.Background()
		mgrHistorical = NewManager(mockRepoHistorical, NewConfig().WithStorageStrategy(SeparateTableStrategy))
		mgrSame = NewManager(mockRepoSame, NewConfig().WithStorageStrategy(SameTableStrategy))
	})

	AfterEach(func() {
		ctrl.Finish()
	})

	Describe("Insert", func() {
		Context("when inserting a new entity", func() {
			It("should set bitemporal dates and version", func() {
				// ARRANGE
				entity := &testEntity{id: "1"}
				mockRepoHistorical.EXPECT().Insert(gomock.Any(), "test", gomock.Any()).Return(entity, nil).Times(1)
				// ACT
				inserted, err := mgrHistorical.Insert(ctx, "test", entity)
				// ASSERT
				Expect(err).To(BeNil())
				Expect(inserted.GetSystemStartDate()).NotTo(BeNil())
				Expect(inserted.GetSystemEndDate()).To(BeNil())
				Expect(inserted.GetValidStartDate()).NotTo(BeNil())
				Expect(inserted.GetValidEndDate()).To(BeNil())
				Expect(inserted.GetVersion()).To(Equal(int64(1)))
			})
		})
	})

	Describe("Update", func() {
		Context("with SeparateTableStrategy", func() {
			It("should archive the previous version with end dates and update the entity", func() {
				// ARRANGE
				start := time.Now().Add(-time.Hour)
				entity := &testEntity{id: "1", version: 1, validStartDate: &start, systemStartDate: &start}
				mockRepoHistorical.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoHistorical.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(entity, nil).Times(1)
				mockRepoHistorical.EXPECT().Insert(gomock.Any(), "test_historical", gomock.Any()).Return(entity, nil).Times(1)
				mockRepoHistorical.EXPECT().Update(gomock.Any(), "test", gomock.Any()).Return(entity, nil).Times(1)
				// ACT
				updated, err := mgrHistorical.Update(ctx, "test", entity)
				// ASSERT
				Expect(err).To(BeNil())
				Expect(updated.GetSystemStartDate()).NotTo(BeNil())
				Expect(updated.GetSystemEndDate()).To(BeNil())
				Expect(updated.GetValidStartDate()).NotTo(BeNil())
				Expect(updated.GetValidEndDate()).To(BeNil())
				Expect(updated.GetVersion()).To(Equal(int64(2)))
			})
		})

		Context("with SameTableStrategy", func() {
			It("should insert the historical record in the same table", func() {
				// ARRANGE
				start := time.Now().Add(-time.Hour)
				entity := &testEntity{id: "1", version: 1, validStartDate: &start, systemStartDate: &start}
				mockRepoSame.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoSame.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(entity, nil).Times(1)
				mockRepoSame.EXPECT().Insert(gomock.Any(), "test", gomock.Any()).Return(entity, nil).Times(1)
				mockRepoSame.EXPECT().Update(gomock.Any(), "test", gomock.Any()).Return(entity, nil).Times(1)
				// ACT
				updated, err := mgrSame.Update(ctx, "test", entity)
				// ASSERT
				Expect(err).To(BeNil())
				Expect(updated.GetVersion()).To(Equal(int64(2)))
			})
		})

		Context("when FindByID fails", func() {
			It("should return an error", func() {
				entity := &testEntity{id: "1"}
				mockRepoHistorical.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoHistorical.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(nil, context.DeadlineExceeded).Times(1)
				// ACT
				_, err := mgrHistorical.Update(ctx, "test", entity)
				// ASSERT
				Expect(err).To(HaveOccurred())
			})
		})
	})

	Describe("Delete", func() {
		Context("when deleting an entity with SeparateTableStrategy", func() {
			It("should archive the entity with end dates and delete it", func() {
				// ARRANGE
				start := time.Now().Add(-time.Hour)
				entity := &testEntity{id: "1", version: 1, validStartDate: &start, systemStartDate: &start}
				mockRepoHistorical.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoHistorical.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(entity, nil).Times(1)
				mockRepoHistorical.EXPECT().Insert(gomock.Any(), "test_historical", gomock.Any()).Return(entity, nil).Times(1)
				mockRepoHistorical.EXPECT().DeleteByID(gomock.Any(), "test", entity.id).Return(true, nil).Times(1)
				// ACT
				deleted, err := mgrHistorical.Delete(ctx, "test", entity.id)
				// ASSERT
				Expect(err).To(BeNil())
				Expect(deleted).To(BeTrue())
			})

			It("should archive the entity with custom valid end date when provided", func() {
				// ARRANGE
				start := time.Now().Add(-time.Hour)
				customEndDate := time.Now().Add(24 * time.Hour) // End date in the future
				entity := &testEntity{id: "1", version: 1, validStartDate: &start, systemStartDate: &start}
				mockRepoHistorical.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoHistorical.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(entity, nil).Times(1)
				mockRepoHistorical.EXPECT().Insert(gomock.Any(), "test_historical", gomock.Any()).Do(
					func(_ context.Context, _ string, archived *testEntity) {
						Expect(archived.GetValidEndDate().Equal(customEndDate)).To(BeTrue())
					},
				).Return(entity, nil).Times(1)
				mockRepoHistorical.EXPECT().DeleteByID(gomock.Any(), "test", entity.id).Return(true, nil).Times(1)
				// ACT
				deleted, err := mgrHistorical.Delete(ctx, "test", entity.id, customEndDate)
				// ASSERT
				Expect(err).To(BeNil())
				Expect(deleted).To(BeTrue())
			})

			It("should return an error when multiple valid end dates are provided", func() {
				// ARRANGE
				start := time.Now().Add(-time.Hour)
				endDate1 := time.Now().Add(24 * time.Hour)
				endDate2 := time.Now().Add(48 * time.Hour)
				entity := &testEntity{id: "1", version: 1, validStartDate: &start, systemStartDate: &start}
				mockRepoHistorical.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoHistorical.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(entity, nil).Times(1)
				// ACT
				_, err := mgrHistorical.Delete(ctx, "test", entity.id, endDate1, endDate2)
				// ASSERT
				Expect(err).To(HaveOccurred())
				Expect(err.Error()).To(Equal("only one valid end date can be provided"))
			})
		})

		Context("when deleting an entity with SameTableStrategy", func() {
			It("should archive the entity in the same table and delete it", func() {
				// ARRANGE
				start := time.Now().Add(-time.Hour)
				entity := &testEntity{id: "1", version: 1, validStartDate: &start, systemStartDate: &start}
				mockRepoSame.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoSame.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(entity, nil).Times(1)
				mockRepoSame.EXPECT().Update(gomock.Any(), "test", gomock.Any()).Return(entity, nil).Times(1)
				// ACT
				deleted, err := mgrSame.Delete(ctx, "test", entity.id)
				// ASSERT
				Expect(err).To(BeNil())
				Expect(deleted).To(BeTrue())
			})
		})

		Context("when FindByID fails", func() {
			It("should return an error", func() {
				entity := &testEntity{id: "1"}
				mockRepoHistorical.EXPECT().Transactional(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, fn func(context.Context) error) error {
						return fn(ctx)
					},
				)
				mockRepoHistorical.EXPECT().FindByID(gomock.Any(), "test", entity.id).Return(nil, context.DeadlineExceeded).Times(1)
				// ACT
				_, err := mgrHistorical.Delete(ctx, "test", entity.id)
				// ASSERT
				Expect(err).To(HaveOccurred())
			})
		})
	})
})
