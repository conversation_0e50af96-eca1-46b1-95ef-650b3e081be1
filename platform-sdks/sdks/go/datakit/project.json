{"name": "datakit", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "sdks/go/datakit", "tags": ["go", "sdk", "cqrs", "library"], "targets": {"test": {"executor": "@nx-go/nx-go:test", "metadata": {"description": "Run unit tests for the datakit SDK using Go test framework"}}, "lint": {"executor": "@nx-go/nx-go:lint", "metadata": {"description": "Run Go linting checks (golangci-lint) for code quality and style"}}, "tidy": {"executor": "@nx-go/nx-go:tidy", "metadata": {"description": "Clean up Go module dependencies (go mod tidy)"}}, "build": {"executor": "nx:run-commands", "options": {"command": "bash -c 'go build ./...'", "cwd": "{projectRoot}"}, "metadata": {"description": "Build all packages in the datakit SDK (library compilation check)"}}}}