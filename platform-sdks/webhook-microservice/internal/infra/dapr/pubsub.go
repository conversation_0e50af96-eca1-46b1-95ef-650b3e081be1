package dapr

import (
	"context"
	"fmt"
	"net/http"
	"time"

	dapr "github.com/dapr/go-sdk/client"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"webhook_microservice/internal/domain"
)

// PubSubClient handles Dapr pub/sub operations
type PubSubClient struct {
	client dapr.Client
	logger *zap.Logger
}

// NewPubSubClient creates a new Dapr pub/sub client
func NewPubSubClient(client dapr.Client, logger *zap.Logger) *PubSubClient {
	return &PubSubClient{
		client: client,
		logger: logger,
	}
}

// PublishEvent publishes an event to a topic
func (p *PubSubClient) PublishEvent(ctx context.Context, pubsubName, topic string, data interface{}) error {
	if p.client == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	err := p.client.PublishEvent(ctx, pubsubName, topic, data)
	if err != nil {
		return fmt.Errorf("failed to publish event: %w", err)
	}

	p.logger.Info("Event published successfully",
		zap.String("pubsub", pubsubName),
		zap.String("topic", topic),
	)

	return nil
}

// SubscriptionHandler represents a subscription event handler
type SubscriptionHandler func(ctx context.Context, event *SubscriptionEvent) error

// SubscriptionEvent represents a Dapr subscription event
type SubscriptionEvent struct {
	ID              string                 `json:"id"`
	Source          string                 `json:"source"`
	Type            string                 `json:"type"`
	SpecVersion     string                 `json:"specversion"`
	DataContentType string                 `json:"datacontenttype"`
	Data            map[string]interface{} `json:"data"`
	Topic           string                 `json:"topic"`
	PubsubName      string                 `json:"pubsubname"`
}

// RegisterSubscriptionHandler registers a subscription handler for Gin router
func (p *PubSubClient) RegisterSubscriptionHandler(router *gin.Engine, topic, pubsubName string, handler SubscriptionHandler) {
	router.POST(fmt.Sprintf("/dapr/subscribe/%s", topic), func(c *gin.Context) {
		var event SubscriptionEvent
		if err := c.ShouldBindJSON(&event); err != nil {
			p.logger.Error("Failed to bind subscription event", zap.Error(err))
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid event format"})
			return
		}

		event.Topic = topic
		event.PubsubName = pubsubName

		ctx := c.Request.Context()
		if err := handler(ctx, &event); err != nil {
			p.logger.Error("Subscription handler failed",
				zap.Error(err),
				zap.String("topic", topic),
				zap.String("pubsub", pubsubName),
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "handler failed"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"status": "SUCCESS"})
	})
}

// GetSubscriptions returns the subscription configuration for Dapr
func (p *PubSubClient) GetSubscriptions() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"pubsubname": "redis-pubsub",
			"topic":      "template-events",
			"route":      "/dapr/subscribe/template-events",
		},
		{
			"pubsubname": "redis-pubsub", 
			"topic":      "notifications",
			"route":      "/dapr/subscribe/notifications",
		},
	}
}

// RegisterSubscriptionsEndpoint registers the /dapr/subscribe endpoint
func (p *PubSubClient) RegisterSubscriptionsEndpoint(router *gin.Engine) {
	router.GET("/dapr/subscribe", func(c *gin.Context) {
		c.JSON(http.StatusOK, p.GetSubscriptions())
	})
}

// WebhookPublishingService implements PublishingService using Dapr pub/sub
type WebhookPublishingService struct {
	client     *PubSubClient
	pubsubName string
	topicName  string
}

// NewWebhookPublishingService creates a new webhook publishing service
func NewWebhookPublishingService(client *PubSubClient, pubsubName, topicName string) *WebhookPublishingService {
	return &WebhookPublishingService{
		client:     client,
		pubsubName: pubsubName,
		topicName:  topicName,
	}
}

// Publish publishes a webhook event to the configured topic
func (w *WebhookPublishingService) Publish(ctx context.Context, event domain.WebhookEvent) error {
	// Create a structured event payload
	eventPayload := map[string]interface{}{
		"delivery_id":  event.DeliveryID.String(),
		"source":       event.Source,
		"payload":      string(event.Payload), // Convert bytes to string for JSON serialization
		"received_at":  event.ReceivedAt.Time.Format(time.RFC3339), // Convert struct to RFC3339 string
	}

	err := w.client.PublishEvent(ctx, w.pubsubName, w.topicName, eventPayload)
	if err != nil {
		return fmt.Errorf("failed to publish webhook event: %w", err)
	}

	return nil
}

// NewPubSubService creates a new publishing service for the container
func NewPubSubService(client dapr.Client, pubsubName, topicName string, logger *zap.Logger) *WebhookPublishingService {
	pubsubClient := NewPubSubClient(client, logger)
	return NewWebhookPublishingService(pubsubClient, pubsubName, topicName)
}