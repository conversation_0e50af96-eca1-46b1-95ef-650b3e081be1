package container

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"strings"
	"sync"

	daprclient "github.com/dapr/go-sdk/client"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

webhook_microservice/internal/app
	"webhook_microservice/internal/app/commands"
	"webhook_microservice/internal/app/cqrs"
	"webhook_microservice/internal/config"
	"webhook_microservice/internal/infra/dapr"
	"webhook_microservice/internal/infra/tracing"
	"webhook_microservice/internal/observability"

	cqrsengine "github.com/Matrics-io/platform-sdks/sdks/go/datakit/v0/cqrs"
)

// Container holds all the application dependencies
type Container struct {
	// Configuration from config package
	config *config.Config

	// Core infrastructure
	logger *slog.Logger
	tracer trace.Tracer

	// Simple Dapr client for webhook functionality
	daprClient daprclient.Client

	// Observability components
	metricsComponents *observability.MetricsComponents
	tracingComponents *observability.TracingComponents
	prometheusEnabled bool

	// Application services
	deduplicationService app.DeduplicationService
	publishingService    app.PublishingService
	appWebhookHandler    *commands.WebhookHandler

	// CQRS components
	cqrsEngine    *cqrsengine.Engine
	cqrsPublisher cqrsengine.Publisher
	cqrsHandlers  *cqrs.WebhookHandlers

	// Initialization control
	initOnce sync.Once
	err      error

	// Cleanup functions
	tracerCleanup func()
}

// NewContainer creates a new container with all dependencies wired together
func NewContainer() *Container {
	// ✅ Load and validate configuration with fail-fast
	cfg, err := config.LoadConfig()
	if err != nil {
		// ❌ REMOVE: Silent fallback to defaults
		// ✅ ADD: Fail fast on configuration errors
		slog.Error("Failed to load configuration", "error", err)
		panic(fmt.Sprintf("Configuration loading failed: %v", err))
	}

	c := &Container{
		config: cfg,
	}

	c.initLogger()
	c.initTracer()
	c.initDaprClient()

	// Initialize application services
	c.initOnce.Do(func() {
		c.initObservability()
		c.initApplicationServices()
		c.initCQRSComponents()
	})

	return c
}

// initLogger initializes the structured logger using configuration
func (c *Container) initLogger() {
	// ✅ Use configuration instead of environment variable
	logLevel := c.config.Logging.Level
	var level slog.Level
	switch strings.ToLower(logLevel) {
	case "debug":
		level = slog.LevelDebug
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: c.config.Environment == "development",
	}
	c.logger = slog.New(slog.NewJSONHandler(os.Stdout, opts)).With("service", "webhook-microservice")
	slog.SetDefault(c.logger)
}

// initTracer initializes OpenTelemetry tracing using configuration
func (c *Container) initTracer() {
	// ✅ Use configuration instead of environment variables
	if !c.config.Observability.Tracing.Enabled {
		c.logger.Info("Tracing disabled in configuration")
		return
	}

	jaegerEndpoint := c.config.Observability.Tracing.Jaeger
	if jaegerEndpoint == "" {
		c.logger.Info("No Jaeger endpoint configured, tracing disabled")
		return
	}

	tracingConfig := tracing.Config{
		ServiceName:    "webhook-microservice",
		JaegerEndpoint: jaegerEndpoint,
		Enabled:        true,
	}

	cleanup, err := tracing.Initialize(tracingConfig)
	if err != nil {
		c.logger.Warn("Failed to initialize tracer, continuing without tracing", "error", err)
		return
	}

	c.tracerCleanup = cleanup
	c.logger.Info("Tracer initialized successfully")
}

// initDaprClient initializes Dapr client using configuration
func (c *Container) initDaprClient() {
	if !c.config.Dapr.Enabled {
		c.logger.Info("Dapr disabled in configuration")
		return
	}

	var err error
	c.daprClient, err = daprclient.NewClient()
	if err != nil {
		c.logger.Warn("Failed to initialize Dapr client, continuing without Dapr", "error", err)
		return
	}

	c.logger.Info("Dapr client initialized successfully")
}

// initObservability initializes observability using configuration
func (c *Container) initObservability() {
	if c.err != nil {
		return
	}

	// Initialize metrics components
	c.metricsComponents = observability.NewMetricsComponents()
	c.logger.Info("Metrics components initialized")

	// Initialize tracing components  
	c.tracingComponents = observability.NewTracingComponents("webhook-microservice")
	c.logger.Info("Tracing components initialized")

	// ✅ Use configuration instead of environment variable
	c.prometheusEnabled = c.config.Observability.Prometheus.Enabled
	c.logger.Info("Observability configuration loaded", "prometheus_enabled", c.prometheusEnabled)
}

// initApplicationServices initializes all application services
func (c *Container) initApplicationServices() {
	if c.err != nil {
		return
	}

	// Initialize deduplication service
	c.initDeduplicationService()
	
	// Initialize publishing service
	c.initPublishingService()
	
	// Initialize app webhook handler
	c.initAppWebhookHandler()
	
	c.logger.Info("Application services initialized successfully")
}

// initDeduplicationService initializes the deduplication service
func (c *Container) initDeduplicationService() {
	if c.err != nil {
		return
	}

	stateClient := dapr.NewStateClient(c.daprClient)
	// ✅ Use configuration instead of fallback
	storeName := c.config.Dapr.StateStore

	c.deduplicationService = dapr.NewWebhookDeduplicationService(stateClient, storeName)
	c.logger.Info("Deduplication service initialized", "store_name", storeName)
}

// initPublishingService initializes the publishing service
func (c *Container) initPublishingService() {
	if c.err != nil {
		return
	}

	// ✅ Use configuration instead of fallback
	pubsubName := c.config.Dapr.PubSubName
	topicName := c.config.Webhook.TopicName

	// Convert slog.Logger to zap.Logger for dapr package
	zapLogger := zap.NewNop() // Default to no-op logger

	c.publishingService = dapr.NewPubSubService(
		c.daprClient,
		pubsubName,
		topicName,
		zapLogger,
	)
	c.logger.Info("Publishing service initialized", "pubsub_name", pubsubName, "topic_name", topicName)
}

// initAppWebhookHandler initializes the webhook handler
func (c *Container) initAppWebhookHandler() {
	if c.err != nil {
		return
	}

	c.appWebhookHandler = commands.NewWebhookHandler(
		c.deduplicationService,
		c.publishingService,
		c.logger,
	)
	c.logger.Info("Webhook handler initialized")
}

// initCQRSComponents initializes CQRS engine and related components
func (c *Container) initCQRSComponents() {
	if c.err != nil {
		return
	}

	// Skip CQRS initialization if disabled
	if !c.config.CQRS.Enabled {
		c.logger.Info("CQRS disabled in configuration")
		return
	}

	c.initCQRSPublisher()
	c.initCQRSHandlers()
	c.initCQRSEngine()
}

// initCQRSPublisher initializes the CQRS publisher
func (c *Container) initCQRSPublisher() {
	if c.err != nil {
		return
	}

	// Create CQRS publisher using existing Dapr configuration
	daprAddress := fmt.Sprintf("http://localhost:%d", c.config.Dapr.HTTPPort)
	c.cqrsPublisher = cqrsengine.NewDaprPublisher(
		c.config.CQRS.Publisher.PubSubName,
		daprAddress,
	)
	c.logger.Info("CQRS publisher initialized",
		"pubsub_name", c.config.CQRS.Publisher.PubSubName,
		"dapr_address", daprAddress)
}

// initCQRSHandlers initializes the CQRS handlers
func (c *Container) initCQRSHandlers() {
	if c.err != nil {
		return
	}

	c.cqrsHandlers = cqrs.NewWebhookHandlers(
		c.appWebhookHandler,
		c.deduplicationService,
		c.publishingService,
		c.logger,
		c.cqrsPublisher,
	)
	c.logger.Info("CQRS handlers initialized")
}

// initCQRSEngine initializes the CQRS engine
func (c *Container) initCQRSEngine() {
	if c.err != nil {
		return
	}

	// Create CQRS engine with HTTP transport
	c.cqrsEngine = cqrsengine.NewEngine(
		c.config.CQRS.ServiceName,
		cqrsengine.WithHTTP(c.config.CQRS.HTTPPort),
		cqrsengine.WithPublisher(c.cqrsPublisher),
	)

	// Register the webhook command to handle the existing /webhook endpoint
	err := c.cqrsEngine.RegisterCommands(
		cqrsengine.CommandConfig{
			Command: &cqrs.ProcessWebhookCommand{},
			Handler: c.cqrsHandlers.ProcessWebhookHandler,
			Metadata: cqrsengine.Metadata{
				Version:     "1.0",
				Description: "Process webhook via CQRS",
				Tags:        []string{"webhook", "process"},
				REST: &cqrsengine.REST{
					Method: "POST",
					Path:   "/webhook", // Use the existing webhook endpoint
				},
				Annotations: map[string]interface{}{
					"rateLimit": "1000/minute",
				},
			},
		},
	)

	if err != nil {
		c.err = fmt.Errorf("failed to register CQRS commands: %w", err)
		return
	}

	// Add custom middleware to CQRS engine's internal Gin router
	c.addCustomMiddlewareToCQRS()

	c.logger.Info("CQRS engine initialized",
		"service_name", c.config.CQRS.ServiceName,
		"http_port", c.config.CQRS.HTTPPort)
}

// addCustomMiddlewareToCQRS adds custom middleware to the CQRS engine's internal Gin router
func (c *Container) addCustomMiddlewareToCQRS() {
	if c.err != nil || c.cqrsEngine == nil {
		return
	}

	// Get the internal Gin router from CQRS engine
	ginRouter := c.cqrsEngine.Gin()

	// Apply standard middleware directly (same as traditional mode)
	ginRouter.Use(gin.Logger())
	ginRouter.Use(c.tracingComponents.Middleware())
	ginRouter.Use(c.metricsComponents.PrometheusMiddleware())
	ginRouter.Use(gin.Recovery())

	// Apply custom middleware directly
	ginRouter.Use(c.webhookAuthMiddleware())
	ginRouter.Use(c.corsMiddleware())
	ginRouter.Use(c.securityHeadersMiddleware())

	c.logger.Info("Standard and custom middleware added to CQRS engine")
}

// Custom middleware functions for CQRS engine
func (c *Container) webhookAuthMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.Request.URL.Path == "/webhook" {
			authHeader := ctx.GetHeader("X-Webhook-Secret")
			if authHeader == "" {
				c.logger.Warn("Missing webhook authentication header", "path", ctx.Request.URL.Path)
				ctx.JSON(401, gin.H{"error": "Missing webhook authentication"})
				ctx.Abort()
				return
			}
			c.logger.Debug("Webhook authentication successful", "path", ctx.Request.URL.Path)
		}
		ctx.Next()
	}
}

func (c *Container) corsMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Header("Access-Control-Allow-Origin", "*")
		ctx.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
		ctx.Header("Access-Control-Allow-Headers", "Content-Type, X-Webhook-Secret")
		if ctx.Request.Method == "OPTIONS" {
			ctx.AbortWithStatus(204)
			return
		}
		ctx.Next()
	}
}

func (c *Container) securityHeadersMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Header("X-Content-Type-Options", "nosniff")
		ctx.Header("X-Frame-Options", "DENY")
		ctx.Header("X-XSS-Protection", "1; mode=block")
		ctx.Next()
	}
}



// Logger returns the logger instance
func (c *Container) Logger() *slog.Logger { return c.logger }

// Config returns the configuration instance
func (c *Container) Config() *config.Config { return c.config }

// DaprClient returns the Dapr client instance
func (c *Container) DaprClient() daprclient.Client { return c.daprClient }

// MetricsComponents returns the metrics components
func (c *Container) MetricsComponents() *observability.MetricsComponents { return c.metricsComponents }

// TracingComponents returns the tracing components
func (c *Container) TracingComponents() *observability.TracingComponents { return c.tracingComponents }

// PrometheusEnabled returns whether Prometheus metrics are enabled
func (c *Container) PrometheusEnabled() bool { return c.prometheusEnabled }

// DeduplicationService returns the deduplication service
func (c *Container) DeduplicationService() app.DeduplicationService { return c.deduplicationService }

// PublishingService returns the publishing service
func (c *Container) PublishingService() app.PublishingService { return c.publishingService }

// AppWebhookHandler returns the webhook handler
func (c *Container) AppWebhookHandler() *commands.WebhookHandler { return c.appWebhookHandler }

// CQRSEngine returns the CQRS engine
func (c *Container) CQRSEngine() *cqrsengine.Engine { return c.cqrsEngine }

// CQRSPublisher returns the CQRS publisher
func (c *Container) CQRSPublisher() cqrsengine.Publisher { return c.cqrsPublisher }

// CQRSHandlers returns the CQRS handlers
func (c *Container) CQRSHandlers() *cqrs.WebhookHandlers { return c.cqrsHandlers }

// HealthCheck performs a health check of all dependencies
func (c *Container) HealthCheck(ctx context.Context) error {
	// Check Dapr client if enabled
	if c.config.Dapr.Enabled && c.daprClient == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	// Check required services
	if c.deduplicationService == nil {
		return fmt.Errorf("deduplication service not initialized")
	}
	if c.publishingService == nil {
		return fmt.Errorf("publishing service not initialized")
	}
	if c.appWebhookHandler == nil {
		return fmt.Errorf("webhook handler not initialized")
	}

	return nil
}

// closeDaprClient closes the Dapr client if initialized
func (c *Container) closeDaprClient() {
	if c.daprClient != nil {
		c.daprClient.Close()
	}
}

// Close performs cleanup of all resources
func (c *Container) Close(ctx context.Context) error {
	// Note: CQRS engine doesn't have a Stop method, it runs until the process ends

	// Clean up tracer if initialized
	if c.tracerCleanup != nil {
		c.tracerCleanup()
	}

	// Clean up Dapr client if initialized
	c.closeDaprClient()

	return nil
}