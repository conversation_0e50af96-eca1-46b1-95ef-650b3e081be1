package container

import (
	"context"
	"fmt"
	"log/slog"
	"os"
	"strings"
	"sync"

	daprclient "github.com/dapr/go-sdk/client"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"

	"webhook_microservice/internal/app"
	"webhook_microservice/internal/app/commands"
	"webhook_microservice/internal/config"
	"webhook_microservice/internal/infra/dapr"
	"webhook_microservice/internal/infra/tracing"
	"webhook_microservice/internal/observability"
)

// Container holds all the application dependencies
type Container struct {
	// Configuration from config package
	config *config.Config

	// Core infrastructure
	logger *slog.Logger
	tracer trace.Tracer

	// Simple Dapr client for webhook functionality
	daprClient daprclient.Client

	// Observability components
	metricsComponents *observability.MetricsComponents
	tracingComponents *observability.TracingComponents
	prometheusEnabled bool

	// Application services
	deduplicationService app.DeduplicationService
	publishingService    app.PublishingService
	appWebhookHandler    *commands.WebhookHandler

	// Initialization control
	initOnce sync.Once
	err      error

	// Cleanup functions
	tracerCleanup func()
}

// NewContainer creates a new container with all dependencies wired together
func NewContainer() *Container {
	// ✅ Load and validate configuration with fail-fast
	cfg, err := config.LoadConfig()
	if err != nil {
		// ❌ REMOVE: Silent fallback to defaults
		// ✅ ADD: Fail fast on configuration errors
		slog.Error("Failed to load configuration", "error", err)
		panic(fmt.Sprintf("Configuration loading failed: %v", err))
	}

	c := &Container{
		config: cfg,
	}

	c.initLogger()
	c.initTracer()
	c.initDaprClient()

	// Initialize application services
	c.initOnce.Do(func() {
		c.initObservability()
		c.initApplicationServices()
	})

	return c
}

// initLogger initializes the structured logger using configuration
func (c *Container) initLogger() {
	// ✅ Use configuration instead of environment variable
	logLevel := c.config.Logging.Level
	var level slog.Level
	switch strings.ToLower(logLevel) {
	case "debug":
		level = slog.LevelDebug
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: c.config.Environment == "development",
	}
	c.logger = slog.New(slog.NewJSONHandler(os.Stdout, opts)).With("service", "webhook-microservice")
	slog.SetDefault(c.logger)
}

// initTracer initializes OpenTelemetry tracing using configuration
func (c *Container) initTracer() {
	// ✅ Use configuration instead of environment variables
	if !c.config.Observability.Tracing.Enabled {
		c.logger.Info("Tracing disabled in configuration")
		return
	}

	jaegerEndpoint := c.config.Observability.Tracing.Jaeger
	if jaegerEndpoint == "" {
		c.logger.Info("No Jaeger endpoint configured, tracing disabled")
		return
	}

	tracingConfig := tracing.Config{
		ServiceName:    "webhook-microservice",
		JaegerEndpoint: jaegerEndpoint,
		Enabled:        true,
	}

	cleanup, err := tracing.Initialize(tracingConfig)
	if err != nil {
		c.logger.Warn("Failed to initialize tracer, continuing without tracing", "error", err)
		return
	}

	c.tracerCleanup = cleanup
	c.logger.Info("Tracer initialized successfully")
}

// initDaprClient initializes Dapr client using configuration
func (c *Container) initDaprClient() {
	if !c.config.Dapr.Enabled {
		c.logger.Info("Dapr disabled in configuration")
		return
	}

	var err error
	c.daprClient, err = daprclient.NewClient()
	if err != nil {
		c.logger.Warn("Failed to initialize Dapr client, continuing without Dapr", "error", err)
		return
	}

	c.logger.Info("Dapr client initialized successfully")
}

// initObservability initializes observability using configuration
func (c *Container) initObservability() {
	if c.err != nil {
		return
	}

	// Initialize metrics components
	c.metricsComponents = observability.NewMetricsComponents()
	c.logger.Info("Metrics components initialized")

	// Initialize tracing components  
	c.tracingComponents = observability.NewTracingComponents("webhook-microservice")
	c.logger.Info("Tracing components initialized")

	// ✅ Use configuration instead of environment variable
	c.prometheusEnabled = c.config.Observability.Prometheus.Enabled
	c.logger.Info("Observability configuration loaded", "prometheus_enabled", c.prometheusEnabled)
}

// initApplicationServices initializes all application services
func (c *Container) initApplicationServices() {
	if c.err != nil {
		return
	}

	// Initialize deduplication service
	c.initDeduplicationService()
	
	// Initialize publishing service
	c.initPublishingService()
	
	// Initialize app webhook handler
	c.initAppWebhookHandler()
	
	c.logger.Info("Application services initialized successfully")
}

// initDeduplicationService initializes the deduplication service
func (c *Container) initDeduplicationService() {
	if c.err != nil {
		return
	}

	stateClient := dapr.NewStateClient(c.daprClient)
	// ✅ Use configuration instead of fallback
	storeName := c.config.Dapr.StateStore

	c.deduplicationService = dapr.NewWebhookDeduplicationService(stateClient, storeName)
	c.logger.Info("Deduplication service initialized", "store_name", storeName)
}

// initPublishingService initializes the publishing service
func (c *Container) initPublishingService() {
	if c.err != nil {
		return
	}

	// ✅ Use configuration instead of fallback
	pubsubName := c.config.Dapr.PubSubName
	topicName := c.config.Webhook.TopicName

	// Convert slog.Logger to zap.Logger for dapr package
	zapLogger := zap.NewNop() // Default to no-op logger

	c.publishingService = dapr.NewPubSubService(
		c.daprClient,
		pubsubName,
		topicName,
		zapLogger,
	)
	c.logger.Info("Publishing service initialized", "pubsub_name", pubsubName, "topic_name", topicName)
}

// initAppWebhookHandler initializes the webhook handler
func (c *Container) initAppWebhookHandler() {
	if c.err != nil {
		return
	}

	c.appWebhookHandler = commands.NewWebhookHandler(
		c.deduplicationService,
		c.publishingService,
		c.logger,
	)
	c.logger.Info("Webhook handler initialized")
}

// Logger returns the logger instance
func (c *Container) Logger() *slog.Logger { return c.logger }

// Config returns the configuration instance
func (c *Container) Config() *config.Config { return c.config }

// DaprClient returns the Dapr client instance
func (c *Container) DaprClient() daprclient.Client { return c.daprClient }

// MetricsComponents returns the metrics components
func (c *Container) MetricsComponents() *observability.MetricsComponents { return c.metricsComponents }

// TracingComponents returns the tracing components
func (c *Container) TracingComponents() *observability.TracingComponents { return c.tracingComponents }

// PrometheusEnabled returns whether Prometheus metrics are enabled
func (c *Container) PrometheusEnabled() bool { return c.prometheusEnabled }

// DeduplicationService returns the deduplication service
func (c *Container) DeduplicationService() app.DeduplicationService { return c.deduplicationService }

// PublishingService returns the publishing service
func (c *Container) PublishingService() app.PublishingService { return c.publishingService }

// AppWebhookHandler returns the webhook handler
func (c *Container) AppWebhookHandler() *commands.WebhookHandler { return c.appWebhookHandler }

// HealthCheck performs a health check of all dependencies
func (c *Container) HealthCheck(ctx context.Context) error {
	// Check Dapr client if enabled
	if c.config.Dapr.Enabled && c.daprClient == nil {
		return fmt.Errorf("dapr client not initialized")
	}

	// Check required services
	if c.deduplicationService == nil {
		return fmt.Errorf("deduplication service not initialized")
	}
	if c.publishingService == nil {
		return fmt.Errorf("publishing service not initialized")
	}
	if c.appWebhookHandler == nil {
		return fmt.Errorf("webhook handler not initialized")
	}

	return nil
}

// closeDaprClient closes the Dapr client if initialized
func (c *Container) closeDaprClient() {
	if c.daprClient != nil {
		c.daprClient.Close()
	}
}

// Close performs cleanup of all resources
func (c *Container) Close(ctx context.Context) error {
	// Clean up tracer if initialized
	if c.tracerCleanup != nil {
		c.tracerCleanup()
	}

	// Clean up Dapr client if initialized
	c.closeDaprClient()

	return nil
} 