# CQRS SDK Integration Migration Guide

This document describes the migration from Redis-based pub/sub to Kafka-based CQRS SDK integration in the webhook microservice.

## Overview

The webhook microservice has been migrated to use the CQRS SDK for event publishing while maintaining backward compatibility and existing functionality.

### Key Changes

1. **Pub/Sub Backend**: Switched from Redis to Kafka for better scalability and CQRS patterns
2. **Publishing Mechanism**: Replaced direct Dapr client with CQRS SDK publisher
3. **Event Interface**: Enhanced `WebhookEvent` to implement CQRS `Named` interface
4. **Configuration**: Updated to support Kafka and CQRS SDK settings

## Migration Details

### 1. Dependencies
- **Added**: `github.com/Matrics-io/platform-sdks/sdks/go/datakit/v0/cqrs`
- **Maintained**: Existing Dapr SDK for state management

### 2. Infrastructure Changes

#### Pub/Sub Component
- **Before**: `redis-pubsub` component using Redis
- **After**: `kafka-pubsub` component using Kafka

#### Docker Compose
- **Added**: Kafka and Zookeeper services
- **Maintained**: Redis for state store (deduplication)

### 3. Code Changes

#### Publishing Service
- **Before**: `WebhookPublishingService` using direct Dapr client
- **After**: `CQRSPublishingService` using CQRS SDK publisher
- **Interface**: Maintained existing `PublishingService` interface for compatibility

#### Event Structure
- **Enhanced**: `WebhookEvent` now implements `Name()` method for CQRS compatibility
- **Maintained**: All existing event fields and validation

#### Configuration
- **Added**: `http_address` field to Dapr configuration for CQRS SDK
- **Updated**: Default `pubsub_name` from `redis-pubsub` to `kafka-pubsub`

## Backward Compatibility

### Event Payload Format
The event payload format remains backward compatible:
```json
{
  "delivery_id": "uuid-string",
  "source": "source-name",
  "payload": "json-string",
  "received_at": "2024-01-01T00:00:00Z",
  "event_type": "webhook.received"
}
```

### API Endpoints
All existing HTTP endpoints remain unchanged:
- `POST /webhook` - Main webhook endpoint
- `GET /health` - Health check
- `GET /metrics` - Prometheus metrics (if enabled)

### Business Logic
All business logic including deduplication, validation, and error handling remains unchanged.

## Deployment

### Prerequisites
1. Kafka cluster running on `localhost:9092`
2. Dapr runtime with Kafka pub/sub component
3. Redis for state store (deduplication)

### Configuration Updates
Update your configuration to use Kafka:
```yaml
dapr:
  pubsub_name: kafka-pubsub
  http_address: http://localhost:3500
```

### Component Files
Ensure the following component files are present:
- `components/kafka-pubsub.yaml` - Kafka pub/sub component
- `components/redis-statestore.yaml` - Redis state store (unchanged)

## Testing

### Integration Tests
Run the CQRS integration tests:
```bash
go test ./tests -run TestCQRS
```

### Manual Testing
1. Start Kafka and Zookeeper
2. Start Dapr with Kafka component
3. Send webhook requests to verify publishing works

## Monitoring

### Metrics
The same Prometheus metrics are available:
- `webhook_requests_total`
- `webhook_processing_duration_seconds`
- `webhook_errors_total`

### Logs
Enhanced logging includes CQRS publisher information:
```
INFO CQRS Publishing service initialized pubsub_name=kafka-pubsub topic_name=webhook-events
```

## Rollback Plan

If rollback is needed:
1. Revert `go.mod` to remove CQRS SDK dependency
2. Restore `redis-pubsub.yaml` component
3. Update configuration to use `redis-pubsub`
4. Revert container initialization to use old publishing service

## Performance Considerations

### Kafka vs Redis
- **Throughput**: Kafka provides higher throughput for high-volume scenarios
- **Durability**: Kafka offers better message durability and replay capabilities
- **Scalability**: Kafka scales better for multiple consumers

### Resource Usage
- **Memory**: Slightly higher due to CQRS SDK overhead
- **CPU**: Minimal impact from CQRS abstraction layer
- **Network**: Similar network usage patterns

## Support

For issues related to CQRS integration:
1. Check Kafka connectivity and component configuration
2. Verify Dapr HTTP address configuration
3. Review CQRS SDK logs for publishing errors
4. Ensure event payload validation passes
