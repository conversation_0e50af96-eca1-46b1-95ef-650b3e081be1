########################################################
## MAIN CONFIGURATION FILE
########################################################

server:
  port: 2021
  host: 0.0.0.0

environment: development

logging:
  level: info  # debug, info, warn, error

observability:
  prometheus:
    enabled: false
    path: /metrics
  tracing:
    enabled: false
    jaeger: http://localhost:14268/api/traces

# Dapr configuration for webhook microservice
dapr:
  enabled: true
  app_id: webhook-microservice
  http_port: 3500
  grpc_port: 50001
  state_store: statestore # /components/redis-statestore.yaml
  pubsub_name: redis-pubsub # /components/redis-pubsub.yaml
  secret_store: config-secret-store # /components/config-secret-store.yaml

# Webhook-specific configuration
webhook:
  topic_name: "webhook-events"
  rate_limit_rpm: 1000 # requests per minute
  waf_allow_list:
    - "0.0.0.0/0"
  validator:
    deliveryIDHeaders:
      "X-GitHub-Delivery": "github"
      "X-Atlassian-Webhook-Identifier": "jira"
      "requestId": "crosschex"
      "X-Delivery-ID": "generic"
    requiredHeaders:
      - "Content-Type"

# Redis configuration for state store and pub/sub
redis:
  addr: "webhook-redis:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  key_prefix: "webhook:"
  default_ttl: 86400  # 24 hours in seconds for deduplication