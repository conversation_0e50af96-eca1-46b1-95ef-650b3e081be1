package main

import (
	"net/http"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// ProviderHandler handles API requests for providers
type ProviderHandler struct {
	manager *bitemporality.Manager[*Provider]
	logger  *zap.Logger
}

// NewProviderHandler creates a new ProviderHandler
func NewProviderHandler(manager *bitemporality.Manager[*Provider], logger *zap.Logger) *ProviderHandler {
	return &ProviderHandler{
		manager: manager,
		logger:  logger,
	}
}

type CreateProviderRequest struct {
	Name      string `json:"name" binding:"required"`
	Specialty string `json:"specialty" binding:"required"`
}

func (h *ProviderHandler) CreateProvider(c *gin.Context) {
	var req CreateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	provider := NewProvider(req.Name, req.Specialty)

	createdProvider, err := h.manager.Insert(c.Request.Context(), "providers", provider)
	if err != nil {
		h.logger.Error("failed to create provider", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create provider"})
		return
	}

	c.JSON(http.StatusCreated, createdProvider)
}

func (h *ProviderHandler) GetProvider(c *gin.Context) {
	id := c.Param("id")
	provider, err := h.manager.Repository().FindByID(c.Request.Context(), "providers", id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "provider not found"})
		return
	}
	c.JSON(http.StatusOK, provider)
}

type UpdateProviderRequest struct {
	Name      string `json:"name"`
	Specialty string `json:"specialty"`
}

func (h *ProviderHandler) UpdateProvider(c *gin.Context) {
	id := c.Param("id")

	var req UpdateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	provider, err := h.manager.Repository().FindByID(c.Request.Context(), "providers", id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "provider not found"})
		return
	}

	if req.Name != "" {
		provider.Name = req.Name
	}
	if req.Specialty != "" {
		provider.Specialty = req.Specialty
	}

	updatedProvider, err := h.manager.Update(c.Request.Context(), "providers", provider)
	if err != nil {
		h.logger.Error("failed to update provider", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update provider"})
		return
	}

	c.JSON(http.StatusOK, updatedProvider)
}

func (h *ProviderHandler) DeleteProvider(c *gin.Context) {
	id := c.Param("id")
	deleted, err := h.manager.Delete(c.Request.Context(), "providers", id)
	if err != nil || !deleted {
		h.logger.Error("failed to delete provider", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete provider"})
		return
	}
	c.Status(http.StatusNoContent)
}

func (h *ProviderHandler) ListProviders(c *gin.Context) {
	providers, err := h.manager.Repository().ListAll(c.Request.Context(), "providers")
	if err != nil {
		h.logger.Error("failed to list providers", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list providers"})
		return
	}
	c.JSON(http.StatusOK, providers)
}
