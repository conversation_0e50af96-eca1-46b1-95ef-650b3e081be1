package main

import (
	"net/http"
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

const (
	patientsTable = "patients"
)

type PatientHandler struct {
	manager *bitemporality.Manager[*Patient]
	logger  *zap.Logger
}

func NewPatientHandler(manager *bitemporality.Manager[*Patient], logger *zap.Logger) *PatientHandler {
	return &PatientHandler{
		manager: manager,
		logger:  logger,
	}
}

func (h *PatientHandler) CreatePatient(c *gin.Context) {
	var patient Patient
	if err := c.ShouldBindJSON(&patient); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if patient.ID == "" {
		patient.ID = NewPatient().ID
	}

	result, err := h.manager.Insert(c.Request.Context(), patientsTable, &patient)
	if err != nil {
		h.logger.Error("failed to create patient", zap.Error(err))
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to create patient"})
		return
	}

	c.JSON(http.StatusCreated, result)
}

func (h *PatientHandler) GetPatient(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing patient ID"})
		return
	}

	result, err := h.manager.Repository().FindByID(c.Request.Context(), patientsTable, id)
	if err != nil {
		h.logger.Error("failed to get patient", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get patient"})
		return
	}

	c.JSON(http.StatusOK, result)
}

func (h *PatientHandler) UpdatePatient(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing patient ID"})
		return
	}

	var patient Patient
	if err := c.ShouldBindJSON(&patient); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	patient.ID = id
	result, err := h.manager.Update(c.Request.Context(), patientsTable, &patient)
	if err != nil {
		h.logger.Error("failed to update patient", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update patient"})
		return
	}

	c.JSON(http.StatusOK, result)
}

func (h *PatientHandler) DeletePatient(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing patient ID"})
		return
	}

	// Support for custom valid end date
	var validEndDate *time.Time
	if dateStr := c.Query("validEndDate"); dateStr != "" {
		date, err := time.Parse(time.RFC3339, dateStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid validEndDate format. Use RFC3339 format (e.g. 2024-12-31T23:59:59Z)"})
			return
		}
		validEndDate = &date
	}

	var err error
	var deleted bool
	if validEndDate != nil {
		deleted, err = h.manager.Delete(c.Request.Context(), patientsTable, id, *validEndDate)
	} else {
		deleted, err = h.manager.Delete(c.Request.Context(), patientsTable, id)
	}

	if err != nil {
		h.logger.Error("failed to delete patient", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete patient"})
		return
	}

	if !deleted {
		c.JSON(http.StatusNotFound, gin.H{"error": "Patient not found"})
		return
	}

	c.Status(http.StatusNoContent)
}

func (h *PatientHandler) ListPatients(c *gin.Context) {
	patients, err := h.manager.Repository().ListAll(c.Request.Context(), patientsTable)
	if err != nil {
		h.logger.Error("failed to list patients", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list patients"})
		return
	}
	c.JSON(http.StatusOK, patients)
}
