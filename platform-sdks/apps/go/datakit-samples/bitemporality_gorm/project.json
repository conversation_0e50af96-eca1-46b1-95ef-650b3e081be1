{"name": "datakit-samples-bitemporality-gorm", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/go/datakit-samples/bitemporality_gorm", "tags": ["go", "sample", "bitemporality", "gorm", "dapr"], "targets": {"docker:up": {"executor": "nx:run-commands", "options": {"command": "docker-compose up -d", "cwd": "{projectRoot}"}, "metadata": {"description": "Start Docker containers for the bitemporality GORM sample"}}, "docker:down": {"executor": "nx:run-commands", "options": {"command": "docker-compose down -v", "cwd": "{projectRoot}"}, "metadata": {"description": "Stop and remove Docker containers and volumes for the bitemporality GORM sample"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "go run .", "cwd": "{projectRoot}"}}, "test": {"executor": "nx:run-commands", "options": {"command": "go test ./...", "cwd": "{projectRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "go fmt ./...", "cwd": "{projectRoot}"}, "metadata": {"description": "Run go fmt for code formatting"}}}}