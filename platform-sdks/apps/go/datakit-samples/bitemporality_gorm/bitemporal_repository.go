package main

import (
	"context"
	"fmt"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// BitemporalRepository is a generic repository for bitemporal entities using GORM
type BitemporalRepository[T bitemporality.Entity] struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewBitemporalRepository creates a new BitemporalRepository
func NewBitemporalRepository[T bitemporality.Entity](db *gorm.DB, logger *zap.Logger) *BitemporalRepository[T] {
	return &BitemporalRepository[T]{
		db:     db,
		logger: logger,
	}
}

func (r *BitemporalRepository[T]) Transactional(ctx context.Context, fn func(ctxWithTx context.Context) error) error {
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("begin transaction: %w", tx.Error)
	}

	if err := fn(ctx); err != nil {
		tx.Rollback()
		return err
	}

	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("commit transaction: %w", err)
	}

	return nil
}

func (r *BitemporalRepository[T]) FindByID(ctx context.Context, tableName string, id string) (T, error) {
	var entity T
	if err := r.db.WithContext(ctx).Table(tableName).Where("id = ? AND system_end_date IS NULL", id).First(&entity).Error; err != nil {
		return entity, fmt.Errorf("find by id: %w", err)
	}
	return entity, nil
}

func (r *BitemporalRepository[T]) Insert(ctx context.Context, tableName string, entity T) (T, error) {
	result := r.db.WithContext(ctx).Table(tableName).Create(entity)
	if result.Error != nil {
		return entity, fmt.Errorf("insert: %w", result.Error)
	}
	return entity, nil
}

func (r *BitemporalRepository[T]) Update(ctx context.Context, tableName string, entity T) (T, error) {
	result := r.db.WithContext(ctx).Table(tableName).Save(entity)
	if result.Error != nil {
		return entity, fmt.Errorf("update: %w", result.Error)
	}
	return entity, nil
}

func (r *BitemporalRepository[T]) DeleteByID(ctx context.Context, tableName string, id string) (bool, error) {
	var entity T
	result := r.db.WithContext(ctx).Table(tableName).Where("id = ? AND system_end_date IS NULL", id).Delete(&entity)
	if result.Error != nil {
		return false, fmt.Errorf("delete: %w", result.Error)
	}
	return result.RowsAffected > 0, nil
}

func (r *BitemporalRepository[T]) ListAll(ctx context.Context, tableName string) ([]T, error) {
	var entities []T
	result := r.db.WithContext(ctx).Table(tableName).Where("system_end_date IS NULL").Find(&entities)
	if result.Error != nil {
		return nil, fmt.Errorf("list all: %w", result.Error)
	}
	return entities, nil
}
