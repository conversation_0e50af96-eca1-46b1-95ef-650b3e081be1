package main

import (
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"
	"github.com/google/uuid"
)

// Patient represents a simplified FHIR Patient resource with bitemporal tracking
type Patient struct {
	ID              string     `json:"id" gorm:"primaryKey" db:"id"`
	Version         int64      `json:"version" db:"version"`
	ValidStartDate  *time.Time `json:"validStartDate" db:"valid_start_date"`
	ValidEndDate    *time.Time `json:"validEndDate" db:"valid_end_date"`
	SystemStartDate *time.Time `json:"systemStartDate" gorm:"primaryKey" db:"system_start_date"`
	SystemEndDate   *time.Time `json:"systemEndDate" db:"system_end_date"`

	// FHIR Patient fields
	Active      bool   `json:"active" db:"active"`
	FirstName   string `json:"firstName" db:"first_name"`
	LastName    string `json:"lastName" db:"last_name"`
	Gender      string `json:"gender" db:"gender"`
	BirthDate   string `json:"birthDate" db:"birth_date"`
	PhoneNumber string `json:"phoneNumber" db:"phone_number"`
	Email       string `json:"email" db:"email"`
	Address     string `json:"address" db:"address"`
}

// Ensure Patient implements Entity interface
var _ bitemporality.Entity = (*Patient)(nil)

// NewPatient creates a new Patient with a generated UUID
func NewPatient() *Patient {
	return &Patient{
		ID: uuid.New().String(),
	}
}

// Implement Entity interface
func (p *Patient) GetID() string                   { return p.ID }
func (p *Patient) SetID(id string)                 { p.ID = id }
func (p *Patient) GetVersion() int64               { return p.Version }
func (p *Patient) SetVersion(v int64)              { p.Version = v }
func (p *Patient) GetValidStartDate() *time.Time   { return p.ValidStartDate }
func (p *Patient) SetValidStartDate(t *time.Time)  { p.ValidStartDate = t }
func (p *Patient) GetValidEndDate() *time.Time     { return p.ValidEndDate }
func (p *Patient) SetValidEndDate(t *time.Time)    { p.ValidEndDate = t }
func (p *Patient) GetSystemStartDate() *time.Time  { return p.SystemStartDate }
func (p *Patient) SetSystemStartDate(t *time.Time) { p.SystemStartDate = t }
func (p *Patient) GetSystemEndDate() *time.Time    { return p.SystemEndDate }
func (p *Patient) SetSystemEndDate(t *time.Time)   { p.SystemEndDate = t }
