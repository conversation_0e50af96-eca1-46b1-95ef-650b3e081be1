-- Create providers table with composite primary key for bitemporal data
CREATE TABLE IF NOT EXISTS providers (
    id TEXT,
    version BIGINT NOT NULL,
    valid_start_date TIMESTAMPTZ NOT NULL,
    valid_end_date TIMESTAMPTZ,
    system_start_date TIMESTAMPTZ NOT NULL,
    system_end_date TIMESTAMPTZ,
    name TEXT NOT NULL,
    specialty TEXT NOT NULL,
    PRIMARY KEY (id, system_start_date)
);

CREATE INDEX IF NOT EXISTS idx_providers_valid ON providers (valid_start_date, valid_end_date);
CREATE INDEX IF NOT EXISTS idx_providers_system ON providers (system_start_date, system_end_date); 