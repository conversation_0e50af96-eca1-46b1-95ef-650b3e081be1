-- Create main patients table for bitemporality (SameTableStrategy)
CREATE TABLE IF NOT EXISTS patients (
    id TEXT NOT NULL,
    version BIGINT NOT NULL,
    valid_start_date TIMESTAMPTZ NOT NULL,
    valid_end_date TIMESTAMPTZ,
    system_start_date TIMESTAMPTZ NOT NULL,
    system_end_date TIMESTAMPTZ,
    active BOOLEAN NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    gender TEXT NOT NULL,
    birth_date TEXT NOT NULL,
    phone_number TEXT,
    email TEXT,
    address TEXT,
    PRIMARY KEY (id, system_start_date)
);

CREATE INDEX IF NOT EXISTS idx_patients_valid ON patients (valid_start_date, valid_end_date);
CREATE INDEX IF NOT EXISTS idx_patients_system ON patients (system_start_date, system_end_date); 
