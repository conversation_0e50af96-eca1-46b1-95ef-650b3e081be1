package main

import (
	"time"

	"github.com/google/uuid"
)

// Appointment represents a non-bitemporal entity
type Appointment struct {
	ID              string    `json:"id" gorm:"primaryKey"`
	PatientID       string    `json:"patientId"`
	AppointmentDate time.Time `json:"appointmentDate"`
	Description     string    `json:"description"`
	CreatedAt       time.Time `json:"createdAt"`
}

// NewAppointment creates a new Appointment for a given patient
func NewAppointment(patientID, description string, appointmentDate time.Time) *Appointment {
	return &Appointment{
		ID:              uuid.New().String(),
		PatientID:       patientID,
		AppointmentDate: appointmentDate,
		Description:     description,
		CreatedAt:       time.Now(),
	}
}
