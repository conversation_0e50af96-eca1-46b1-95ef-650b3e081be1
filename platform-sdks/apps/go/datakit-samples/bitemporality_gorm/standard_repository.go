package main

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

// StandardRepository is a generic repository for non-bitemporal entities
type StandardRepository[T any] struct {
	db     *gorm.DB
	logger *zap.Logger
}

// NewStandardRepository creates a new StandardRepository
func NewStandardRepository[T any](db *gorm.DB, logger *zap.Logger) *StandardRepository[T] {
	return &StandardRepository[T]{
		db:     db,
		logger: logger,
	}
}

// Create inserts a new record into the database
func (r *StandardRepository[T]) Create(ctx context.Context, entity *T) (*T, error) {
	result := r.db.WithContext(ctx).Create(entity)
	if result.Error != nil {
		return nil, fmt.Errorf("create: %w", result.Error)
	}
	return entity, nil
}

// FindBy queries for records based on a condition
func (r *StandardRepository[T]) FindBy(ctx context.Context, condition string, args ...interface{}) ([]*T, error) {
	var entities []*T
	result := r.db.WithContext(ctx).Where(condition, args...).Find(&entities)
	if result.Error != nil {
		return nil, fmt.Errorf("find by: %w", result.Error)
	}
	return entities, nil
}
