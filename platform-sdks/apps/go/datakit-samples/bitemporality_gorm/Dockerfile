# syntax=docker/dockerfile:1

# Build stage
FROM golang:1.24.3 AS builder
WORKDIR /app
COPY go.mod go.sum ./
# Copy the local SDK code for replace directive
COPY ../../../../sdks/go/datakit ../../../../sdks/go/datakit
COPY . .
RUN go mod download
RUN CGO_ENABLED=0 GOOS=linux go build -o app main.go

# Run stage
FROM alpine:latest
WORKDIR /app
COPY --from=builder /app/app ./app
COPY migrations ./migrations
EXPOSE 8080
ENTRYPOINT ["./app"] 