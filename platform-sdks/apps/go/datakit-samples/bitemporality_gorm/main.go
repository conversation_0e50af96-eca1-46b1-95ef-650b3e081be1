package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

const (
	defaultPort = "8080"
)

func main() {
	// Initialize logger
	logger, _ := zap.NewProduction()
	defer logger.Sync()

	// Get database connection string from environment
	dbURL := os.Getenv("DATABASE_URL")
	if dbURL == "" {
		logger.Fatal("DATABASE_URL environment variable is required")
	}

	// Connect to database
	db, err := gorm.Open(postgres.Open(dbURL), &gorm.Config{})
	if err != nil {
		logger.Fatal("failed to connect to database", zap.Error(err))
	}

	// Initialize repositories
	config := bitemporality.NewConfig()
	patientRepo := NewBitemporalRepository[*Patient](db, logger)
	providerRepo := NewBitemporalRepository[*Provider](db, logger)
	appointmentRepo := NewStandardRepository[Appointment](db, logger)

	// Initialize bitemporal managers
	patientManager := bitemporality.NewManager[*Patient](patientRepo, config)
	providerManager := bitemporality.NewManager[*Provider](providerRepo, config)

	// Initialize handlers
	patientHandler := NewPatientHandler(patientManager, logger)
	appointmentHandler := NewAppointmentHandler(appointmentRepo, logger)
	providerHandler := NewProviderHandler(providerManager, logger)

	// Set up Gin router
	r := gin.Default()

	// Routes
	api := r.Group("/api")
	{
		patients := api.Group("/patients")
		{
			patients.POST("/", patientHandler.CreatePatient)
			patients.GET("/:id", patientHandler.GetPatient)
			patients.PUT("/:id", patientHandler.UpdatePatient)
			patients.DELETE("/:id", patientHandler.DeletePatient)
			patients.GET("/", patientHandler.ListPatients)
		}
		appointments := api.Group("/appointments")
		{
			appointments.POST("/", appointmentHandler.CreateAppointment)
			appointments.GET("/patient/:patientId", appointmentHandler.GetAppointmentsForPatient)
		}
		providers := api.Group("/providers")
		{
			providers.POST("/", providerHandler.CreateProvider)
			providers.GET("/:id", providerHandler.GetProvider)
			providers.PUT("/:id", providerHandler.UpdateProvider)
			providers.DELETE("/:id", providerHandler.DeleteProvider)
			providers.GET("/", providerHandler.ListProviders)
		}
	}

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = defaultPort
	}

	srv := &http.Server{
		Addr:    fmt.Sprintf(":%s", port),
		Handler: r,
	}

	// Graceful shutdown
	done := make(chan bool)
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-quit
		logger.Info("server is shutting down...")

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		srv.SetKeepAlivesEnabled(false)
		if err := srv.Shutdown(ctx); err != nil {
			logger.Fatal("could not gracefully shutdown the server", zap.Error(err))
		}
		close(done)
	}()

	logger.Info("server is starting...", zap.String("port", port))
	if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		logger.Fatal("could not start server", zap.Error(err))
	}

	<-done
	logger.Info("server stopped")
}
