package main

import (
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"
	"github.com/google/uuid"
)

// Provider represents a healthcare provider with bitemporal tracking
type Provider struct {
	ID              string     `json:"id"`
	Version         int64      `json:"version"`
	ValidStartDate  *time.Time `json:"validStartDate"`
	ValidEndDate    *time.Time `json:"validEndDate"`
	SystemStartDate *time.Time `json:"systemStartDate"`
	SystemEndDate   *time.Time `json:"systemEndDate"`
	Name            string     `json:"name"`
	Specialty       string     `json:"specialty"`
}

// Ensure Provider implements Entity interface
var _ bitemporality.Entity = (*Provider)(nil)

// NewProvider creates a new Provider
func NewProvider(name, specialty string) *Provider {
	return &Provider{
		ID:        uuid.New().String(),
		Name:      name,
		Specialty: specialty,
	}
}

// Implement Entity interface
func (p *Provider) GetID() string                   { return p.ID }
func (p *Provider) SetID(id string)                 { p.ID = id }
func (p *Provider) GetVersion() int64               { return p.Version }
func (p *Provider) SetVersion(v int64)              { p.Version = v }
func (p *Provider) GetValidStartDate() *time.Time   { return p.ValidStartDate }
func (p *Provider) SetValidStartDate(t *time.Time)  { p.ValidStartDate = t }
func (p *Provider) GetValidEndDate() *time.Time     { return p.ValidEndDate }
func (p *Provider) SetValidEndDate(t *time.Time)    { p.ValidEndDate = t }
func (p *Provider) GetSystemStartDate() *time.Time  { return p.SystemStartDate }
func (p *Provider) SetSystemStartDate(t *time.Time) { p.SystemStartDate = t }
func (p *Provider) GetSystemEndDate() *time.Time    { return p.SystemEndDate }
func (p *Provider) SetSystemEndDate(t *time.Time)   { p.SystemEndDate = t }
