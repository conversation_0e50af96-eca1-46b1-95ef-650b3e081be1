package main

import (
	"context"
	"fmt"
	"reflect"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// DB is an interface that groups query and exec methods, implemented by both pgx.Tx and pgxpool.Pool
type DB interface {
	pgxscan.Querier
	Exec(ctx context.Context, sql string, arguments ...interface{}) (pgconn.CommandTag, error)
}

// BitemporalRepository is a generic repository for bitemporal entities using pgx
type BitemporalRepository[T bitemporality.Entity] struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewBitemporalRepository creates a new BitemporalRepository
func NewBitemporalRepository[T bitemporality.Entity](pool *pgxpool.Pool, logger *zap.Logger) *BitemporalRepository[T] {
	return &BitemporalRepository[T]{
		pool:   pool,
		logger: logger,
	}
}

func (r *BitemporalRepository[T]) Transactional(ctx context.Context, fn func(ctxWithTx context.Context) error) error {
	tx, err := r.pool.Begin(ctx)
	if err != nil {
		return fmt.Errorf("begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Create a new context with the transaction
	ctxWithTx := context.WithValue(ctx, "tx", tx)

	if err := fn(ctxWithTx); err != nil {
		return err // The rollback is deferred
	}

	return tx.Commit(ctx)
}

func (r *BitemporalRepository[T]) getDB(ctx context.Context) DB {
	if tx, ok := ctx.Value("tx").(pgx.Tx); ok {
		return tx
	}
	return r.pool
}

func (r *BitemporalRepository[T]) FindByID(ctx context.Context, tableName string, id string) (T, error) {
	// Since T is a pointer type (e.g. *Patient), we can't just pass &T to pgxscan,
	// as it would be a double pointer (**Patient).
	// Instead, we use reflection to create a new instance of the underlying struct (Patient),
	// get a pointer to it, and pass that to pgxscan.
	var zero T
	entity := reflect.New(reflect.TypeOf(zero).Elem()).Interface()

	query := fmt.Sprintf("SELECT * FROM %s WHERE id = $1 AND system_end_date IS NULL", tableName)
	if err := pgxscan.Get(ctx, r.getDB(ctx), entity, query, id); err != nil {
		return zero, err
	}

	return entity.(T), nil
}

func (r *BitemporalRepository[T]) Insert(ctx context.Context, tableName string, entity T) (T, error) {
	query, args, err := buildInsertQuery(tableName, entity)
	if err != nil {
		return entity, fmt.Errorf("build insert query: %w", err)
	}

	_, err = r.getDB(ctx).Exec(ctx, query, args...)
	return entity, err
}

func (r *BitemporalRepository[T]) Update(ctx context.Context, tableName string, entity T) (T, error) {
	query, args, err := buildUpdateQuery(tableName, entity)
	r.logger.Info("Update query", zap.String("query", query), zap.Any("args", args))
	if err != nil {
		return entity, fmt.Errorf("build update query: %w", err)
	}
	_, err = r.getDB(ctx).Exec(ctx, query, args...)
	return entity, err
}

func (r *BitemporalRepository[T]) DeleteByID(ctx context.Context, tableName string, id string) (bool, error) {
	query := fmt.Sprintf("DELETE FROM %s WHERE id = $1", tableName)
	result, err := r.getDB(ctx).Exec(ctx, query, id)
	if err != nil {
		return false, err
	}
	return result.RowsAffected() > 0, nil
}

func (r *BitemporalRepository[T]) ListAll(ctx context.Context, tableName string) ([]T, error) {
	var entities []T
	query := fmt.Sprintf("SELECT * FROM %s WHERE system_end_date IS NULL", tableName)
	err := pgxscan.Select(ctx, r.getDB(ctx), &entities, query)
	return entities, err
}
