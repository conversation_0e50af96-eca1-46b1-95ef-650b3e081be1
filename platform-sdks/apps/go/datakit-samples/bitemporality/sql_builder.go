package main

import (
	"fmt"
	"reflect"
	"strings"
	"unicode"
)

// buildInsertQuery dynamically builds a SQL INSERT statement from a struct
func buildInsertQuery(table string, s interface{}) (string, []interface{}, error) {
	v := reflect.ValueOf(s)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return "", nil, fmt.Errorf("expected a struct")
	}

	var columns []string
	var placeholders []string
	var values []interface{}

	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		// This is a simplified example; a real implementation would parse json/db tags
		// to get the correct column names.
		fieldName := toSnakeCase(t.Field(i).Name)
		columns = append(columns, fieldName)
		placeholders = append(placeholders, fmt.Sprintf("$%d", i+1))
		values = append(values, v.Field(i).Interface())
	}

	query := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		table,
		strings.Join(columns, ", "),
		strings.Join(placeholders, ", "),
	)

	return query, values, nil
}

// toSnakeCase converts a string from CamelCase to snake_case
func toSnakeCase(str string) string {
	var result strings.Builder
	for i, r := range str {
		if i > 0 && isUpper(r) {
			if i+1 < len(str) && isLower(rune(str[i+1])) || isLower(rune(str[i-1])) {
				result.WriteRune('_')
			}
		}
		result.WriteRune(unicode.ToLower(r))
	}
	return result.String()
}

func isUpper(r rune) bool {
	return unicode.IsUpper(r)
}

func isLower(r rune) bool {
	return unicode.IsLower(r)
}

// buildUpdateQuery dynamically builds a SQL UPDATE statement from a struct (SeparateTableStrategy only)
func buildUpdateQuery(table string, s interface{}) (string, []interface{}, error) {
	v := reflect.ValueOf(s)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return "", nil, fmt.Errorf("expected a struct")
	}

	var setClauses []string
	var values []interface{}

	t := v.Type()
	var idValue interface{}
	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		fieldName := toSnakeCase(field.Name)
		value := v.Field(i).Interface()

		if fieldName == "id" {
			idValue = value
			continue
		}

		setClauses = append(setClauses, fmt.Sprintf("%s = $%d", fieldName, len(values)+1))
		values = append(values, value)
	}

	if idValue == nil {
		return "", nil, fmt.Errorf("missing id field")
	}

	setClause := strings.Join(setClauses, ", ")
	values = append(values, idValue)
	query := fmt.Sprintf("UPDATE %s SET %s WHERE id = $%d;",
		table,
		setClause,
		len(values))
	return query, values, nil
}
