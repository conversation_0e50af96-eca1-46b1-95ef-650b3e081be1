{"name": "datakit-samples-bitemporality", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/go/datakit-samples/bitemporality", "tags": ["go", "sample", "bitemporality", "pgx", "dapr"], "targets": {"docker:up": {"executor": "nx:run-commands", "options": {"command": "docker-compose up -d", "cwd": "{projectRoot}"}, "metadata": {"description": "Start Docker containers for the bitemporality sample"}}, "docker:down": {"executor": "nx:run-commands", "options": {"command": "docker-compose down -v", "cwd": "{projectRoot}"}, "metadata": {"description": "Stop and remove Docker containers and volumes for the bitemporality sample"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "go run .", "cwd": "{projectRoot}"}}, "test": {"executor": "nx:run-commands", "options": {"command": "go test ./...", "cwd": "{projectRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "go fmt ./...", "cwd": "{projectRoot}"}, "metadata": {"description": "Run go fmt for code formatting"}}}}