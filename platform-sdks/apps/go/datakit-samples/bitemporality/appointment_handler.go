package main

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// AppointmentHandler handles API requests for appointments
type AppointmentHandler struct {
	repo   *StandardRepository[Appointment]
	logger *zap.Logger
}

// NewAppointmentHandler creates a new AppointmentHandler
func NewAppointmentHandler(repo *StandardRepository[Appointment], logger *zap.Logger) *AppointmentHandler {
	return &AppointmentHandler{
		repo:   repo,
		logger: logger,
	}
}

type CreateAppointmentRequest struct {
	PatientID       string    `json:"patientId" binding:"required"`
	AppointmentDate time.Time `json:"appointmentDate" binding:"required"`
	Description     string    `json:"description"`
}

func (h *AppointmentHandler) CreateAppointment(c *gin.Context) {
	var req CreateAppointmentRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	appt := NewAppointment(req.PatientID, req.Description, req.AppointmentDate)

	createdAppt, err := h.repo.Create(c.Request.Context(), "appointments", *appt)
	if err != nil {
		h.logger.Error("failed to create appointment", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create appointment"})
		return
	}

	c.JSON(http.StatusCreated, createdAppt)
}

func (h *AppointmentHandler) GetAppointmentsForPatient(c *gin.Context) {
	patientID := c.Param("patientId")

	appointments, err := h.repo.FindBy(c.Request.Context(), "appointments", "patient_id = $1", patientID)
	if err != nil {
		h.logger.Error("failed to get appointments", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get appointments"})
		return
	}

	c.JSON(http.StatusOK, appointments)
}
