package main

import (
	"context"
	"fmt"

	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

// StandardRepository is a generic repository for non-bitemporal entities
type StandardRepository[T any] struct {
	pool   *pgxpool.Pool
	logger *zap.Logger
}

// NewStandardRepository creates a new StandardRepository
func NewStandardRepository[T any](pool *pgxpool.Pool, logger *zap.Logger) *StandardRepository[T] {
	return &StandardRepository[T]{
		pool:   pool,
		logger: logger,
	}
}

// Create inserts a new record into the database
func (r *StandardRepository[T]) Create(ctx context.Context, table string, entity T) (T, error) {
	// This is a simplified example; a real implementation would be more robust
	// and handle different struct tags and column names.
	query, args, err := buildInsertQuery(table, entity)
	if err != nil {
		return entity, fmt.Errorf("build insert query: %w", err)
	}

	_, err = r.pool.Exec(ctx, query, args...)
	if err != nil {
		return entity, fmt.Errorf("exec insert: %w", err)
	}
	return entity, nil
}

// FindBy queries for records based on a condition
func (r *StandardRepository[T]) FindBy(ctx context.Context, table string, condition string, args ...interface{}) ([]*T, error) {
	var entities []*T
	query := fmt.Sprintf("SELECT * FROM %s WHERE %s", table, condition)
	err := pgxscan.Select(ctx, r.pool, &entities, query, args...)
	if err != nil {
		return nil, fmt.Errorf("find by: %w", err)
	}
	return entities, nil
}
