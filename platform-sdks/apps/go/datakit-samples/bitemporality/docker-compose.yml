version: '3.8'

services:
  db:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: bitemporality
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data

  migrate:
    image: migrate/migrate:v4.16.2
    depends_on:
      - db
    volumes:
      - ./migrations:/migrations
    entrypoint: ["/bin/sh", "-c"]
    command:
      - |
        ls -l /migrations
        until nc -z db 5432; do
          echo 'Waiting for Postgres...'; sleep 2;
        done
        migrate -path=/migrations -database ************************************/bitemporality?sslmode=disable up

volumes:
  db_data: 