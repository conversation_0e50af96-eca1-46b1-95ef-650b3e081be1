-- Create providers table for bitemporal data
CREATE TABLE IF NOT EXISTS providers (
    id TEXT PRIMARY KEY,
    version BIGINT NOT NULL,
    valid_start_date TIMESTAMPTZ NOT NULL,
    valid_end_date TIMESTAMPTZ,
    system_start_date TIMESTAMPTZ NOT NULL,
    system_end_date TIMESTAMPTZ,
    name TEXT NOT NULL,
    specialty TEXT NOT NULL
);

-- Create historical table for providers
CREATE TABLE IF NOT EXISTS providers_historical (
    id TEXT,
    version BIGINT NOT NULL,
    valid_start_date TIMESTAMPTZ NOT NULL,
    valid_end_date TIMESTAMPTZ,
    system_start_date TIMESTAMPTZ NOT NULL,
    system_end_date TIMESTAMPTZ,
    name TEXT NOT NULL,
    specialty TEXT NOT NULL
);

CREATE INDEX IF NOT EXISTS idx_providers_valid ON providers (valid_start_date, valid_end_date);
CREATE INDEX IF NOT EXISTS idx_providers_system ON providers (system_start_date, system_end_date);
CREATE INDEX IF NOT EXISTS idx_providers_historical_valid ON providers_historical (valid_start_date, valid_end_date);
CREATE INDEX IF NOT EXISTS idx_providers_historical_system ON providers_historical (system_start_date, system_end_date); 