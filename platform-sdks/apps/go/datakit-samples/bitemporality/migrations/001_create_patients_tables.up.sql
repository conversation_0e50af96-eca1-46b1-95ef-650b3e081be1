-- Create main patients table
CREATE TABLE IF NOT EXISTS patients (
    id TEXT PRIMARY KEY,
    version BIGINT NOT NULL,
    valid_start_date TIMESTAMPTZ NOT NULL,
    valid_end_date TIMESTAMPTZ,
    system_start_date TIMESTAMPTZ NOT NULL,
    system_end_date TIMESTAMPTZ,
    active BOOLEAN NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    gender TEXT NOT NULL,
    birth_date TEXT NOT NULL,
    phone_number TEXT,
    email TEXT,
    address TEXT
);

-- Create historical table for bitemporal records
CREATE TABLE IF NOT EXISTS patients_historical (
    id TEXT,
    version BIGINT NOT NULL,
    valid_start_date TIMESTAMPTZ NOT NULL,
    valid_end_date TIMESTAMPTZ,
    system_start_date TIMESTAMPTZ NOT NULL,
    system_end_date TIMESTAMPTZ,
    active BOOLEAN NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    gender TEXT NOT NULL,
    birth_date TEXT NOT NULL,
    phone_number TEXT,
    email TEXT,
    address TEXT
);

CREATE INDEX IF NOT EXISTS idx_patients_valid ON patients (valid_start_date, valid_end_date);
CREATE INDEX IF NOT EXISTS idx_patients_system ON patients (system_start_date, system_end_date);
CREATE INDEX IF NOT EXISTS idx_patients_historical_valid ON patients_historical (valid_start_date, valid_end_date);
CREATE INDEX IF NOT EXISTS idx_patients_historical_system ON patients_historical (system_start_date, system_end_date); 