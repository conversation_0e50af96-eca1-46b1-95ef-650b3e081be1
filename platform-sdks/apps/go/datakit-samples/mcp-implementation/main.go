package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/v0/cqrs"
	"github.com/gin-contrib/cors"
)

func main() {
	// --- Infrastructure ---
	patientRepo := NewInMemoryPatientRepository()
	patientService := NewPatientService(patientRepo)

	userRepo := NewInMemoryUserRepository()
	userService := NewUserService(userRepo)

	// Create CQRS engine with HTTP transport on :8080
	engine := cqrs.NewEngine("mcp-implementation", cqrs.WithHTTP(8080))

	// Enable CORS so Cursor (in-browser) can connect to SSE and MCP endpoints
	g := engine.Gin()
	g.Use(cors.New(cors.Config{
		AllowAllOrigins:  true,
		AllowHeaders:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowCredentials: false,
	}))

	// Register patient command with MCP metadata
	if err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &CreatePatientCommand{},
		Handler: patientService.CreatePatientHandler,
		Metadata: cqrs.Metadata{
			Version:     "1.0",
			Description: "Creates a patient aggregate",
			MCP: &cqrs.MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "command",
				RequestSchemaRef:  "#/components/schemas/CreatePatientRequest",
				ResponseSchemaRef: "#/components/schemas/CreatePatientResponse",
			},
		},
	}); err != nil {
		log.Fatalf("command registration failed: %v", err)
	}

	// Register patient query with MCP metadata
	if err := engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &GetPatientQuery{},
		Handler: patientService.GetPatientHandler,
		Metadata: cqrs.Metadata{
			Version:     "1.0",
			Description: "Get patient by ID",
			MCP: &cqrs.MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "query",
				RequestSchemaRef:  "#/components/schemas/GetPatientRequest",
				ResponseSchemaRef: "#/components/schemas/GetPatientResponse",
			},
		},
	}); err != nil {
		log.Fatalf("query registration failed: %v", err)
	}

	// Register user command with MCP metadata
	if err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &CreateUserCommand{},
		Handler: userService.CreateUserHandler,
		Metadata: cqrs.Metadata{
			Version:     "1.0",
			Description: "Creates a user aggregate",
			MCP: &cqrs.MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "command",
				RequestSchemaRef:  "#/components/schemas/CreateUserRequest",
				ResponseSchemaRef: "#/components/schemas/CreateUserResponse",
			},
		},
	}); err != nil {
		log.Fatalf("user command registration failed: %v", err)
	}

	// Register user query with MCP metadata
	if err := engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &GetUserQuery{},
		Handler: userService.GetUserHandler,
		Metadata: cqrs.Metadata{
			Version:     "1.0",
			Description: "Get user by ID",
			MCP: &cqrs.MCP{
				ContextType:       "aggregate",
				Version:           "1.0.0",
				OperationCategory: "query",
				RequestSchemaRef:  "#/components/schemas/GetUserRequest",
				ResponseSchemaRef: "#/components/schemas/GetUserResponse",
			},
		},
	}); err != nil {
		log.Fatalf("user query registration failed: %v", err)
	}

	// MCP endpoints are now automatically created by the SDK based on registered commands/queries with MCP metadata

	// --- Start engine ---
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	if err := engine.Start(ctx); err != nil {
		log.Fatalf("engine failed: %v", err)
	}

	<-ctx.Done()
}
