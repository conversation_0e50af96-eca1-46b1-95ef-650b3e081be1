package main

import (
	"context"
	"errors"
	"sync"
)

// InMemoryPatientRepository stores patients in RAM for demo/testing purposes.
type InMemoryPatientRepository struct {
	mu       sync.RWMutex
	patients map[string]*Patient
}

func NewInMemoryPatientRepository() *InMemoryPatientRepository {
	return &InMemoryPatientRepository{patients: make(map[string]*Patient)}
}

// InMemoryUserRepository stores users in RAM for demo/testing purposes.
type InMemoryUserRepository struct {
	mu    sync.RWMutex
	users map[string]*User
}

func NewInMemoryUserRepository() *InMemoryUserRepository {
	return &InMemoryUserRepository{users: make(map[string]*User)}
}

func (r *InMemoryPatientRepository) Save(p *Patient) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.patients[p.ID] = p
}

func (r *InMemoryPatientRepository) Get(id string) (*Patient, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	if p, ok := r.patients[id]; ok {
		return p, nil
	}
	return nil, errors.New("patient not found")
}

func (r *InMemoryPatientRepository) List() []*Patient {
	r.mu.RLock()
	defer r.mu.RUnlock()
	patients := make([]*Patient, 0, len(r.patients))
	for _, p := range r.patients {
		patients = append(patients, p)
	}
	return patients
}

func (r *InMemoryUserRepository) Save(u *User) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.users[u.ID] = u
}

func (r *InMemoryUserRepository) Get(id string) (*User, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	if u, ok := r.users[id]; ok {
		return u, nil
	}
	return nil, errors.New("user not found")
}

func (r *InMemoryUserRepository) List() []*User {
	r.mu.RLock()
	defer r.mu.RUnlock()
	users := make([]*User, 0, len(r.users))
	for _, u := range r.users {
		users = append(users, u)
	}
	return users
}

// PatientService groups command & query handlers.
type PatientService struct {
	repo *InMemoryPatientRepository
}

func NewPatientService(repo *InMemoryPatientRepository) *PatientService {
	return &PatientService{repo: repo}
}

// UserService groups command & query handlers.
type UserService struct {
	repo *InMemoryUserRepository
}

func NewUserService(repo *InMemoryUserRepository) *UserService {
	return &UserService{repo: repo}
}

// ---- Command handler ----
func (s *PatientService) CreatePatientHandler(ctx context.Context, cmd *CreatePatientCommand) error {
	// Ensure defaults
	cmd.SetDefaults()
	patient := &Patient{
		ID:   cmd.ID,
		Name: cmd.PatientName,
		Age:  cmd.Age,
	}
	s.repo.Save(patient)
	return nil
}

// ---- Query handler ----
func (s *PatientService) GetPatientHandler(ctx context.Context, qry *GetPatientQuery) (interface{}, error) {
	return s.repo.Get(qry.ID)
}

// ---- User Command handler ----
func (s *UserService) CreateUserHandler(ctx context.Context, cmd *CreateUserCommand) error {
	// Ensure defaults
	cmd.SetDefaults()
	user := &User{
		ID:    cmd.ID,
		Name:  cmd.UserName,
		Email: cmd.Email,
		Age:   cmd.Age,
	}
	s.repo.Save(user)
	return nil
}

// ---- User Query handler ----
func (s *UserService) GetUserHandler(ctx context.Context, qry *GetUserQuery) (interface{}, error) {
	return s.repo.Get(qry.ID)
}
