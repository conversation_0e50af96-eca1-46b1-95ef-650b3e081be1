package main

import "github.com/google/uuid"

// User represents a simple user aggregate.
type User struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Email string `json:"email"`
	Age   int    `json:"age"`
}

// ---- Commands ----

// CreateUserCommand instructs the system to create a new user.
type CreateUserCommand struct {
	ID       string `json:"id"`
	UserName string `json:"userName" binding:"required"`
	Email    string `json:"email" binding:"required"`
	Age      int    `json:"age" binding:"required"`
}

func (c *CreateUserCommand) Name() string { return "CreateUserCommand" }

// GetID returns the user ID for MCP response formatting
func (c *CreateUserCommand) GetID() string {
	return c.ID
}

// SetDefaults ensures ID is populated if omitted.
func (c *CreateUserCommand) SetDefaults() {
	if c.ID == "" {
		c.ID = uuid.NewString()
	}
}

// ---- Queries ----

// GetUserQuery fetches a user by its ID.
type GetUserQuery struct {
	ID string `json:"id" uri:"id" binding:"required"`
}

func (q *GetUserQuery) Name() string { return "GetUserByIDQuery" }
