package main

import "github.com/google/uuid"

// Patient represents a simple patient aggregate.
type Patient struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Age  int    `json:"age"`
}

// ---- Commands ----

// CreatePatientCommand instructs the system to create a new patient.
type CreatePatientCommand struct {
	ID          string `json:"id"`
	PatientName string `json:"patientName" binding:"required"`
	Age         int    `json:"age" binding:"required"`
}

func (c *CreatePatientCommand) Name() string { return "CreatePatientCommand" }

// GetID returns the patient ID for MCP response formatting
func (c *CreatePatientCommand) GetID() string {
	return c.ID
}

// SetDefaults ensures ID is populated if omitted.
func (c *CreatePatientCommand) SetDefaults() {
	if c.ID == "" {
		c.ID = uuid.NewString()
	}
}

// ---- Queries ----

// GetPatientQuery fetches a patient by its ID.
type GetPatientQuery struct {
	ID string `json:"id" uri:"id" binding:"required"`
}

func (q *GetPatientQuery) Name() string { return "GetPatientByIDQuery" }
