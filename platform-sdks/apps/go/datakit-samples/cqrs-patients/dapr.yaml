version: 1
common:
  resourcesPath: ./components/
  daprdFlags:
    - "--placement-host-address="
    - "--scheduler-host-address="
apps:
  - appID: notification-service
    appPort: 8081
    appProtocol: http
    daprHTTPPort: 3500
    daprGRPCPort: 50001
    logLevel: debug
    appDirPath: ./notification-service
    command: ["go", "run", "."]
  - appID: registration-service
    appPort: 8080
    appProtocol: http
    daprHTTPPort: 3501
    daprGRPCPort: 50002
    logLevel: debug
    appDirPath: ./registration-service
    command: ["go", "run", "."]
