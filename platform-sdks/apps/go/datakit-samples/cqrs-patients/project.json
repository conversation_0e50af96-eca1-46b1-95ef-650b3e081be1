{"name": "cqrs-patients", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/go/datakit-samples/cqrs-patients", "tags": ["go", "sample", "cqrs", "dapr"], "targets": {"docker:up": {"executor": "nx:run-commands", "options": {"command": "docker-compose up -d", "cwd": "{projectRoot}"}, "metadata": {"description": "Start Docker containers (<PERSON><PERSON><PERSON>, Zookeeper) in detached mode for the CQRS patients sample"}}, "docker:down": {"executor": "nx:run-commands", "options": {"command": "docker-compose down -v", "cwd": "{projectRoot}"}, "metadata": {"description": "Stop and remove Docker containers and volumes for the CQRS patients sample"}}, "wait-kafka": {"executor": "nx:run-commands", "options": {"command": "bash -c 'echo \"Waiting for <PERSON><PERSON><PERSON> to be ready...\"; count=0; max_attempts=30; while [ $count -lt $max_attempts ]; do if docker exec kafka kafka-topics --bootstrap-server localhost:9092 --list >/dev/null 2>&1; then echo \"✓ Kafka is ready!\"; exit 0; fi; count=$((count + 1)); echo \"Waiting for Kafka... ($count/$max_attempts)\"; sleep 2; done; echo \"✗ Kafka failed to start within 60 seconds\"; exit 1'", "cwd": "{projectRoot}"}, "metadata": {"description": "Wait for <PERSON><PERSON><PERSON> to be ready and responsive (up to 60 seconds)"}}, "dapr:init": {"executor": "nx:run-commands", "options": {"command": "dapr init || echo 'Dapr already initialized or init failed - continuing...'"}, "metadata": {"description": "Initialize Dapr runtime components (only needed once per system)"}}, "start": {"executor": "nx:run-commands", "options": {"commands": ["nx docker:up cqrs-patients", "nx wait-kafka cqrs-patients", "nx dapr:init cqrs-patients", "sleep 5", "dapr run -f dapr.yaml"], "cwd": "{projectRoot}", "parallel": false}, "metadata": {"description": "Start the complete CQRS patients sample: Docker services, Kafka, Dapr, and microservices"}}, "stop": {"executor": "nx:run-commands", "options": {"commands": ["dapr stop --app-id registration-service || true", "dapr stop --app-id notification-service || true", "nx docker:down cqrs-patients"], "parallel": false}, "metadata": {"description": "Stop all Dapr applications and Docker containers for the CQRS patients sample"}}, "cleanup": {"executor": "nx:run-commands", "options": {"command": "bash -c 'pkill -f \"dapr run\" || true; pkill -f \"daprd\" || true; lsof -t -i:3500 | xargs kill -9 2>/dev/null || true; lsof -t -i:3501 | xargs kill -9 2>/dev/null || true; lsof -t -i:50001 | xargs kill -9 2>/dev/null || true; lsof -t -i:50002 | xargs kill -9 2>/dev/null || true; lsof -t -i:8080 | xargs kill -9 2>/dev/null || true; lsof -t -i:8081 | xargs kill -9 2>/dev/null || true; rm -rf .dapr/logs/* || true; rm -rf registration-service/.dapr/logs/* || true; rm -rf notification-service/.dapr/logs/* || true'", "cwd": "{projectRoot}"}, "metadata": {"description": "Force cleanup of all Dapr processes, ports, and log files (use when normal stop fails)"}}, "test:create-patient": {"executor": "nx:run-commands", "options": {"command": "curl -X POST http://localhost:8080/api/v1/patients -H \"Content-Type: application/json\" -d '{\"patientName\":\"<PERSON>\",\"email\":\"<EMAIL>\",\"phone\":\"************\",\"address\":\"123 Main St\"}' -w \"\\nHTTP Status: %{http_code}\\n\""}, "metadata": {"description": "Test creating a new patient via REST API (requires services to be running)"}}, "test:list-notifications": {"executor": "nx:run-commands", "options": {"command": "curl -X GET http://localhost:8081/api/v1/notifications -H \"Content-Type: application/json\" -w \"\\nHTTP Status: %{http_code}\\n\""}, "metadata": {"description": "Test retrieving notifications via REST API (requires services to be running)"}}, "test:flow": {"executor": "nx:run-commands", "options": {"command": "bash -c 'echo \"=== Testing Complete Patient Flow ===\"; echo \"1. Creating patient...\"; curl -X POST http://localhost:8080/api/v1/patients -H \"Content-Type: application/json\" -d \"{\\\"patientName\\\":\\\"<PERSON>\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"phone\\\":\\\"************\\\",\\\"address\\\":\\\"789 Test Ave\\\"}\" -w \"\\nHTTP Status: %{http_code}\\n\"; echo \"\"; echo \"2. Waiting for event processing...\"; sleep 3; echo \"\"; echo \"3. Checking notifications...\"; curl -X GET http://localhost:8081/api/v1/notifications -H \"Content-Type: application/json\" -w \"\\nHTTP Status: %{http_code}\\n\"'"}, "metadata": {"description": "Test the complete patient registration flow: create patient → event processing → notification generation"}}, "status": {"executor": "nx:run-commands", "options": {"command": "bash -c 'echo \"=== Docker Services ===\"; docker-compose ps; echo \"\"; echo \"=== Dapr Applications ===\"; dapr list || echo \"No Dapr apps running\"; echo \"\"; echo \"=== Port Usage ===\"; lsof -i :3500 || echo \"Port 3500 not in use\"; lsof -i :3501 || echo \"Port 3501 not in use\"; lsof -i :8080 || echo \"Port 8080 not in use\"; lsof -i :8081 || echo \"Port 8081 not in use\"'", "cwd": "{projectRoot}"}, "metadata": {"description": "Check the status of Docker services, Dapr applications, and port usage"}}, "health": {"executor": "nx:run-commands", "options": {"command": "bash -c 'echo \"=== Health Check ===\"; echo \"Registration Service Health:\"; curl -s http://localhost:8080/health || echo \"Registration service not responding\"; echo \"\"; echo \"Notification Service Health:\"; curl -s http://localhost:8081/health || echo \"Notification service not responding\"'"}, "metadata": {"description": "Check the health status of both registration and notification services"}}, "lint": {"executor": "nx:run-commands", "options": {"commands": ["nx run registration-service:lint", "nx run notification-service:lint"]}, "metadata": {"description": "Run lint for all child Go services"}}, "test": {"executor": "nx:run-commands", "options": {"commands": ["nx run registration-service:test", "nx run notification-service:test"]}, "metadata": {"description": "Run tests for all child Go services"}}, "build": {"executor": "nx:run-commands", "options": {"commands": ["nx run registration-service:build", "nx run notification-service:build"]}, "metadata": {"description": "Build all child Go services"}}}}