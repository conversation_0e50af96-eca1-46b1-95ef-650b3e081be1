package main

import "time"

// CreateNotificationCommand represents a command to create a notification
type CreateNotificationCommand struct {
	PatientID string `json:"patientId" binding:"required"`
	Type      string `json:"type" binding:"required"`
	Title     string `json:"title" binding:"required"`
	Message   string `json:"message" binding:"required"`
}

// Name implements cqrs.Named interface - follows naming convention
func (c *CreateNotificationCommand) Name() string {
	return "CreateNotificationCommand" // Auto-generates: POST /api/v1/notifications
}

// UpdateNotificationStatusCommand represents a command to update notification status
type UpdateNotificationStatusCommand struct {
	ID     string `uri:"id" binding:"required"`      // From path parameter
	Status string `json:"status" binding:"required"` // From JSON body
}

// Name implements cqrs.Named interface
func (c *UpdateNotificationStatusCommand) Name() string {
	return "UpdateNotificationCommand" // Auto-generates: PUT /api/v1/notifications/:id
}

// ProcessPatientEventCommand represents a command triggered by patient events
// This matches the structure of PatientRegisteredEvent from registration service
type ProcessPatientEventCommand struct {
	PatientID    string    `json:"patientId"`
	PatientName  string    `json:"patientName"`
	Email        string    `json:"email"`
	Phone        string    `json:"phone"`
	Address      string    `json:"address"`
	RegisteredAt time.Time `json:"registeredAt"`
}

// Name implements cqrs.Named interface
func (c *ProcessPatientEventCommand) Name() string {
	return "ProcessPatientEventCommand" // Will be used for PubSub only
}
