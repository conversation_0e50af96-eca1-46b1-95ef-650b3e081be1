{"name": "notification-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/go/datakit-samples/cqrs-patients/notification-service", "tags": ["go", "sample", "cqrs", "dapr", "microservice"], "targets": {"test": {"executor": "@nx-go/nx-go:test", "metadata": {"description": "Run unit tests for the notification service using Go test framework"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "go fmt ./...", "cwd": "{projectRoot}"}, "metadata": {"description": "Run go fmt for code formatting"}}, "tidy": {"executor": "nx:run-commands", "options": {"command": "go mod tidy", "cwd": "{projectRoot}"}, "metadata": {"description": "Run go mod tidy to clean up Go module dependencies"}}, "build": {"executor": "nx:run-commands", "options": {"command": "bash -c 'go build -o notification-service .'", "cwd": "{projectRoot}"}, "metadata": {"description": "Build the notification service Go binary"}}}}