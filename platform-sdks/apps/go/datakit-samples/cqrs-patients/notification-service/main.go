package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/v0/cqrs"
)

func main() {
	fmt.Println("🔔 Patient Notification Service")
	fmt.Println("================================")

	// Create service
	service := NewNotificationService()

	// Create CQRS engine with HTTP and PubSub
	engine := cqrs.NewEngine("patient-notification-service",
		cqrs.WithHTTP(8081),
		cqrs.WithSubscriber("kafka-pubsub", "http://localhost:3500", 9081),
	)

	// Register commands - both HTTP and PubSub
	err := engine.RegisterCommands(
		// Auto-generated: POST /api/v1/notifications (from CreateNotificationCommand)
		cqrs.CommandConfig{
			Command: &CreateNotificationCommand{},
			Handler: service.CreateNotificationHandler, // Concrete handler
			Metadata: cqrs.Metadata{
				Version:     "1.0",
				Description: "Create a new notification",
				Tags:        []string{"notification", "create"},
				Annotations: map[string]interface{}{
					"rateLimit":     "200/minute",
					"requiredRoles": []string{"staff", "admin"},
				},
				// REST: auto-generated as POST /api/v1/notifications
			},
		},

		// PubSub only - processes patient events from Kafka
		cqrs.CommandConfig{
			Command: &ProcessPatientEventCommand{},
			Handler: service.ProcessPatientEventHandler, // Concrete handler
			Metadata: cqrs.Metadata{
				Version:     "1.0",
				Description: "Process patient registration events",
				Tags:        []string{"patient", "event", "notification"},
				PubSub: &cqrs.PubSub{
					Topic: "patients",
					// No event filter - process all patient events
				},
				Annotations: map[string]interface{}{
					"eventSource": "patient-registration-service",
					"retryCount":  3,
				},
			},
		},
	)

	if err != nil {
		log.Fatalf("Failed to register commands: %v", err)
	}

	// Register queries with auto-generation
	err = engine.RegisterQueries(
		// Auto-generated: GET /api/v1/notifications (from GetNotificationsQuery)
		cqrs.QueryConfig{
			Query:   &GetNotificationsQuery{},
			Handler: service.GetNotificationsHandler, // Concrete handler
			Metadata: cqrs.Metadata{
				Version:     "1.0",
				Description: "Get all notifications with pagination",
				Tags:        []string{"notification", "list"},
				Annotations: map[string]interface{}{
					"cacheable":    true,
					"cacheTimeout": "30s",
				},
				// REST: auto-generated as GET /api/v1/notifications
			},
		},

		// Custom path for patient-specific notifications
		cqrs.QueryConfig{
			Query:   &GetNotificationByPatientIDQuery{},
			Handler: service.GetNotificationByPatientIDHandler, // Concrete handler
			Metadata: cqrs.Metadata{
				Version:     "1.0",
				Description: "Get notifications for a specific patient",
				Tags:        []string{"notification", "patient"},
				REST: &cqrs.REST{
					Method: "GET",
					Path:   "/api/v1/notifications/patient/:patientId",
				},
				Annotations: map[string]interface{}{
					"cacheable":    true,
					"cacheTimeout": "60s",
				},
			},
		},
	)

	if err != nil {
		log.Fatalf("Failed to register queries: %v", err)
	}

	// Start engine with comprehensive logging
	ctx := context.Background()
	if err := engine.Start(ctx); err != nil {
		log.Fatalf("Failed to start engine: %v", err)
	}

	fmt.Println("\n📋 Available Endpoints:")
	fmt.Println("  • POST /api/v1/notifications                    - Create notification")
	fmt.Println("  • GET  /api/v1/notifications                    - Get all notifications")
	fmt.Println("  • GET  /api/v1/notifications/patient/:patientId - Get notifications by patient")
	fmt.Println("  • GET  /health                                  - Health check")
	fmt.Println("")
	fmt.Println("📡 PubSub Subscriptions:")
	fmt.Println("  • Topic: patients                               - Process patient events")
	fmt.Println("")
	fmt.Println("📝 Example Usage:")
	fmt.Println("  curl http://localhost:8081/api/v1/notifications?limit=5&offset=0")

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
	<-sigChan

	fmt.Println("\n🛑 Shutting down service...")
}
