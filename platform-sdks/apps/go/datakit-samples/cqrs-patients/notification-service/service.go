package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
)

// NotificationService handles notification business logic
type NotificationService struct {
	mu            sync.RWMutex
	notifications map[string]*Notification
}

// NewNotificationService creates a new notification service
func NewNotificationService() *NotificationService {
	return &NotificationService{
		notifications: make(map[string]*Notification),
	}
}

// ProcessPatientEventHandler handles patient events and creates notifications with concrete type
func (s *NotificationService) ProcessPatientEventHandler(ctx context.Context, cmd *ProcessPatientEventCommand) error {
	// Direct access to command fields - no type assertion needed!

	fmt.Printf("Processing patient event: PatientID=%s, PatientName=%s, Email=%s\n",
		cmd.PatientID, cmd.PatientName, cmd.Email)

	// Create notification based on event
	notification := &Notification{
		ID:        uuid.New().String(),
		PatientID: cmd.PatientID,
		Type:      "patient_registered",
		Title:     "Patient Registration",
		Message:   fmt.Sprintf("New patient registered: %s (%s)", cmd.PatientName, cmd.Email),
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	// Store notification
	s.mu.Lock()
	s.notifications[notification.ID] = notification
	s.mu.Unlock()

	fmt.Printf("Created notification: %s for patient %s\n", notification.ID, cmd.PatientID)

	// In a real system, you might send email, SMS, push notification, etc.
	// For now, just log it
	fmt.Printf("Notification sent: %s\n", notification.Message)

	return nil
}

// CreateNotificationHandler handles creation of notifications with concrete type
func (s *NotificationService) CreateNotificationHandler(ctx context.Context, cmd *CreateNotificationCommand) error {
	// Direct access to command fields - no type assertion needed!

	notification := &Notification{
		ID:        uuid.New().String(),
		PatientID: cmd.PatientID,
		Type:      cmd.Type,
		Title:     cmd.Title,
		Message:   cmd.Message,
		Status:    "pending",
		CreatedAt: time.Now(),
	}

	// Store notification
	s.mu.Lock()
	s.notifications[notification.ID] = notification
	s.mu.Unlock()

	fmt.Printf("Created notification: %s for patient %s\n", notification.ID, cmd.PatientID)
	return nil
}

// GetNotificationsHandler handles query to get all notifications with concrete type
func (s *NotificationService) GetNotificationsHandler(ctx context.Context, qry *GetNotificationsQuery) (interface{}, error) {
	// Direct access to query fields - no type assertion needed!

	s.mu.RLock()
	defer s.mu.RUnlock()

	// Set defaults
	limit := qry.Limit
	if limit <= 0 {
		limit = 10 // Default limit
	}
	offset := qry.Offset
	if offset < 0 {
		offset = 0
	}

	// Get all notifications and apply pagination
	allNotifications := make([]*Notification, 0, len(s.notifications))
	for _, notification := range s.notifications {
		allNotifications = append(allNotifications, notification)
	}

	// Apply pagination
	total := len(allNotifications)
	start := offset
	if start > total {
		start = total
	}
	end := start + limit
	if end > total {
		end = total
	}

	var notifications []*Notification
	if start < total {
		notifications = allNotifications[start:end]
	} else {
		notifications = []*Notification{}
	}

	fmt.Printf("Retrieved %d notifications (limit=%d, offset=%d, total=%d)\n",
		len(notifications), limit, offset, total)

	return map[string]interface{}{
		"notifications": notifications,
		"total":         total,
		"limit":         limit,
		"offset":        offset,
	}, nil
}

// GetNotificationByPatientIDHandler handles query to get notifications by patient ID with concrete type
func (s *NotificationService) GetNotificationByPatientIDHandler(ctx context.Context, qry *GetNotificationByPatientIDQuery) (interface{}, error) {
	// Direct access to query fields - no type assertion needed!

	if qry.PatientID == "" {
		return nil, fmt.Errorf("patient ID is required")
	}

	s.mu.RLock()
	defer s.mu.RUnlock()

	var patientNotifications []*Notification
	for _, notification := range s.notifications {
		if notification.PatientID == qry.PatientID {
			patientNotifications = append(patientNotifications, notification)
		}
	}

	fmt.Printf("Retrieved %d notifications for patient %s\n", len(patientNotifications), qry.PatientID)

	return map[string]interface{}{
		"notifications": patientNotifications,
		"patientId":     qry.PatientID,
		"total":         len(patientNotifications),
	}, nil
}
