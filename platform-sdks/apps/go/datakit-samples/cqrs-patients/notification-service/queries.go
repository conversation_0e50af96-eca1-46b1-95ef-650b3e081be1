package main

import "time"

// GetNotificationsQuery represents a query to get all notifications with pagination
type GetNotificationsQuery struct {
	Limit  int `form:"limit"`  // Query parameter: ?limit=10
	Offset int `form:"offset"` // Query parameter: ?offset=0
}

// Name implements cqrs.Named interface - follows naming convention
func (q *GetNotificationsQuery) Name() string {
	return "GetNotificationsQuery" // Auto-generates: GET /api/v1/notifications
}

// GetNotificationByPatientIDQuery represents a query to get notifications for a patient
type GetNotificationByPatientIDQuery struct {
	PatientID string `uri:"patientId" binding:"required"` // From path parameter /:patientId
}

// Name implements cqrs.Named interface
func (q *GetNotificationByPatientIDQuery) Name() string {
	return "GetNotificationByPatientIDQuery" // Auto-generates: GET /api/v1/notifications/:patientId (needs custom path)
}

// Notification represents a notification entity
type Notification struct {
	ID        string    `json:"id"`
	PatientID string    `json:"patientId"`
	Type      string    `json:"type"`
	Title     string    `json:"title"`
	Message   string    `json:"message"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"createdAt"`
}
