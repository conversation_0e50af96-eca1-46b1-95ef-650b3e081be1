{"name": "registration-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/go/datakit-samples/cqrs-patients/registration-service", "tags": ["go", "sample", "cqrs", "dapr", "microservice"], "targets": {"test": {"executor": "@nx-go/nx-go:test", "metadata": {"description": "Run unit tests for the registration service using Go test framework"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "go fmt ./...", "cwd": "{projectRoot}"}, "metadata": {"description": "Run go fmt for code formatting"}}, "tidy": {"executor": "nx:run-commands", "options": {"command": "go mod tidy", "cwd": "{projectRoot}"}, "metadata": {"description": "Run go mod tidy to clean up Go module dependencies"}}, "build": {"executor": "nx:run-commands", "options": {"command": "bash -c 'go build -o registration-service .'", "cwd": "{projectRoot}"}, "metadata": {"description": "Build the registration service Go binary"}}}}