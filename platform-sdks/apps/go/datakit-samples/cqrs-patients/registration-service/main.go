package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/v0/cqrs"
)

func main() {
	fmt.Println("🏥 Patient Registration Service")
	fmt.Println("================================")

	// Create publisher
	publisher := cqrs.NewDaprPublisher("kafka-pubsub", "http://localhost:3501")

	// Create service
	service := NewPatientRegistrationService(publisher)

	// Create CQRS engine with HTTP transport and auto-generation
	engine := cqrs.NewEngine("patient-registration-service",
		cqrs.WithHTTP(8080),
	)

	// Register commands with auto-generated endpoints and concrete handlers
	err := engine.RegisterCommands(
		// Auto-generated: POST /api/v1/patients (from CreatePatientCommand)
		cqrs.CommandConfig{
			Command: &RegisterPatientCommand{},
			Handler: service.RegisterPatientHandler, // Concrete handler - no type assertions!
			Metadata: cqrs.Metadata{
				Version:     "1.0",
				Description: "Register a new patient in the system",
				Tags:        []string{"patient", "registration", "create"},
				Annotations: map[string]interface{}{
					"rateLimit":     "100/minute",
					"requiredRoles": []string{"nurse", "doctor", "admin"},
					"auditLevel":    "high",
				},
				// REST: auto-generated as POST /api/v1/patients
			},
		},

		// Auto-generated: PUT /api/v1/patients/:id (from UpdatePatientCommand)
		cqrs.CommandConfig{
			Command: &UpdatePatientCommand{},
			Handler: service.UpdatePatientHandler, // Concrete handler
			Metadata: cqrs.Metadata{
				Version:     "1.0",
				Description: "Update existing patient information",
				Tags:        []string{"patient", "update"},
				Annotations: map[string]interface{}{
					"rateLimit":     "50/minute",
					"requiredRoles": []string{"nurse", "doctor", "admin"},
					"auditLevel":    "medium",
				},
				// REST: auto-generated as PUT /api/v1/patients/:id
			},
		},

		// Auto-generated: DELETE /api/v1/patients/:id (from DeactivatePatientCommand)
		cqrs.CommandConfig{
			Command: &DeactivatePatientCommand{},
			Handler: service.DeactivatePatientHandler, // Concrete handler
			Metadata: cqrs.Metadata{
				Version:     "1.0",
				Description: "Deactivate a patient account",
				Tags:        []string{"patient", "deactivate", "delete"},
				Annotations: map[string]interface{}{
					"rateLimit":     "20/minute",
					"requiredRoles": []string{"doctor", "admin"},
					"auditLevel":    "high",
					"requireReason": true,
				},
				// REST: auto-generated as DELETE /api/v1/patients/:id
			},
		},
	)

	if err != nil {
		log.Fatalf("Failed to register commands: %v", err)
	}

	// Start engine with comprehensive logging
	ctx := context.Background()
	if err := engine.Start(ctx); err != nil {
		log.Fatalf("Failed to start engine: %v", err)
	}

	fmt.Println("\n📋 Available Endpoints:")
	fmt.Println("  • POST   /api/v1/patients        - Register new patient")
	fmt.Println("  • PUT    /api/v1/patients/:id    - Update patient information")
	fmt.Println("  • DELETE /api/v1/patients/:id    - Deactivate patient")
	fmt.Println("  • GET    /health                 - Health check")
	fmt.Println("")
	fmt.Println("📝 Example Usage:")
	fmt.Println("  curl -X POST http://localhost:8080/api/v1/patients \\")
	fmt.Println("    -H 'Content-Type: application/json' \\")
	fmt.Println("    -d '{\"patientName\":\"John Doe\",\"email\":\"<EMAIL>\"}'")

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
	<-sigChan

	fmt.Println("\n🛑 Shutting down service...")
}
