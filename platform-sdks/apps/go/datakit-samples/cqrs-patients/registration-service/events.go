package main

import "time"

// PatientRegisteredEvent is emitted when a patient is registered
type PatientRegisteredEvent struct {
	PatientID    string
	PatientName  string
	Email        string
	Phone        string
	Address      string
	RegisteredAt time.Time
}

// Name implements cqrs.Named interface
func (e *PatientRegisteredEvent) Name() string {
	return "patient.registered"
}

// PatientUpdatedEvent is emitted when patient information is updated
type PatientUpdatedEvent struct {
	PatientID   string
	PatientName string
	Email       string
	Phone       string
	Address     string
	UpdatedAt   time.Time
	UpdatedBy   string
}

// Name implements cqrs.Named interface
func (e *PatientUpdatedEvent) Name() string {
	return "patient.updated"
}

// PatientDeactivatedEvent is emitted when a patient is deactivated
type PatientDeactivatedEvent struct {
	PatientID     string
	Reason        string
	DeactivatedAt time.Time
	DeactivatedBy string
}

// Name implements cqrs.Named interface
func (e *PatientDeactivatedEvent) Name() string {
	return "patient.deactivated"
}
