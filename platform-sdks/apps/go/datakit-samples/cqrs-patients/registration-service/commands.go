package main

// RegisterPatientCommand represents a command to register a new patient
type RegisterPatientCommand struct {
	PatientName string `json:"patientName" binding:"required"`
	Email       string `json:"email" binding:"required,email"`
	Phone       string `json:"phone"`
	Address     string `json:"address"`
}

// Name implements cqrs.Named interface - follows naming convention for auto-generation
func (c *RegisterPatientCommand) Name() string {
	return "CreatePatientCommand" // Follows convention: CreateXCommand -> POST /patients
}

// UpdatePatientCommand represents a command to update patient information
type UpdatePatientCommand struct {
	ID          string `uri:"id" binding:"required"`  // From path parameter
	PatientName string `json:"patientName"`           // From JSON body
	Email       string `json:"email" binding:"email"` // From JSON body
	Phone       string `json:"phone"`                 // From JSON body
	Address     string `json:"address"`               // From JSON body
}

// Name implements cqrs.Named interface
func (c *UpdatePatientCommand) Name() string {
	return "UpdatePatientCommand" // Follows convention: UpdateXCommand -> PUT /patients/:id
}

// DeactivatePatientCommand represents a command to deactivate a patient
type DeactivatePatientCommand struct {
	ID     string `uri:"id" binding:"required"` // From path parameter
	Reason string `json:"reason"`               // From JSON body
}

// Name implements cqrs.Named interface
func (c *DeactivatePatientCommand) Name() string {
	return "DeactivatePatientCommand" // Follows convention: DeleteXCommand -> DELETE /patients/:id
}

// SetID allows setting ID from path parameters
func (c *DeactivatePatientCommand) SetID(id string) {
	c.ID = id
}
