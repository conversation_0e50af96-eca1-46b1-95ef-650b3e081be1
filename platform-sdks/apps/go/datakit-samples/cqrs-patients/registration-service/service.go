package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
)

// PatientRegistrationService handles patient registration business logic
type PatientRegistrationService struct {
	mu        sync.RWMutex
	patients  map[string]*Patient
	publisher publisher // For publishing events
}

// Patient represents a patient entity
type Patient struct {
	ID           string
	PatientName  string
	Email        string
	Phone        string
	Address      string
	IsActive     bool
	RegisteredAt time.Time
	UpdatedAt    time.Time
}

type publisher interface {
	PublishEvent(ctx context.Context, topic string, event interface{}) error
}

// NewPatientRegistrationService creates a new service instance
func NewPatientRegistrationService(publisher publisher) *PatientRegistrationService {
	return &PatientRegistrationService{
		patients:  make(map[string]*Patient),
		publisher: publisher,
	}
}

// RegisterPatientHandler handles patient registration command with concrete type
func (s *PatientRegistrationService) RegisterPatientHandler(ctx context.Context, cmd *RegisterPatientCommand) error {
	// No type assertion needed - direct access to command fields!

	// Validate input
	if cmd.PatientName == "" {
		return fmt.Errorf("patient name is required")
	}
	if cmd.Email == "" {
		return fmt.Errorf("email is required")
	}

	// Generate patient ID
	patientID := uuid.New().String()

	// Create patient
	patient := &Patient{
		ID:           patientID,
		PatientName:  cmd.PatientName,
		Email:        cmd.Email,
		Phone:        cmd.Phone,
		Address:      cmd.Address,
		IsActive:     true,
		RegisteredAt: time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Store patient
	s.mu.Lock()
	s.patients[patientID] = patient
	s.mu.Unlock()

	fmt.Printf("Patient registered: %s (ID: %s)\n", patient.PatientName, patient.ID)

	// Publish event
	event := &PatientRegisteredEvent{
		PatientID:    patientID,
		PatientName:  patient.PatientName,
		Email:        patient.Email,
		Phone:        patient.Phone,
		Address:      patient.Address,
		RegisteredAt: patient.RegisteredAt,
	}

	if s.publisher != nil {
		if err := s.publisher.PublishEvent(ctx, "patients", event); err != nil {
			fmt.Printf("Failed to publish PatientRegisteredEvent: %v\n", err)
			// Don't fail the command if event publishing fails
		} else {
			fmt.Printf("Published PatientRegisteredEvent for patient: %s\n", patientID)
		}
	}

	return nil
}

// UpdatePatientHandler handles patient update command with concrete type
func (s *PatientRegistrationService) UpdatePatientHandler(ctx context.Context, cmd *UpdatePatientCommand) error {
	// Direct access to command fields - no type assertion needed!

	// Validate input
	if cmd.ID == "" {
		return fmt.Errorf("patient ID is required")
	}

	s.mu.Lock()
	patient, exists := s.patients[cmd.ID]
	if !exists {
		s.mu.Unlock()
		return fmt.Errorf("patient not found: %s", cmd.ID)
	}

	// Update patient fields
	if cmd.PatientName != "" {
		patient.PatientName = cmd.PatientName
	}
	if cmd.Email != "" {
		patient.Email = cmd.Email
	}
	if cmd.Phone != "" {
		patient.Phone = cmd.Phone
	}
	if cmd.Address != "" {
		patient.Address = cmd.Address
	}
	patient.UpdatedAt = time.Now()
	s.mu.Unlock()

	fmt.Printf("Patient updated: %s (ID: %s)\n", patient.PatientName, patient.ID)

	// Publish event
	event := &PatientUpdatedEvent{
		PatientID:   patient.ID,
		PatientName: patient.PatientName,
		Email:       patient.Email,
		Phone:       patient.Phone,
		Address:     patient.Address,
		UpdatedAt:   patient.UpdatedAt,
		UpdatedBy:   "system", // In real app, would get from context
	}

	if s.publisher != nil {
		if err := s.publisher.PublishEvent(ctx, "patients", event); err != nil {
			fmt.Printf("Failed to publish PatientUpdatedEvent: %v\n", err)
		} else {
			fmt.Printf("Published PatientUpdatedEvent for patient: %s\n", patient.ID)
		}
	}

	return nil
}

// DeactivatePatientHandler handles patient deactivation command with concrete type
func (s *PatientRegistrationService) DeactivatePatientHandler(ctx context.Context, cmd *DeactivatePatientCommand) error {
	// Direct access to command fields - no type assertion needed!

	// Validate input
	if cmd.ID == "" {
		return fmt.Errorf("patient ID is required")
	}

	s.mu.Lock()
	patient, exists := s.patients[cmd.ID]
	if !exists {
		s.mu.Unlock()
		return fmt.Errorf("patient not found: %s", cmd.ID)
	}

	// Deactivate patient
	patient.IsActive = false
	patient.UpdatedAt = time.Now()
	s.mu.Unlock()

	fmt.Printf("Patient deactivated: %s (ID: %s) - Reason: %s\n",
		patient.PatientName, patient.ID, cmd.Reason)

	// Publish event
	event := &PatientDeactivatedEvent{
		PatientID:     patient.ID,
		Reason:        cmd.Reason,
		DeactivatedAt: time.Now(),
		DeactivatedBy: "system", // In real app, would get from context
	}

	if s.publisher != nil {
		if err := s.publisher.PublishEvent(ctx, "patients", event); err != nil {
			fmt.Printf("Failed to publish PatientDeactivatedEvent: %v\n", err)
		} else {
			fmt.Printf("Published PatientDeactivatedEvent for patient: %s\n", patient.ID)
		}
	}

	return nil
}
