package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
)

/* Domain Events -------------------------------------------- */

// UserCreatedEvent is published when a new user is created
type UserCreatedEvent struct {
	UserID    string    `json:"user_id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	CreatedAt time.Time `json:"created_at"`
}

func (e UserCreatedEvent) Type() string {
	return "user.created"
}

// UserUpdatedEvent is published when a user is updated
type UserUpdatedEvent struct {
	UserID    string    `json:"user_id"`
	Email     string    `json:"email"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (e UserUpdatedEvent) Type() string {
	return "user.updated"
}

// PasswordResetRequestedEvent is published when a password reset is requested
type PasswordResetRequestedEvent struct {
	UserID      string    `json:"user_id"`
	RequestedAt time.Time `json:"requested_at"`
}

func (e PasswordResetRequestedEvent) Type() string {
	return "password.reset.requested"
}

/* DTOs ----------------------------------------------------- */

// CreateUserCommand represents a command to create a new user
type CreateUserCommand struct {
	Name  string `json:"name" binding:"required"`
	Email string `json:"email" binding:"required,email"`
}

// UpdateUserCommand represents a command to update an existing user
type UpdateUserCommand struct {
	ID    string `json:"id" path:"id" binding:"required"`
	Email string `json:"email" binding:"required,email"`
}

// ResetPasswordCommand represents a command to reset a user's password
type ResetPasswordCommand struct {
	ID string `json:"id" binding:"required"`
}

// GetUserQuery represents a query to retrieve user information
type GetUserQuery struct {
	ID string `json:"id" path:"id" form:"id" binding:"required"`
}

/* Service -------------------------------------------------- */

// UserService handles user-related operations with dependency injection
type UserService struct {
	publisher *cqrs.Publisher
}

// NewUserService creates a new UserService with dependencies
func NewUserService(publisher *cqrs.Publisher) *UserService {
	return &UserService{
		publisher: publisher,
	}
}

/* Handlers ------------------------------------------------- */

// createUser handles the CreateUserCommand
func (s *UserService) createUser(ctx context.Context, c any) error {
	cmd := c.(*CreateUserCommand)
	log.Printf("CreateUser executed: Name=%s, Email=%s", cmd.Name, cmd.Email)

	// In a real implementation, you would:
	// 1. Validate the command
	// 2. Create the user in the database
	userID := "user-" + cmd.Name // Simulate generating user ID

	// 3. Publish domain event using simple PublishEvent
	event := UserCreatedEvent{
		UserID:    userID,
		Name:      cmd.Name,
		Email:     cmd.Email,
		CreatedAt: time.Now(),
	}

	if err := s.publisher.PublishEvent(ctx, "users", event); err != nil {
		log.Printf("Failed to publish UserCreatedEvent: %v", err)
		// In production, you might want to handle this differently
		// (e.g., retry, dead letter queue, etc.)
	} else {
		log.Printf("Published UserCreatedEvent for user %s", userID)
	}

	// 4. Handle any business logic
	return nil
}

// updateUser handles the UpdateUserCommand
func (s *UserService) updateUser(ctx context.Context, c any) error {
	cmd := c.(*UpdateUserCommand)
	log.Printf("UpdateUser executed: ID=%s, Email=%s", cmd.ID, cmd.Email)

	// In a real implementation, you would:
	// 1. Validate the command
	// 2. Check if user exists
	// 3. Update the user in the database

	// 4. Publish domain event using simple PublishEvent
	event := UserUpdatedEvent{
		UserID:    cmd.ID,
		Email:     cmd.Email,
		UpdatedAt: time.Now(),
	}

	if err := s.publisher.PublishEvent(ctx, "users", event); err != nil {
		log.Printf("Failed to publish UserUpdatedEvent: %v", err)
	} else {
		log.Printf("Published UserUpdatedEvent for user %s", cmd.ID)
	}

	return nil
}

// resetPassword handles the ResetPasswordCommand
func (s *UserService) resetPassword(ctx context.Context, c any) error {
	cmd := c.(*ResetPasswordCommand)
	log.Printf("ResetPassword executed: ID=%s", cmd.ID)

	// In a real implementation, you would:
	// 1. Validate the command
	// 2. Generate a new password or reset token
	// 3. Update the user's password

	// 4. Publish domain event for notification service using simple PublishEvent
	event := PasswordResetRequestedEvent{
		UserID:      cmd.ID,
		RequestedAt: time.Now(),
	}

	if err := s.publisher.PublishEvent(ctx, "notifications", event); err != nil {
		log.Printf("Failed to publish PasswordResetRequestedEvent: %v", err)
	} else {
		log.Printf("Published PasswordResetRequestedEvent for user %s", cmd.ID)
	}

	return nil
}

// getUser handles the GetUserQuery
func (s *UserService) getUser(ctx context.Context, q any) (any, error) {
	query := q.(*GetUserQuery)
	log.Printf("GetUser executed: ID=%s", query.ID)

	// In a real implementation, you would:
	// 1. Validate the query
	// 2. Retrieve user from database
	// 3. Transform to appropriate response format

	// Return mock user data
	return map[string]interface{}{
		"id":     query.ID,
		"name":   "John Doe",
		"email":  "<EMAIL>",
		"status": "active",
	}, nil
}

/* Main ----------------------------------------------------- */

func main() {

	log.Println("Starting User Service with CQRS-Dapr (cqrs)")

	// Initialize the publisher for domain events
	publisher, err := cqrs.NewPublisher("pubsub", 3501) // Use Dapr default gRPC port
	if err != nil {
		log.Fatal("Failed to create publisher:", err)
	}
	defer func() {
		if err := publisher.Close(); err != nil {
			log.Printf("Error closing publisher: %v", err)
		}
	}()

	log.Println("Publisher initialized successfully")

	// Create the UserService with dependencies
	userService := NewUserService(publisher)

	// Create the engine with multiple transport options
	engine := cqrs.NewEngine("user-service",
		cqrs.WithVersion("1.5.0"),       // Service version
		cqrs.WithGRPC(9090),             // gRPC service invocations
		cqrs.WithREST(8080),             // REST API endpoints
		cqrs.WithPubSub("pubsub", 9080), // PubSub event handling
		cqrs.WithMCP(&cqrs.MCPConfig{ // MCP protocol support
			Path:     "/mcp",
			EnableWS: false, // WebSocket not needed for MVP
		}),
	)

	// Print engine summary
	log.Println(engine.Summary())

	// COMMAND 1: gRPC only
	// This command can only be invoked via Dapr service invocation (gRPC)
	if err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &CreateUserCommand{},
		Handler: userService.createUser,
		Transport: cqrs.Transport{
			GRPC: &cqrs.GRPC{Method: "CreateUser"},
		},
		Meta: map[string]interface{}{
			"description": "Creates a new user via gRPC service invocation",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register CreateUser command:", err)
	}

	// COMMAND 2: REST + PubSub + MCP
	// This command can be triggered via REST API, PubSub events, or MCP
	if err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &UpdateUserCommand{},
		Handler: userService.updateUser,
		Transport: cqrs.Transport{
			REST:   &cqrs.REST{Method: "PUT", Path: "/api/v1/users/:id"},
			PubSub: &cqrs.PubSub{Topic: "users"},
			MCP: &cqrs.MCP{
				Description: "Change the user's email address",
				// Title and other fields omitted → won't appear in JSON
			},
		},
		Meta: map[string]interface{}{
			"description": "Updates user information via REST, PubSub, or MCP",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register UpdateUser command:", err)
	}

	// COMMAND 3: PubSub only (Event Handler)
	// This is effectively an event handler that only responds to PubSub events
	if err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &ResetPasswordCommand{},
		Handler: userService.resetPassword,
		Transport: cqrs.Transport{
			PubSub: &cqrs.PubSub{Topic: "password-resets"},
		},
		Meta: map[string]interface{}{
			"description": "Handles password reset events from PubSub",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register ResetPassword command:", err)
	}

	// QUERY: REST + MCP
	// This query can be called via REST API or MCP
	if err := engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &GetUserQuery{},
		Handler: userService.getUser,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/api/v1/users/:id"},
			MCP: &cqrs.MCP{
				Title:       "Get User",
				Description: "Retrieve user information by ID",
				OutputSchema: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"id":     map[string]string{"type": "string"},
						"name":   map[string]string{"type": "string"},
						"email":  map[string]string{"type": "string"},
						"status": map[string]string{"type": "string"},
					},
				},
			},
		},
		Meta: map[string]interface{}{
			"description": "Retrieves user information via REST API or MCP",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register GetUser query:", err)
	}

	// Start the engine
	log.Println("All handlers registered successfully. Starting engine...")

	// Demonstrate different publisher usage patterns
	demonstratePublisherUsage(publisher)

	// Create context that can be cancelled
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Set up graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start engine in a goroutine
	engineErr := make(chan error, 1)
	go func() {
		if err := engine.Start(ctx); err != nil {
			engineErr <- err
		}
	}()

	// Wait for shutdown signal or engine error
	select {
	case err := <-engineErr:
		log.Fatal("Engine failed to start:", err)
	case sig := <-sigChan:
		log.Printf("Received signal %v, shutting down gracefully...", sig)
		cancel()

		// Give some time for graceful shutdown
		log.Println("Service stopped")
		os.Exit(0)
	}
}

/* Publisher Usage Examples --------------------------------- */

// demonstratePublisherUsage shows different ways to publish events using simple PublishEvent
func demonstratePublisherUsage(pub *cqrs.Publisher) {
	ctx := context.Background()

	log.Println("=== Publisher Usage Examples ===")

	// 1. Using PublishEvent with Event interface (Simplest approach)
	userCreatedEvent := UserCreatedEvent{
		UserID:    "demo-user-123",
		Name:      "Demo User",
		Email:     "<EMAIL>",
		CreatedAt: time.Now(),
	}

	log.Println("1. Publishing with PublishEvent (simple):")
	if err := pub.PublishEvent(ctx, "users", userCreatedEvent); err != nil {
		log.Printf("   ❌ Failed: %v", err)
	} else {
		log.Printf("   ✅ Published %s event to 'users' topic", userCreatedEvent.Type())
	}

	// 2. Using PublishEvent with different event types
	userUpdatedEvent := UserUpdatedEvent{
		UserID:    "demo-user-456",
		Email:     "<EMAIL>",
		UpdatedAt: time.Now(),
	}

	log.Println("2. Publishing UserUpdatedEvent:")
	if err := pub.PublishEvent(ctx, "users", userUpdatedEvent); err != nil {
		log.Printf("   ❌ Failed: %v", err)
	} else {
		log.Printf("   ✅ Published %s event to 'users' topic", userUpdatedEvent.Type())
	}

	// 3. Using PublishEvent for notification events
	passwordResetEvent := PasswordResetRequestedEvent{
		UserID:      "demo-user-789",
		RequestedAt: time.Now(),
	}

	log.Println("3. Publishing PasswordResetRequestedEvent:")
	if err := pub.PublishEvent(ctx, "notifications", passwordResetEvent); err != nil {
		log.Printf("   ❌ Failed: %v", err)
	} else {
		log.Printf("   ✅ Published %s event to 'notifications' topic", passwordResetEvent.Type())
	}

	log.Println("=== End Publisher Examples ===")
}
