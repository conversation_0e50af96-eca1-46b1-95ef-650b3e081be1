#!/bin/bash

echo "Testing MCP Integration"
echo "======================"

BASE_URL="http://localhost:8080/mcp"

echo -e "\n1. Initialize MCP connection"
curl -s -X POST $BASE_URL \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{}}'

echo -e "\n\n2. List available tools"
curl -s -X POST $BASE_URL \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc":"2.0","id":2,"method":"tools/list"}'

echo -e "\n\n3. Call update-user tool"
curl -s -X POST $BASE_URL \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"update-user","arguments":{"id":"42","email":"<EMAIL>"}}}'

echo -e "\n\n4. Call get-user tool"
curl -s -X POST $BASE_URL \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc":"2.0","id":4,"method":"tools/call","params":{"name":"get-user","arguments":{"id":"42"}}}'

echo -e "\n" 