{"name": "datakit-samples-cqrs", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/go/datakit-samples/cqrs", "tags": ["go", "sample", "cqrs"], "targets": {"serve": {"executor": "nx:run-commands", "options": {"command": "go run .", "cwd": "{projectRoot}"}}, "test": {"executor": "nx:run-commands", "options": {"command": "go test ./...", "cwd": "{projectRoot}"}}, "lint": {"executor": "nx:run-commands", "options": {"command": "go fmt ./...", "cwd": "{projectRoot}"}, "metadata": {"description": "Run go fmt for code formatting"}}}}