# CQRS Example - User Service

This example demonstrates the complete usage of the `cqrs` library with a simple user management service that supports multiple transport mechanisms.

## Features Demonstrated

- **gRPC Service Invocation**: CreateUser command via Dapr service invocation
- **REST API**: UpdateUser command and GetUser query via HTTP endpoints  
- **PubSub Events**: UpdateUser and ResetPassword commands via event handling
- **Multiple Transports**: Single commands exposed through multiple transport mechanisms
- **Validation**: Transport configuration validation and error handling

## Commands and Queries

| Operation | Type | Transports | Description |
|-----------|------|------------|-------------|
| `CreateUser` | Command | gRPC | Creates a new user via service invocation |
| `UpdateUser` | Command | REST + PubSub | Updates user info via API or events |
| `ResetPassword` | Command | PubSub | Handles password reset events |
| `GetUser` | Query | REST | Retrieves user information |

## Running the Example

### Quick Start Options

#### Option 1: Standalone Mode (Simplest - REST only)

This runs the Go service directly without Dapr dependencies:

```bash
# Terminal 1: Start the service
go run main.go

# Terminal 2: Test the REST endpoints
curl -X GET http://localhost:8080/api/v1/users/123
curl -X PUT http://localhost:8080/api/v1/users/123 -H "Content-Type: application/json" -d '{"id":"123","email":"<EMAIL>"}'
```

*Note: You can also use `task run:standalone` and `task test:rest:only` for convenience.*

#### Option 2: With Dapr (Full Features)

**Prerequisites:**
1. **Install Dapr CLI**:
   ```bash
   # macOS
   brew install dapr/tap/dapr-cli
   
   # Or download from https://github.com/dapr/cli/releases
   ```

2. **Initialize Dapr** (one-time setup):
   ```bash
   dapr init
   ```

**Start the Service:**
```bash
# Run with Dapr (may show PubSub errors without Redis)
dapr run \
  --app-id user-service \
  --app-port 9090 \
  --app-protocol grpc \
  --dapr-grpc-port 3501 \
  --dapr-http-port 3500 \
  go run .
```

*Note: You can also use `task dapr:init` and `task run:simple` for convenience.*

### Development Convenience Commands

For convenience, you can use Task commands (requires [Task](https://taskfile.dev/) to be installed):

```bash
# See all available commands
task help

# Quick commands
task run:standalone  # Same as: go run main.go
task run:simple      # Same as: dapr run --app-id user-service --app-port 9090 --app-protocol grpc --dapr-grpc-port 3501 --dapr-http-port 3500 --log-level info go run main.go
task test:rest:only  # Test REST endpoints
task test:grpc       # Test gRPC via Dapr
task dapr:init       # Same as: dapr init
```

### Service Ports

The service will start with:
- **gRPC Service**: Port 9090 (for Dapr service invocation)
- **REST API**: Port 8080 (direct HTTP access)
- **PubSub Subscriber**: Port 9081 (for event handling)
- **Dapr Sidecar**: HTTP on 3500, gRPC on 3501

## Testing the Endpoints

### Direct Commands (Recommended)

#### 1. gRPC Service Invocation (CreateUser)

```bash
# Via Dapr HTTP API
curl -X POST http://localhost:3500/v1.0/invoke/user-service/method/CreateUser \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>"}'

# Expected Response: {"status":"ok"}
```

#### 2. REST API (GetUser)

```bash
# Direct REST call
curl -X GET http://localhost:8080/api/v1/users/123

# Expected Response: {"id":"123","name":"John Doe","email":"<EMAIL>","status":"active"}
```

#### 3. REST API (UpdateUser)

```bash
# Direct REST call
curl -X PUT http://localhost:8080/api/v1/users/123 \
  -H "Content-Type: application/json" \
  -d '{"id":"123","email":"<EMAIL>"}'

# Expected Response: {"status":"ok"}
```

#### 4. PubSub Events

```bash
# Publish UpdateUser event
dapr publish --publish-app-id user-service --pubsub pubsub --topic user-updates \
  --data '{"id":"123","email":"<EMAIL>"}'

# Publish ResetPassword event  
dapr publish --publish-app-id user-service --pubsub pubsub --topic password-resets \
  --data '{"id":"123"}'
```

## Expected Log Output

When running the service, you should see logs like:

```
2024/01/15 10:30:00 Starting User Service with CQRS-Dapr (cqrs)
2024/01/15 10:30:00 Engine: user-service
  gRPC Port: 9090
  REST Port: 8080
  Subscriber Port: 9081
  PubSub Component: test-pubsub
  Commands: 3
  Queries: 1

2024/01/15 10:30:00 Registered gRPC command handler: CreateUser
2024/01/15 10:30:00 Registered REST command handler: PUT /api/v1/users/:id
2024/01/15 10:30:00 Registered PubSub command handler: test-pubsub/users
2024/01/15 10:30:00 Registered PubSub command handler: test-pubsub/users
2024/01/15 10:30:00 Registered REST query handler: GET /api/v1/users/:id
2024/01/15 10:30:00 All handlers registered successfully. Starting engine...
2024/01/15 10:30:00 Starting gRPC service on port 9090
2024/01/15 10:30:00 Starting subscriber service on port 9081
2024/01/15 10:30:00 Starting REST service on port 8080
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Dapr Sidecar  │    │  User Service   │    │   Components    │
│                 │    │     (cqrs)      │    │                 │
│ HTTP: 3500      │◄──►│ gRPC: 9090      │    │ Kafka PubSub    │
│ gRPC: 3501      │    │ REST: 8080      │◄──►│ Redis State     │
│                 │    │ Sub:  9081      │    │ PostgreSQL      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Key Benefits Demonstrated

1. **Transport Flexibility**: Same business logic accessible via multiple protocols
2. **Dapr Integration**: Leverages Dapr's service mesh capabilities
3. **Type Safety**: Strong typing for all commands and queries
4. **Validation**: Comprehensive configuration and runtime validation
5. **Observability**: Built-in logging and error handling
6. **Scalability**: Event-driven architecture with pub/sub patterns

## Next Steps

- Add database persistence (PostgreSQL, Redis)
- Implement authentication and authorization
- Add OpenTelemetry tracing
- Create Docker deployment configuration
- Add integration tests with Testcontainers 
