version: '3'

vars:
  APP_ID: user-service
  GRPC_PORT: 9090
  REST_PORT: 8080
  SUB_PORT: 9081
  DAPR_HTTP_PORT: 3500
  DAPR_GRPC_PORT: 3501
  PUBSUB_NAME: pubsub

tasks:
  # Development tasks
  build:
    desc: Build the example application
    cmds:
      - go build -o user-service .

  test:
    desc: Run tests for the cqrs library
    dir: ..
    cmds:
      - go test -v .

  clean:
    desc: Clean build artifacts
    cmds:
      - rm -f user-service
      - rm -f example

  # Dapr setup tasks
  dapr:init:
    desc: Initialize Dapr (one-time setup)
    cmds:
      - dapr init

  # Run the service
  run:
    desc: Run the user service with Dapr
    cmds:
      - lsof -i :{{.REST_PORT}} | grep LISTEN | awk '{print $2}' | xargs kill -9
      - dapr run --app-id {{.APP_ID}} --app-port {{.GRPC_PORT}} --app-protocol grpc --dapr-grpc-port {{.DAPR_GRPC_PORT}} --dapr-http-port {{.DAPR_HTTP_PORT}} --log-level info go run .

  run:dev:
    desc: Run the service in development mode (with go run)
    cmds:
      - dapr run --app-id {{.APP_ID}} --app-port {{.GRPC_PORT}} --app-protocol grpc --dapr-grpc-port {{.DAPR_GRPC_PORT}} --dapr-http-port {{.DAPR_HTTP_PORT}} --log-level debug go run main.go

  run:simple:
    desc: Run without external dependencies (may show PubSub errors but gRPC/REST will work)
    cmds:
      - dapr run --app-id {{.APP_ID}} --app-port {{.GRPC_PORT}} --app-protocol grpc --dapr-grpc-port {{.DAPR_GRPC_PORT}} --dapr-http-port {{.DAPR_HTTP_PORT}} --log-level info go run main.go

  run:standalone:
    desc: Run Go service directly (REST only, no Dapr)
    cmds:
      - echo "Starting service in standalone mode on port {{.REST_PORT}}"
      - echo "Note - Only REST endpoints will work without Dapr"
      - go run main.go

  # Test individual transport types
  test:grpc:
    desc: Test gRPC service invocation (CreateUser)
    cmds:
      - echo "Testing gRPC CreateUser command..."
      - |
        curl -X POST http://localhost:{{.DAPR_HTTP_PORT}}/v1.0/invoke/{{.APP_ID}}/method/CreateUser \
          -H "Content-Type: application/json" \
          -d '{"name":"John Doe","email":"<EMAIL>"}' \
          -w "\nStatus: %{http_code}\n"

  test:rest:get:
    desc: Test REST API - GetUser query
    cmds:
      - echo "Testing REST GetUser query (direct)..."
      - |
        curl -X GET http://localhost:{{.REST_PORT}}/api/v1/users/123 \
          -w "\nStatus: %{http_code}\n"

  test:rest:put:
    desc: Test REST API - UpdateUser command
    cmds:
      - echo "Testing REST UpdateUser command (direct)..."
      - |
        curl -X PUT http://localhost:{{.REST_PORT}}/api/v1/users/123 \
          -H "Content-Type: application/json" \
          -d '{"id":"123","email":"<EMAIL>"}' \
          -w "\nStatus: %{http_code}\n"

  test:rest:only:
    desc: Test REST endpoints only (no Dapr required)
    cmds:
      - echo "Testing REST endpoints directly..."
      - task: test:rest:get
      - task: test:rest:put

  test:pubsub:update:
    desc: Test PubSub - UpdateUser event
    cmds:
      - echo "Publishing UpdateUser event..."
      - |
        dapr publish \
          --publish-app-id {{.APP_ID}} \
          --pubsub {{.PUBSUB_NAME}} \
          --topic user-updates \
          --data '{"id":"123","email":"<EMAIL>"}'

  test:pubsub:reset:
    desc: Test PubSub - ResetPassword event
    cmds:
      - echo "Publishing ResetPassword event..."
      - |
        dapr publish \
          --publish-app-id {{.APP_ID}} \
          --pubsub {{.PUBSUB_NAME}} \
          --topic password-resets \
          --data '{"id":"123"}'

  # Comprehensive testing
  test:basic:
    desc: Test basic transports (gRPC + REST, no PubSub)
    deps: [test:grpc, test:rest:get, test:rest:put]
    cmds:
      - echo "Basic transport tests completed!"

  test:all:
    desc: Test all transport types in sequence
    deps: [test:grpc, test:rest:get, test:rest:put, test:pubsub:update, test:pubsub:reset]
    cmds:
      - echo "All transport tests completed!"

  # Utility tasks
  stop:
    desc: Stop the Dapr application
    cmds:
      - dapr stop --app-id {{.APP_ID}}

  status:
    desc: Check application status
    cmds:
      - echo "Checking service endpoints..."
      - dapr list

  help:
    desc: Show available commands
    cmds:
      - echo "Available commands for CQRS Example"
      - echo ""
      - echo "Setup & Build"
      - echo "  task dapr:init     - Initialize Dapr (one-time)"
      - echo "  task build         - Build the application"
      - echo "  task clean         - Clean build artifacts"
      - echo ""
      - echo "Running the Service"
      - echo "  task run           - Run with Dapr"
      - echo "  task run:dev       - Run with go run and Dapr (debug mode)"
      - echo "  task run:simple    - Run with Dapr (may show PubSub errors)"
      - echo "  task run:standalone - Run Go service directly (REST only)"
      - echo "  task stop          - Stop the application"
      - echo ""
      - echo "Testing Individual Transports"
      - echo "  task test:grpc     - Test gRPC service invocation"
      - echo "  task test:rest:get - Test REST GET query"
      - echo "  task test:rest:put - Test REST PUT command"
      - echo "  task test:rest:only - Test REST endpoints only (no Dapr)"
      - echo "  task test:pubsub:update - Test PubSub UpdateUser event"
      - echo "  task test:pubsub:reset  - Test PubSub ResetPassword event"
      - echo ""
      - echo "Comprehensive Testing"
      - echo "  task test:basic    - Test basic transports (gRPC + REST)"
      - echo "  task test:all      - Test all transports sequentially"
      - echo ""
      - echo "Utilities"
      - echo "  task status        - Check application status"

  default:
    desc: Show help by default
    cmds:
      - task: help
