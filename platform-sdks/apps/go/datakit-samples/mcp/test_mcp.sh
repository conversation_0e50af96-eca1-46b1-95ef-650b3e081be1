#!/bin/bash

set -e

echo "🧪 Testing MCP Integration (Standalone Mode)"
echo "============================================="

BASE_URL="http://localhost:8080/mcp"
FAILED_TESTS=0
TOTAL_TESTS=0

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to run a test
run_test() {
    local test_name="$1"
    local curl_cmd="$2"
    local expected_pattern="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -e "\n${BLUE}Test $TOTAL_TESTS: $test_name${NC}"
    echo "Command: $curl_cmd"
    
    # Run the curl command and capture output
    local response
    response=$(eval "$curl_cmd" 2>/dev/null)
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo -e "${RED}❌ FAILED: curl command failed (exit code: $exit_code)${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
    
    echo "Response: $response"
    
    # Check if response matches expected pattern
    if echo "$response" | grep -q "$expected_pattern"; then
        echo -e "${GREEN}✅ PASSED${NC}"
        return 0
    else
        echo -e "${RED}❌ FAILED: Response doesn't match expected pattern '$expected_pattern'${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Function to check if service is running
check_service() {
    echo -e "\n${YELLOW}Checking if MCP service is running...${NC}"
    
    if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Service is running${NC}"
        return 0
    else
        echo -e "${RED}❌ Service is not running on $BASE_URL${NC}"
        echo -e "${YELLOW}💡 Start the service with: go run . --mcp-standalone${NC}"
        exit 1
    fi
}

# Main test execution
main() {
    echo -e "${BLUE}MCP Protocol Test Suite${NC}"
    echo "Testing Model Context Protocol integration with CQRS SDK"
    echo ""
    
    # Check if service is running
    check_service
    
    # Test 1: Initialize MCP connection
    run_test "Initialize MCP Connection" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":1,\"method\":\"initialize\",\"params\":{}}'" \
        '"protocolVersion":"2025-06-18"'
    
    # Test 2: List available tools
    run_test "List Available Tools" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":2,\"method\":\"tools/list\"}'" \
        '"tools":'
    
    # Test 3: Call update-user tool
    run_test "Call Update User Tool" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":3,\"method\":\"tools/call\",\"params\":{\"name\":\"update-user\",\"arguments\":{\"id\":\"1\",\"email\":\"<EMAIL>\"}}}'" \
        '"text":"Operation completed successfully"'
    
    # Test 4: Call get-user tool
    run_test "Call Get User Tool" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":4,\"method\":\"tools/call\",\"params\":{\"name\":\"get-user\",\"arguments\":{\"id\":\"1\"}}}'" \
        '"content":'
    
    # Test 5: Call create-user tool
    run_test "Call Create User Tool" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":5,\"method\":\"tools/call\",\"params\":{\"name\":\"create-user\",\"arguments\":{\"name\":\"Neo Anderson\",\"email\":\"<EMAIL>\"}}}'" \
        '"text":"Operation completed successfully"'
    
    # Test 6: Call list-users tool
    run_test "Call List Users Tool" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":6,\"method\":\"tools/call\",\"params\":{\"name\":\"list-users\",\"arguments\":{\"limit\":10,\"offset\":0}}}'" \
        '"content":'
    
    # Test 7: Test invalid method
    run_test "Invalid Method Handling" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":7,\"method\":\"invalid/method\"}'" \
        '"error":'
    
    # Test 8: Test invalid tool name
    run_test "Invalid Tool Name Handling" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":8,\"method\":\"tools/call\",\"params\":{\"name\":\"nonexistent-tool\",\"arguments\":{}}}'" \
        '"error":'
    
    # Test 9: Test malformed JSON
    run_test "Malformed JSON Handling" \
        "curl -s -X POST $BASE_URL -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"id\":9,\"method\":\"tools/list\"'" \
        '"error":'
    
    # Test 10: Test REST endpoints still work
    run_test "REST Endpoint Still Works" \
        "curl -s -X GET 'http://localhost:8080/api/v1/users/1'" \
        '"name":"John Doe"'
    
    # Print summary
    echo ""
    echo "============================================="
    echo -e "${BLUE}Test Summary${NC}"
    echo "============================================="
    echo "Total tests: $TOTAL_TESTS"
    echo -e "Passed: ${GREEN}$((TOTAL_TESTS - FAILED_TESTS))${NC}"
    echo -e "Failed: ${RED}$FAILED_TESTS${NC}"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All tests passed! MCP integration is working correctly.${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ Some tests failed. Please check the output above.${NC}"
        exit 1
    fi
}

# Run the main function
main "$@" 