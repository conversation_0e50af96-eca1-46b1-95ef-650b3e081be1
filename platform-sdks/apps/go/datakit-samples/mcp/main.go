package main

import (
	"context"
	"errors"
	"fmt"
	"log"
	"os"
	"os/signal"
	"strconv"
	"sync"
	"syscall"
	"time"

	"github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
)

/* Domain Model --------------------------------------------- */

// User represents a user entity in the domain
type User struct {
	ID     int    `json:"id"`
	Name   string `json:"name"`
	Email  string `json:"email"`
	Status string `json:"status"`
}

// New<PERSON>ser creates a new user without ID (will be auto-generated)
func NewUser(name, email string) *User {
	return &User{
		ID:     0, // Will be set by repository
		Name:   name,
		Email:  email,
		Status: "active",
	}
}

// UpdateEmail updates the user's email address
func (u *User) UpdateEmail(email string) {
	u.Email = email
}

/* Repository Interface ------------------------------------- */

// UserRepository defines the contract for user data access
type UserRepository interface {
	Save(user *User) error
	FindByID(id string) (*User, error)
	FindAll(limit, offset int) ([]*User, int, error)
	Update(user *User) error
	Delete(id string) error
}

/* In-Memory Repository Implementation ---------------------- */

// InMemoryUserRepository implements UserRepository using in-memory storage
type InMemoryUserRepository struct {
	users     map[string]*User
	mutex     sync.RWMutex
	idCounter int
}

// NewInMemoryUserRepository creates a new in-memory user repository
func NewInMemoryUserRepository() *InMemoryUserRepository {
	repo := &InMemoryUserRepository{
		users:     make(map[string]*User),
		mutex:     sync.RWMutex{},
		idCounter: 3, // Start from 3 since we'll pre-load users with IDs 1 and 2
	}

	// Add some initial test data
	testUsers := []*User{
		{ID: 1, Name: "John Doe", Email: "<EMAIL>", Status: "active"},
		{ID: 2, Name: "Jane Smith", Email: "<EMAIL>", Status: "active"},
	}

	for _, user := range testUsers {
		repo.users[strconv.Itoa(user.ID)] = user
	}

	return repo
}

// NextID generates the next sequential ID
func (r *InMemoryUserRepository) NextID() int {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	id := r.idCounter
	r.idCounter++
	return id
}

// Save stores a new user
func (r *InMemoryUserRepository) Save(user *User) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// Generate ID if not provided (ID is 0)
	if user.ID == 0 {
		user.ID = r.idCounter
		r.idCounter++
	}

	if _, exists := r.users[strconv.Itoa(user.ID)]; exists {
		return errors.New("user already exists")
	}

	r.users[strconv.Itoa(user.ID)] = user
	log.Printf("User saved: ID=%d, Name=%s, Email=%s", user.ID, user.Name, user.Email)
	return nil
}

// FindByID retrieves a user by ID
func (r *InMemoryUserRepository) FindByID(id string) (*User, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	user, exists := r.users[id]
	if !exists {
		return nil, errors.New("user not found")
	}

	return user, nil
}

// FindAll retrieves all users with pagination
func (r *InMemoryUserRepository) FindAll(limit, offset int) ([]*User, int, error) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	// Convert map to slice
	allUsers := make([]*User, 0, len(r.users))
	for _, user := range r.users {
		allUsers = append(allUsers, user)
	}

	total := len(allUsers)

	// Apply pagination
	start := offset
	if start > total {
		start = total
	}

	end := start + limit
	if limit <= 0 || end > total {
		end = total
	}

	return allUsers[start:end], total, nil
}

// Update modifies an existing user
func (r *InMemoryUserRepository) Update(user *User) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.users[strconv.Itoa(user.ID)]; !exists {
		return errors.New("user not found")
	}

	r.users[strconv.Itoa(user.ID)] = user
	log.Printf("User updated: ID=%d, Name=%s, Email=%s", user.ID, user.Name, user.Email)
	return nil
}

// Delete removes a user by ID
func (r *InMemoryUserRepository) Delete(id string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.users[id]; !exists {
		return errors.New("user not found")
	}

	delete(r.users, id)
	log.Printf("User deleted: ID=%s", id)
	return nil
}

/* Utility Functions ---------------------------------------- */

// Note: ID generation is now handled by the repository's NextID method

/* DTOs ----------------------------------------------------- */

// CreateUserCommand represents a command to create a new user
type CreateUserCommand struct {
	Name  string `json:"name" binding:"required"`
	Email string `json:"email" binding:"required,email"`
}

// UpdateUserCommand represents a command to update an existing user
type UpdateUserCommand struct {
	ID    string `json:"id" uri:"id" binding:"required"`
	Email string `json:"email" binding:"required,email"`
}

// GetUserQuery represents a query to retrieve user information
type GetUserQuery struct {
	ID string `json:"id" uri:"id" form:"id" binding:"required"`
}

// ListUsersQuery represents a query to retrieve all users
type ListUsersQuery struct {
	Limit  int `json:"limit" form:"limit"`
	Offset int `json:"offset" form:"offset"`
}

// ListUsersResponse is the structured response for the list-users query
type ListUsersResponse struct {
	Users  []*User `json:"users"`
	Total  int     `json:"total"`
	Limit  int     `json:"limit"`
	Offset int     `json:"offset"`
}

/* Service -------------------------------------------------- */

// UserService handles user-related operations with repository
type UserService struct {
	userRepo UserRepository
}

// NewUserService creates a new UserService with repository
func NewUserService(userRepo UserRepository) *UserService {
	return &UserService{
		userRepo: userRepo,
	}
}

/* Handlers ------------------------------------------------- */

// createUser handles the CreateUserCommand
func (s *UserService) createUser(ctx context.Context, c any) error {
	cmd := c.(*CreateUserCommand)
	log.Printf("CreateUser executed: Name=%s, Email=%s", cmd.Name, cmd.Email)

	// Create new user (ID will be generated by repository)
	user := NewUser(cmd.Name, cmd.Email)

	// Save to repository
	if err := s.userRepo.Save(user); err != nil {
		log.Printf("Failed to create user: %v", err)
		return fmt.Errorf("failed to create user: %w", err)
	}

	log.Printf("User created successfully: ID=%d", user.ID)
	return nil
}

// updateUser handles the UpdateUserCommand
func (s *UserService) updateUser(ctx context.Context, c any) error {
	cmd := c.(*UpdateUserCommand)
	log.Printf("UpdateUser executed: ID=%s, Email=%s", cmd.ID, cmd.Email)

	// Find existing user
	user, err := s.userRepo.FindByID(cmd.ID)
	if err != nil {
		log.Printf("User not found: %v", err)
		return fmt.Errorf("user not found: %w", err)
	}

	// Update email
	user.UpdateEmail(cmd.Email)

	// Save changes
	if err := s.userRepo.Update(user); err != nil {
		log.Printf("Failed to update user: %v", err)
		return fmt.Errorf("failed to update user: %w", err)
	}

	log.Printf("User updated successfully: ID=%d", user.ID)
	return nil
}

// getUser handles the GetUserQuery
func (s *UserService) getUser(ctx context.Context, q any) (any, error) {
	query := q.(*GetUserQuery)
	log.Printf("GetUser executed: ID=%s", query.ID)

	// Find user in repository
	user, err := s.userRepo.FindByID(query.ID)
	if err != nil {
		log.Printf("User not found: %v", err)
		return nil, fmt.Errorf("user not found: %w", err)
	}

	log.Printf("User found: ID=%d, Name=%s", user.ID, user.Name)
	return user, nil
}

// listUsers handles the ListUsersQuery
func (s *UserService) listUsers(ctx context.Context, q any) (any, error) {
	query := q.(*ListUsersQuery)
	log.Printf("ListUsers executed: Limit=%d, Offset=%d", query.Limit, query.Offset)

	// Set default pagination if not specified
	limit := query.Limit
	if limit <= 0 {
		limit = 100 // Default limit
	}

	offset := query.Offset
	if offset < 0 {
		offset = 0
	}

	// Get users from repository
	users, total, err := s.userRepo.FindAll(limit, offset)
	if err != nil {
		log.Printf("Failed to list users: %v", err)
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	result := ListUsersResponse{
		Users:  users,
		Total:  total,
		Limit:  limit,
		Offset: offset,
	}

	log.Printf("Users listed: Count=%d, Total=%d", len(users), total)
	return result, nil
}

/* Main Function -------------------------------------------- */

func main() {
	log.Println("Starting MCP Test Service")

	// Create repository and service
	userRepo := NewInMemoryUserRepository()
	userService := NewUserService(userRepo)

	// Create the engine with REST and MCP support only
	engine := cqrs.NewEngine("mcp-test-service",
		cqrs.WithVersion("2.0.0"), // Set service version
		cqrs.WithREST(8080),       // REST API endpoints
		cqrs.WithMCP(&cqrs.MCPConfig{ // MCP protocol support
			Path:     "/mcp",
			EnableWS: false, // WebSocket not needed for MVP
		}),
		cqrs.WithLogging(&cqrs.LogConfig{
			Level:  cqrs.LogLevelDebug,
			Format: cqrs.LogFormatText,
		}),
	)

	// Print engine summary
	log.Println(engine.Summary())

	// COMMAND: CREATE USER - REST + MCP
	if err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &CreateUserCommand{},
		Handler: userService.createUser,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "POST", Path: "/api/v1/users"},
			MCP: &cqrs.MCP{
				Description: "Create a new user with name and email",
				InputSchema: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"name":  map[string]string{"type": "string"},
						"email": map[string]string{"type": "string"},
					},
					"required": []string{"name", "email"},
				},
			},
		},
		Meta: map[string]interface{}{
			"description": "Creates a new user via REST or MCP",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register CreateUser command:", err)
	}

	// COMMAND: UPDATE USER - REST + MCP
	if err := engine.RegisterCommands(cqrs.CommandConfig{
		Command: &UpdateUserCommand{},
		Handler: userService.updateUser,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "PUT", Path: "/api/v1/users/:id"},
			MCP: &cqrs.MCP{
				Description: "Change the user's email address",
				InputSchema: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"id":    map[string]string{"type": "string"},
						"email": map[string]string{"type": "string"},
					},
					"required": []string{"id", "email"},
				},
			},
		},
		Meta: map[string]interface{}{
			"description": "Updates user information via REST or MCP",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register UpdateUser command:", err)
	}

	// QUERY: GET USER - REST + MCP
	if err := engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &GetUserQuery{},
		Handler: userService.getUser,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/api/v1/users/:id"},
			MCP: &cqrs.MCP{
				Title:       "Get User",
				Description: "Retrieve user information by ID",
				InputSchema: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"id": map[string]string{"type": "string"},
					},
					"required": []string{"id"},
				},
			},
		},
		Meta: map[string]interface{}{
			"description": "Retrieves user information via REST API or MCP",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register GetUser query:", err)
	}

	// QUERY: LIST USERS - REST + MCP
	if err := engine.RegisterQueries(cqrs.QueryConfig{
		Query:   &ListUsersQuery{},
		Handler: userService.listUsers,
		Transport: cqrs.Transport{
			REST: &cqrs.REST{Method: "GET", Path: "/api/v1/users"},
			MCP: &cqrs.MCP{
				Title:       "List Users",
				Description: "Retrieve all users with optional pagination",
				InputSchema: map[string]any{
					"type": "object",
					"properties": map[string]any{
						"limit":  map[string]string{"type": "number"},
						"offset": map[string]string{"type": "number"},
					},
				},
			},
		},
		Meta: map[string]interface{}{
			"description": "Retrieves all users with pagination via REST API or MCP",
			"version":     "1.0",
		},
	}); err != nil {
		log.Fatal("Failed to register ListUsers query:", err)
	}

	// Start the engine
	log.Println("All handlers registered successfully. Starting engine...")

	// Create context that can be cancelled
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Set up graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start engine in a goroutine
	engineErr := make(chan error, 1)
	go func() {
		if err := engine.Start(ctx); err != nil {
			engineErr <- err
		}
	}()

	log.Println("Service started successfully!")
	log.Println("REST endpoints available at http://localhost:8080")
	log.Println("MCP endpoints available at http://localhost:8080/mcp")

	// Wait for shutdown signal or engine error
	select {
	case err := <-engineErr:
		log.Fatal("Engine failed to start:", err)
	case sig := <-sigChan:
		log.Printf("Received signal %v, shutting down gracefully...", sig)
		cancel()

		// Give some time for graceful shutdown
		time.Sleep(1 * time.Second)
		log.Println("Service stopped")
		os.Exit(0)
	}
}
