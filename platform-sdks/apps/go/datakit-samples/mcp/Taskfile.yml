version: '3'

vars:
  SERVICE_NAME: mcp-test-service
  PORT: 8080
  MCP_PATH: /mcp

tasks:
  default:
    desc: Show available tasks
    cmd: task --list

  run:
    desc: Run the MCP test service
    deps: [deps]
    cmd: |
      echo "🚀 Running the MCP test service..."
      lsof -i :{{.PORT}} | grep LISTEN | awk '{print $2}' | xargs kill -9
      go run main.go

  deps:
    desc: Download Go dependencies
    cmd: go mod tidy
    sources:
      - go.mod
      - go.sum
      - "*.go"

  build:
    desc: Build the service binary
    cmd: go build -o {{.SERVICE_NAME}} main.go
    deps: [deps]

  test:
    desc: Run all tests (requires service to be running)
    cmd: |
      echo "🧪 Running All Tests..."
      echo "Make sure the service is running with 'task run' in another terminal"
      sleep 2
      ./test_mcp.sh
    deps: [check-service]

  test-rest:
    desc: Test REST API endpoints
    cmd: |
      echo "🌐 Testing REST API..."
      echo "Make sure the service is running with 'task run' in another terminal"
      curl -s http://localhost:{{.PORT}}/api/v1/users/1 | jq '.' || echo "Install jq for formatted output"
    deps: [check-service]

  test-mcp:
    desc: Test MCP protocol endpoints
    cmd: |
      echo "🔌 Testing MCP Protocol..."
      echo "Make sure the service is running with 'task run' in another terminal"
      curl -s -X POST http://localhost:{{.PORT}}{{.MCP_PATH}} \
        -H 'Content-Type: application/json' \
        -d '{"jsonrpc":"2.0","id":1,"method":"tools/list"}' | jq '.' || echo "Install jq for formatted output"
    deps: [check-service]

  check-service:
    desc: Check if service is running
    cmd: |
      if ! curl -s --connect-timeout 3 http://localhost:{{.PORT}}{{.MCP_PATH}} > /dev/null 2>&1; then
        echo "❌ Service is not running on http://localhost:{{.PORT}}"
        echo "💡 Start the service with: task run"
        exit 1
      fi
      echo "✅ Service is running"

  dev:
    desc: Run service with auto-reload (requires 'air' tool)
    cmd: |
      if command -v air >/dev/null 2>&1; then
        air
      else
        echo "❌ 'air' tool not found. Install with: go install github.com/cosmtrek/air@latest"
        echo "🔄 Falling back to regular run..."
        task run
      fi

  lint:
    desc: Run linter
    cmd: |
      if command -v golangci-lint >/dev/null 2>&1; then
        golangci-lint run
      else
        echo "❌ golangci-lint not found. Install from: https://golangci-lint.run/usage/install/"
        echo "🔄 Running basic go vet..."
        go vet ./...
      fi

  format:
    desc: Format Go code
    cmd: |
      go fmt ./...
      if command -v goimports >/dev/null 2>&1; then
        goimports -w .
      else
        echo "💡 Install goimports for better formatting: go install golang.org/x/tools/cmd/goimports@latest"
      fi

  clean:
    desc: Clean build artifacts
    cmd: |
      rm -f {{.SERVICE_NAME}}
      go clean

  help:
    desc: Show detailed help
    cmd: |
      echo "MCP Test Service - Task Commands"
      echo "================================"
      echo ""
      echo "Development:"
      echo "  task run          - Start the service"
      echo "  task dev          - Start with auto-reload (requires air)"
      echo "  task build        - Build binary"
      echo ""
      echo "Testing:"
      echo "  task test         - Run all tests"
      echo "  task test-rest    - Test REST endpoints"
      echo "  task test-mcp     - Test MCP endpoints"
      echo ""
      echo "Code Quality:"
      echo "  task lint         - Run linter"
      echo "  task format       - Format code"
      echo ""
      echo "Utilities:"
      echo "  task clean        - Clean build files"
      echo "  task deps         - Download dependencies"
      echo "  task check-service - Check if service is running"
      echo ""
      echo "Examples:"
      echo "  # Start service and test in parallel"
      echo "  task run &"
      echo "  sleep 3 && task test"
      echo ""
      echo "Service Details:"
      echo "  URL: http://localhost:{{.PORT}}"
      echo "  MCP: http://localhost:{{.PORT}}{{.MCP_PATH}}"
      echo "  API: http://localhost:{{.PORT}}/api/v1/users"
