{"name": "datakit-samples", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/typescript/datakit-samples", "tags": ["typescript", "sample", "cqrs", "datakit"], "targets": {"lint": {"executor": "nx:run-commands", "options": {"command": "eslint examples/**/*.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run eslint for code formatting"}}, "serve:basic": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/01-basic/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the basic CQRS example"}}, "serve:middleware": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/02-middleware/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the middleware example"}}, "serve:rest": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/03-rest-api/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the REST API example"}}, "serve:trpc": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/04-trpc-client/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the tRPC client example"}}, "serve:testing": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/05-testing/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the testing example"}}, "serve:pubsub": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/07-pubsub/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the pubsub example"}}, "serve:fullstack": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/06-fullstack/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the fullstack example"}}, "serve:mcp": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/08-mcp-integration/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the MCP example"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "nodemon --config ../../../nodemon.json examples/06-fullstack/index.ts", "cwd": "{projectRoot}"}, "metadata": {"description": "Run the fullstack example"}}, "test": {"executor": "nx:run-commands", "options": {"command": "jest", "cwd": "{projectRoot}"}, "metadata": {"description": "Run jest tests"}}}}