# CQRS SDK Examples

This directory contains progressive examples demonstrating the CQRS SDK functionality.

## Prerequisites

```bash
# Install dependencies
pnpm install

# Build the main datakit package first
cd ../datakit
pnpm build
cd ../datakit-samples
```

## Examples

### 1. Basic Usage (`example:basic`)
Learn the fundamentals: defining commands, queries, and basic execution.

```bash
pnpm example:basic
```

### 2. Middleware (`example:middleware`)
Add authentication, logging, and other cross-cutting concerns.

```bash
pnpm example:middleware
```

### 3. REST API (`example:rest`)
Expose your procedures as REST endpoints with OpenAPI documentation.

```bash
pnpm example:rest
```

### 4. tRPC Client (`example:trpc`)
Use type-safe tRPC clients to consume your procedures.

```bash
pnpm example:trpc
```

### 5. Testing (`example:testing`)
Comprehensive testing with mocking capabilities.

```bash
pnpm example:testing
```

### 6. Full-Stack App (`example:fullstack`)
A complete application with both REST and tRPC endpoints.

```bash
pnpm example:fullstack
```

## Development

```bash
# Run all tests
pnpm test

# Watch mode for development
pnpm test:watch

# Build TypeScript
pnpm build
```

## Structure

```
examples/
├── 01-basic/           # Basic CQRS usage
├── 02-middleware/      # Middleware examples
├── 03-rest-api/        # REST API setup
├── 04-trpc-client/     # tRPC client usage
├── 05-testing/         # Testing patterns
└── 06-fullstack/       # Complete application
```

Each example is self-contained and demonstrates specific aspects of the CQRS SDK. 