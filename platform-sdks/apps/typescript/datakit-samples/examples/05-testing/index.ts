import { z } from 'zod';
import { CQRS } from '@matricsio/datakit';

// Context types
type AppContext = {
  userId?: string;
  requestId: string;
  timestamp?: Date;
};

// Create CQRS instance
const { define, registry } = CQRS.create<AppContext>();
const { command, query } = define;

// Mock external services
interface EmailService {
  sendEmail(to: string, subject: string, body: string): Promise<boolean>;
}

interface DatabaseService {
  users: Map<string, { id: string; email: string; name: string }>;
  saveUser(user: { id: string; email: string; name: string }): Promise<void>;
  findUser(id: string): Promise<{ id: string; email: string; name: string } | null>;
  deleteUser(id: string): Promise<boolean>;
}

// Mock implementations
const mockEmailService: EmailService = {
  async sendEmail(to: string, subject: string, body: string): Promise<boolean> {
    console.log(`📧 Sending email to ${to}: ${subject}`);
    console.log(`📧 Body: ${body}`);
    return true;
  }
};

const mockDatabaseService: DatabaseService = {
  users: new Map(),

  async saveUser(user: { id: string; email: string; name: string }) {
    this.users.set(user.id, user);
    console.log(`💾 Saved user: ${user.name}`);
  },

  async findUser(id: string) {
    const user = this.users.get(id);
    console.log(`🔍 Finding user ${id}: ${user ? 'found' : 'not found'}`);
    return user || null;
  },

  async deleteUser(id: string) {
    const existed = this.users.has(id);
    this.users.delete(id);
    console.log(`🗑️  Deleted user ${id}: ${existed ? 'success' : 'not found'}`);
    return existed;
  }
};

// Define procedures
const createUser = command({
  input: z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Valid email is required'),
  }),
  output: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    created: z.boolean(),
    emailSent: z.boolean(),
  }),
  handler: async ({ input, ctx }) => {
    const userId = `user-${Date.now()}`;

    // Save to database
    await mockDatabaseService.saveUser({
      id: userId,
      name: input.name,
      email: input.email,
    });

    // Send welcome email
    const emailSent = await mockEmailService.sendEmail(
      input.email,
      'Welcome!',
      `Hello ${input.name}, welcome to our platform!`
    );

    console.log(`✅ [${ctx.requestId}] User created: ${input.name}`);

    return {
      id: userId,
      name: input.name,
      email: input.email,
      created: true,
      emailSent,
    };
  },
});

const getUser = query({
  input: z.object({
    id: z.string(),
  }),
  output: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
    found: z.boolean(),
  }).nullable(),
  handler: async ({ input, ctx }) => {
    const user = await mockDatabaseService.findUser(input.id);

    console.log(`🔍 [${ctx.requestId}] User lookup: ${input.id}`);

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      found: true,
    };
  },
});

const deleteUser = command({
  input: z.object({
    id: z.string(),
  }),
  output: z.object({
    id: z.string(),
    deleted: z.boolean(),
  }),
  handler: async ({ input, ctx }) => {
    const deleted = await mockDatabaseService.deleteUser(input.id);

    console.log(`🗑️  [${ctx.requestId}] User deletion: ${input.id}`);

    return {
      id: input.id,
      deleted,
    };
  },
});

const getUserStats = query({
  input: z.void(),
  output: z.object({
    totalUsers: z.number(),
    timestamp: z.string(),
  }),
  handler: async ({ ctx }) => {
    const totalUsers = mockDatabaseService.users.size;

    console.log(`📊 [${ctx.requestId}] User stats requested`);

    return {
      totalUsers,
      timestamp: (ctx.timestamp || new Date()).toISOString(),
    };
  },
});

// Create registry
const appRegistry = registry({
  createUser,
  getUser,
  deleteUser,
  getUserStats,
});

// Create CQRS instance
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: () => ({
    requestId: `req-${Date.now()}`,
    userId: 'test-user',
    timestamp: new Date(),
  }),
});

// Testing utilities and examples
async function demonstrateBasicTesting() {
  console.log('🧪 Basic Testing Example\n');

  try {
    // Test user creation
    console.log('1. Testing user creation...');
    const createResult = await cqrs.commands.createUser({
      name: 'John Doe',
      email: '<EMAIL>',
    });

    console.log('✅ User created:', createResult);

    // Test user retrieval
    console.log('\n2. Testing user retrieval...');
    const getResult = await cqrs.queries.getUser({ id: createResult.id });
    console.log('✅ User retrieved:', getResult);

    // Test user stats
    console.log('\n3. Testing user stats...');
    const statsResult = await cqrs.queries.getUserStats();
    console.log('✅ User stats:', statsResult);

    // Test user deletion
    console.log('\n4. Testing user deletion...');
    const deleteResult = await cqrs.commands.deleteUser({ id: createResult.id });
    console.log('✅ User deleted:', deleteResult);

    // Verify deletion
    console.log('\n5. Verifying deletion...');
    const deletedUser = await cqrs.queries.getUser({ id: createResult.id });
    console.log('✅ User after deletion:', deletedUser);

  } catch (error) {
    console.error('❌ Basic testing error:', error);
  }
}

async function demonstrateMockedTesting() {
  console.log('\n🎭 Mocked Testing Example\n');

  // Create a mocked registry
  const mockedRegistry = cqrs.enableMocking({
    requestId: `mock-req-${Date.now()}`,
    userId: 'test-user',
    timestamp: new Date(),
  });

  // Mock the createUser command
  mockedRegistry.spyOn('createUser').mockImplementation(async (args) => {
    console.log('🎭 Mocked createUser called with:', args);
    return {
      id: 'mocked-user-123',
      name: args.input.name,
      email: args.input.email,
      created: true,
      emailSent: false, // Simulate email service failure
    };
  });

  // Mock the getUser query
  mockedRegistry.spyOn('getUser').mockImplementation(async (args) => {
    console.log('🎭 Mocked getUser called with:', args);
    if (args.input.id === 'mocked-user-123') {
      return {
        id: 'mocked-user-123',
        name: 'Mocked User',
        email: '<EMAIL>',
        found: true,
      };
    }
    return null;
  });

  try {
    console.log('1. Testing with mocked createUser...');
    const createResult = await cqrs.commands.createUser({
      name: 'Test User',
      email: '<EMAIL>',
    });
    console.log('✅ Mocked create result:', createResult);

    console.log('\n2. Testing with mocked getUser...');
    const getResult = await cqrs.queries.getUser({ id: 'mocked-user-123' });
    console.log('✅ Mocked get result:', getResult);

    console.log('\n3. Testing call tracking...');
    const createCalls = mockedRegistry.getCalls('createUser');
    const getCalls = mockedRegistry.getCalls('getUser');

    console.log('✅ createUser calls:', createCalls.calls.length);
    console.log('✅ getUser calls:', getCalls.calls.length);

    console.log('\n4. Inspecting call details...');
    console.log('✅ Last createUser call:', createCalls.calls[createCalls.calls.length - 1]);
    console.log('✅ Last getUser call:', getCalls.calls[getCalls.calls.length - 1]);

    console.log('\n5. Testing unmocked procedure...');
    const statsResult = await cqrs.queries.getUserStats();
    console.log('✅ Unmocked stats result:', statsResult);

  } catch (error) {
    console.error('❌ Mocked testing error:', error);
  }
}

async function demonstrateTestSuite() {
  console.log('\n🧪 Test Suite Example\n');

  const cqrs = new CQRS({
    registry: appRegistry,
    getContext: () => ({
      requestId: `req-${Date.now()}`,
      userId: 'test-user',
      timestamp: new Date(),
    }),
  });

  const testResults: { name: string; passed: boolean; error?: string }[] = [];

  async function runTest(name: string, testFn: () => Promise<void>) {
    try {
      console.log(`🧪 Running: ${name}`);
      await testFn();
      testResults.push({ name, passed: true });
      console.log(`✅ Passed: ${name}\n`);
    } catch (error) {
      testResults.push({ name, passed: false, error: error instanceof Error ? error.message : String(error) });
      console.log(`❌ Failed: ${name} - ${error instanceof Error ? error.message : String(error)}\n`);
    }
  }

  // Test: User creation with valid data
  await runTest('User creation with valid data', async () => {
    const result = await cqrs.commands.createUser({
      name: 'Valid User',
      email: '<EMAIL>',
    });

    if (!result.created) throw new Error('User was not created');
    if (!result.id) throw new Error('User ID was not generated');
    if (result.name !== 'Valid User') throw new Error('Name mismatch');
  });

  // Test: User creation with invalid email
  await runTest('User creation with invalid email', async () => {
    try {
      await cqrs.commands.createUser({
        name: 'Invalid User',
        email: 'invalid-email',
      });
      throw new Error('Should have thrown validation error');
    } catch (error) {
      if (!(error instanceof Error)) {
        throw new Error(`Wrong validation error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  });

  // Test: Get non-existent user
  await runTest('Get non-existent user', async () => {
    const result = await cqrs.queries.getUser({ id: 'non-existent-id' });
    if (result !== null) throw new Error('Should return null for non-existent user');
  });

  // Test: User stats
  await runTest('User stats', async () => {
    const result = await cqrs.queries.getUserStats();
    if (typeof result.totalUsers !== 'number') throw new Error('totalUsers should be a number');
    if (!result.timestamp) throw new Error('timestamp should be present');
  });

  // Print test results
  console.log('📊 Test Results Summary:');
  console.log('========================');

  const passed = testResults.filter(t => t.passed).length;
  const failed = testResults.filter(t => !t.passed).length;

  testResults.forEach(result => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} ${result.name}`);
    if (!result.passed && result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  console.log(`\nTotal: ${testResults.length}, Passed: ${passed}, Failed: ${failed}`);

  if (failed === 0) {
    console.log('🎉 All tests passed!');
  } else {
    console.log('💥 Some tests failed!');
  }
}

async function runTestingExample() {
  console.log('🧪 Testing Example\n');

  try {
    // Basic testing
    await demonstrateBasicTesting();

    // Mocked testing
    await demonstrateMockedTesting();

    // Test suite
    await demonstrateTestSuite();

    console.log('\n🎉 Testing example completed successfully!');

  } catch (error) {
    console.error('❌ Error in testing example:', error);
  }
}

// Run the example
runTestingExample().catch(console.error);
