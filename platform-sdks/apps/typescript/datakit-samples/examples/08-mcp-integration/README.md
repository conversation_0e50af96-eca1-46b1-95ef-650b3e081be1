# MCP (Model Context Protocol) Integration Example

This example demonstrates how to integrate MCP with the CQRS SDK using a realistic cardiac device monitoring system, showcasing tools, resources, and resource templates for healthcare AI assistant interactions.

## Overview

The example implements a cardiac device monitoring system with the following features:

### 🛠️ **MCP Tools (CQRS Procedures)**
- **Patient Management**: Create, update, and get patients
- **Interrogation Review**: Review device interrogations, mark as reviewed, get statistics
- **Analytics**: Get comprehensive statistics with clinical formatting

### 📚 **MCP Resources**
- **Static Resources**: Patient/Device/Interrogation schemas, API documentation
- **Dynamic Resource Templates**: Patient profiles with device history, PDF report access

### ✨ **MCP Features Demonstrated**
- **Streaming**: Real-time content updates during operations
- **Progress Reporting**: Track operation completion
- **Rich Content Formatting**: Custom transformers for beautiful output
- **Annotations**: ReadOnly hints, streaming hints, etc.

## Medical Domain Context

This example models a **cardiac device monitoring system** used in healthcare:

### Device Types
- **PPM (Pacemaker)**: Monitors heart rhythm and provides pacing when needed
- **ILR (Implantable Loop Recorder)**: Continuous heart rhythm monitoring device  
- **ICD (Implantable Cardioverter Defibrillator)**: Treats dangerous arrhythmias with shocks

### Priority System
- **HIGH (1)**: Critical findings requiring immediate medical attention
- **MEDIUM (2)**: Notable findings requiring timely clinical review
- **LOW (3)**: Routine findings for standard follow-up

### Clinical Workflow
1. Devices automatically transmit data (daily/monthly)
2. System processes transmissions and assigns priority based on measurements
3. Doctors review high/medium priority interrogations via AI assistant
4. Clinical notes are added and interrogations marked as reviewed
5. Analytics track trends and ensure no critical findings are missed

## Getting Started

### Prerequisites
```bash
npm install @matricsio/datakit
```

### Running the Example

1. **Start the MCP Server**:
```bash
cd apps/typescript/datakit-samples/examples/08-mcp-integration
npx tsx index.ts
```

2. **Run with Demo Data**:
```bash
npx tsx index.ts --demo
```

3. **Connect MCP Client**: Use any MCP-compatible client to connect via stdio transport.

## Architecture

### CQRS Structure
```
📁 Patient Management
├── createPatient (Command) - Register new patients
├── updatePatient (Command) - Update patient information
├── getPatient (Query) - Get patient details with optional device info
└── getPatients (Query) - List/filter patients

📁 Interrogation Review
├── markInterrogationReviewed (Command) - Mark interrogation as reviewed
├── getInterrogation (Query) - Get detailed interrogation with context
└── getInterrogations (Query) - List/filter interrogations by priority, device type, etc.

📁 Analytics
└── getInterrogationStats (Query) - Comprehensive statistics and trends

📁 Internal Operations (REST-only)
└── createInterrogation (Command) - Process incoming device data
```

### MCP Integration
```
🔧 Tools (8 doctor-facing + 1 internal)
├── Patient management tools with clinical context
├── Interrogation review tools with priority filtering
├── Analytics tools with medical formatting
└── Proper annotations (readOnly, streaming, etc.)

📚 Resources (4 static)
├── cardiac://schemas/patient - Patient JSON schema
├── cardiac://schemas/device - Device JSON schema
├── cardiac://schemas/interrogation - Interrogation JSON schema
└── cardiac://api/documentation - Complete API docs

🎯 Resource Templates (2 dynamic)
├── cardiac://patients/{id} - Patient profile with device/interrogation history
└── cardiac://interrogations/{id}/pdf - PDF report access
```

## Key Features

### 1. **Streaming Content**
Operations provide real-time updates:
```typescript
await ctx.mcp?.streamContent({
    type: 'text',
    text: `🏥 Creating patient record for ${input.name}...`
});
```

### 2. **Progress Reporting**
Track long-running operations:
```typescript
await ctx.mcp?.reportProgress({ progress: 75, total: 100 });
```

### 3. **Dynamic Resources**

Context-aware resource templates:
```typescript
cqrs.addResourceTemplate({
    uriTemplate: 'cardiac://patients/{id}',
    load: async (args) => {
        const patient = patients.get(args.id);
        const patientDevices = devices.filter(d => d.patientId === args.id);
        const patientInterrogations = interrogations.filter(i => i.patientId === args.id);
        return {
            text: JSON.stringify({ ...patient, devices: patientDevices, summary: {...} }),
            mimeType: 'application/json'
        };
    }
});
```

## Example Interactions

### Creating a Patient
**Input**:
```json
{
  "name": "John Smith",
  "dateOfBirth": "1965-03-15",
  "clinicId": "clinic-cardio-01"
}
```

**MCP Output**:
```
🏥 Creating patient record for John Smith...
✅ Created patient: John Smith (ID: patient-123456)
```

### Getting Interrogations
**Input**:
```json
{
  "priority": 1,
  "reviewed": false,
  "limit": 5
}
```

**MCP Output**:
```
📊 Found 2 interrogations:
• Maria Garcia - ICD (HIGH) ⏳ 2024-01-15
• Robert Johnson - ILR (HIGH) ⏳ 2024-01-14
```

### Marking Interrogation as Reviewed
**Input**:
```json
{
  "id": "int-003",
  "reviewedBy": "dr-smith",
  "notes": "Elevated AF burden at 15.2%. Recommend medication adjustment and follow-up in 2 weeks."
}
```

**MCP Output**:
```
📋 Marking interrogation int-003 as reviewed...
✅ Marked as reviewed: Robert Johnson - ILR (HIGH priority)
```

### Getting Analytics
**Input**:
```json
{
  "days": 30
}
```

**MCP Output**:
```
📈 Interrogation Statistics (Last 30 days)
📊 Total: 4 interrogations
⏳ Pending Review: 1
🔴 High Priority: 2
🟡 Medium Priority: 1
🟢 Low Priority: 1
🔧 Device Types: ICD: 1, PPM: 2, ILR: 1
```

### Resource Access
**Resource**: `cardiac://patients/patient-001`

**Output**:
```json
{
  "id": "patient-001",
  "name": "John Smith",
  "dateOfBirth": "1965-03-15T00:00:00.000Z",
  "clinicId": "clinic-cardio-01",
  "devices": [
    {
      "id": "device-ppm-001",
      "type": "PPM",
      "manufacturer": "Medtronic",
      "model": "Azure XT MRI"
    }
  ],
  "summary": {
    "totalDevices": 1,
    "recentInterrogations": 2,
    "pendingReview": 0,
    "highPriority": 0
  }
}
```

## Configuration

### MCP Server Config
```typescript
const server = await cqrs.run({
    mcp: {
        adapter: 'fastmcp',
        config: {
            name: 'Cardiac Monitoring System',
            version: '1.0.0',
            transportType: 'stdio',
            instructions: '...' // Clinical workflow instructions for AI
        }
    }
});
```

### Procedure MCP Metadata
```typescript
metadata: {
    title: 'Create Patient', // Used by MCP for UI display
    description: 'Register a new patient in the cardiac monitoring system',
    tags: ['patient-management'],
    mcp: {
        enabled: true,
        streamingHint: true,
        // Smart defaults based on procedure type:
        // Commands: readOnlyHint=false, idempotentHint=false, destructiveHint=false
        // Queries: readOnlyHint=true, idempotentHint=true, destructiveHint=false
    }
}
```

### Smart Defaults

The MCP adapter automatically applies intelligent defaults based on procedure type:

**For Queries (read operations):**
- `readOnlyHint: true` - Queries don't modify data
- `idempotentHint: true` - Same input produces same output  
- `destructiveHint: false` - Conservative default

**For Commands (write operations):**
- `readOnlyHint: false` - Commands can modify data
- `idempotentHint: false` - Commands may have side effects
- `destructiveHint: false` - Conservative default

**General metadata integration:**
- Uses `metadata.title` for MCP tool display name
- Uses `metadata.description` for tool description
- Inherits tags and other common metadata

You can always override these defaults by explicitly setting values in the MCP metadata.

## Integration with AI Assistants

This MCP server can be used with:
- **Claude Desktop**: Add to MCP configuration
- **Custom MCP Clients**: Connect via stdio transport
- **Development Tools**: Use for testing and debugging

### Example MCP Client Configuration
```json
{
  "mcpServers": {
    "blog-platform": {
      "command": "npx",
      "args": ["tsx", "path/to/index.ts"],
      "env": {}
    }
  }
}
```

## Advanced Features

### Type Safety
Full TypeScript support with proper type inference:
```typescript
// Fully typed procedure calls
const user = await cqrs.commands.createUser({
    name: "John", // ✅ string required
    email: "<EMAIL>", // ✅ valid email
    role: "admin" // ✅ valid enum value
});
```

### Error Handling
Comprehensive error handling with MCP-friendly responses:
```typescript
try {
    const post = await cqrs.commands.createPost(input);
} catch (error) {
    // Errors are automatically formatted for MCP clients
    throw new Error(`Failed to create post: ${error.message}`);
}
```

### Resource Caching
Resources can implement caching for better performance:
```typescript
load: async (args) => {
    // Implement your caching strategy here
    const cached = cache.get(args.id);
    if (cached) return cached;
    
    const result = await generateResource(args);
    cache.set(args.id, result);
    return result;
}
```

## Testing

Run the comprehensive test suite:
```bash
npm test -- --testPathPattern=mcp
```

The tests cover:
- ✅ MCP adapter functionality
- ✅ Resource and resource template handling  
- ✅ Tool execution and context passing
- ✅ Type safety and error handling
- ✅ Integration scenarios

## Next Steps

1. **Add Authentication**: Integrate with your auth system
2. **Database Integration**: Replace in-memory storage
3. **Real-time Updates**: Add WebSocket support
4. **Advanced Resources**: Implement file uploads, images
5. **Custom Transports**: Add HTTP stream support

## Related Examples

- **Example 03**: REST API integration
- **Example 04**: tRPC integration  
- **Example 07**: PubSub patterns

This MCP integration showcases the power of combining CQRS patterns with AI assistant capabilities, providing a robust foundation for building AI-enhanced applications.
