import { CQRS } from '@matricsio/datakit';
import { AppRegistry, appRegistry } from './appRegistry';
import { seedData } from './services/seed';
import { addResources } from './services/resources';
import { AppContext } from './schemas/types';

// Create CQRS instance
const cqrs = new CQRS<AppRegistry, AppContext>({
    registry: appRegistry,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getContext: ({ traceId, procedureName, procedureType, input }) => ({
        userId: 'current-doctor-id', // In real app, extract from auth
        requestId: traceId,
        clinicId: 'clinic-cardio-01', // In real app, extract from user context
    }),
});

// Add MCP resources
addResources(cqrs);

// Main function
async function main() {
    // Seed initial data
    seedData();

    // Start the MCP server
    const server = await cqrs.run({
        http: {
            adapter: 'fastify',
            config: {
                port: 3012,
                protocols: {
                    trpc: {
                        enabled: true,
                        prefix: '/trpc',
                        ui: {
                            enabled: true,
                        },
                    },
                    rest: {
                        enabled: true,
                        prefix: '/api',
                        swagger: {
                            enabled: true,
                            prefix: '/docs',
                            title: 'Cardiac Monitoring System API',
                            version: '1.0.0',
                        },
                    },
                },
            },
        },
        mcp: {
            adapter: 'fastmcp',
            config: {
                name: 'Cardiac Monitoring System',
                version: '1.0.0',
                instructions: 'This is a cardiac device monitoring system for healthcare providers.',
                transportType: 'stdio', // Use stdio for MCP client compatibility
            }
        }
    });

    process.on('SIGINT', async () => {
        await server.stop();
        process.exit(0);
    });
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

if (import.meta.url === new URL(import.meta.url).href) {
    main().catch(console.error);
}
