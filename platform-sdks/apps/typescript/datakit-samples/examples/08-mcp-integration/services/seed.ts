import { store } from './store';

export const seedData = () => {
    const now = new Date();
    
    // Create patients
    const patient1 = {
        id: 'patient-001',
        name: '<PERSON>',
        dateOfBirth: new Date('1965-03-15'),
        clinicId: 'clinic-cardio-01',
        lastVisit: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
        createdAt: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000), // 3 months ago
    };
    
    const patient2 = {
        id: 'patient-002',
        name: '<PERSON>',
        dateOfBirth: new Date('1972-08-22'),
        clinicId: 'clinic-cardio-01',
        lastVisit: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
        createdAt: new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000), // 6 months ago
    };
    
    const patient3 = {
        id: 'patient-003',
        name: '<PERSON>',
        dateOfBirth: new Date('1958-11-10'),
        clinicId: 'clinic-cardio-01',
        createdAt: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 1 month ago
    };
    
    store.patients.set(patient1.id, patient1);
    store.patients.set(patient2.id, patient2);
    store.patients.set(patient3.id, patient3);
    
    // Create devices
    const device1 = {
        id: 'device-ppm-001',
        patientId: patient1.id,
        type: 'PPM' as const,
        manufacturer: 'Medtronic',
        model: 'Azure XT MRI',
        serialNumber: 'PPM123456789',
        implantDate: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000), // 2 months ago
        lastTransmission: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
        createdAt: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000),
    };
    
    const device2 = {
        id: 'device-icd-001',
        patientId: patient2.id,
        type: 'ICD' as const,
        manufacturer: 'Boston Scientific',
        model: 'DYNAGEN X4',
        serialNumber: 'ICD987654321',
        implantDate: new Date(now.getTime() - 120 * 24 * 60 * 60 * 1000), // 4 months ago
        lastTransmission: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        createdAt: new Date(now.getTime() - 120 * 24 * 60 * 60 * 1000),
    };
    
    const device3 = {
        id: 'device-ilr-001',
        patientId: patient3.id,
        type: 'ILR' as const,
        manufacturer: 'Abbott',
        model: 'Confirm Rx',
        serialNumber: 'ILR555666777',
        lastTransmission: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        createdAt: new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000), // 25 days ago
    };
    
    store.devices.set(device1.id, device1);
    store.devices.set(device2.id, device2);
    store.devices.set(device3.id, device3);
    
    // Create interrogations
    const interrogation1 = {
        id: 'int-001',
        deviceId: device1.id,
        patientId: patient1.id,
        sessionDate: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000),
        type: 'InClinic' as const,
        priority: 2 as const,
        pdfUrl: 'https://storage.example.com/interrogations/int-001.pdf',
        measurements: {
            batteryVoltage: 2.8,
            leadImpedance: 520,
            pacingThreshold: 0.75,
            sensingAmplitude: 12.5,
            pacingPercentage: 85,
        },
        createdAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000),
    };
    
    const interrogation2 = {
        id: 'int-002',
        deviceId: device2.id,
        patientId: patient2.id,
        sessionDate: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
        type: 'Remote' as const,
        priority: 1 as const, // HIGH priority
        pdfUrl: 'https://storage.example.com/interrogations/int-002.pdf',
        measurements: {
            batteryVoltage: 2.5,
            shockImpedance: 45,
            vtThreshold: 180,
            vfThreshold: 240,
            episodes: 2,
            shocksDelivered: 0,
        },
        reviewedBy: 'dr-smith',
        reviewedAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000),
        notes: 'Two VT episodes detected, but self-terminated. Monitor closely.',
        createdAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
    };
    
    const interrogation3 = {
        id: 'int-003',
        deviceId: device3.id,
        patientId: patient3.id,
        sessionDate: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
        type: 'Remote' as const,
        priority: 1 as const, // HIGH priority - needs review
        pdfUrl: 'https://storage.example.com/interrogations/int-003.pdf',
        measurements: {
            afBurden: 15.2,
            heartRateVariability: 28,
            activityLevel: 3.2,
            abnormalRhythms: 12,
        },
        createdAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
    };
    
    const interrogation4 = {
        id: 'int-004',
        deviceId: device1.id,
        patientId: patient1.id,
        sessionDate: new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000),
        type: 'InClinic' as const,
        priority: 3 as const,
        pdfUrl: 'https://storage.example.com/interrogations/int-004.pdf',
        measurements: {
            batteryVoltage: 2.8,
            leadImpedance: 515,
            pacingThreshold: 0.75,
            sensingAmplitude: 12.8,
            pacingPercentage: 82,
        },
        reviewedBy: 'dr-johnson',
        reviewedAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
        notes: 'Normal parameters, routine follow-up.',
        createdAt: new Date(now.getTime() - 8 * 24 * 60 * 60 * 1000),
    };
    
    store.interrogations.set(interrogation1.id, interrogation1);
    store.interrogations.set(interrogation2.id, interrogation2);
    store.interrogations.set(interrogation3.id, interrogation3);
    store.interrogations.set(interrogation4.id, interrogation4);
};
