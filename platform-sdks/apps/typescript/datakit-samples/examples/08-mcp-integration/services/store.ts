import { Patient, PatientSchema } from '../schemas/patient';
import { <PERSON><PERSON>, DeviceSchema } from '../schemas/device';
import { Interrogation } from '../schemas/interrogation';
import { DeviceType, Priority, TransmissionType } from '../schemas/types';
import { z } from 'zod';

// Patient input schemas
export const createPatientInput = z.object({
    name: z.string().min(1),
    dateOfBirth: z.date(),
    clinicId: z.string().min(1).describe('The clinic to which the patient belongs'),
});

export const updatePatientInput = z.object({
    id: z.string(),
    name: z.string().optional(),
    lastVisit: z.date().optional(),
    dateOfBirth: z.date().optional(),
});

export const getPatientInput = z.object({
    id: z.string(),
    includeDevices: z.boolean().default(false),
});

export const getPatientsInput = z.object({
    clinicId: z.string().optional(),
    limit: z.number().min(1).max(100).default(10),
    offset: z.number().min(0).default(0),
});

// Device input schemas
export const registerDeviceInput = z.object({
    patientId: z.string(),
    type: DeviceType,
    manufacturer: z.string(),
    model: z.string(),
    serialNumber: z.string(),
    implantDate: z.date().optional(),
});

export const updateDeviceInput = z.object({
    id: z.string(),
    lastTransmission: z.date().optional(),
});

export const getDevicesByPatientInput = z.object({
    patientId: z.string(),
});

// Interrogation input schemas
export const markInterrogationReviewedInput = z.object({
    id: z.string(),
    reviewedBy: z.string(),
    notes: z.string().min(1),
});

export const getInterrogationInput = z.object({
    id: z.string(),
});

export const getInterrogationsInput = z.object({
    patientId: z.string().optional(),
    deviceType: DeviceType.optional(),
    priority: Priority.optional(),
    reviewed: z.boolean().optional(),
    sessionDateFrom: z.date().optional(),
    sessionDateTo: z.date().optional(),
    limit: z.number().min(1).max(50).default(10),
    offset: z.number().min(0).default(0),
});

export const getInterrogationStatsInput = z.object({
    clinicId: z.string().optional(),
    days: z.number().min(1).max(365).default(30),
});

export const createInterrogationInput = z.object({
    deviceSerialNumber: z.string(),
    patientIdentifier: z.string(),
    sessionDate: z.date(),
    type: TransmissionType,
    pdfUrl: z.string().url(),
    measurements: z.record(z.string(), z.any()),
    deviceInfo: z.object({
        type: DeviceType,
        manufacturer: z.string(),
        model: z.string(),
    }),
    processedBy: z.string().optional(),
    processedDate: z.date().optional(),
});

// In-memory data stores
export const store = {
    patients: new Map<string, Patient>(),
    devices: new Map<string, Device>(),
    interrogations: new Map<string, Interrogation>(),

    // Patient operations
    createPatient: (input: z.infer<typeof createPatientInput>): Patient => {
        const id = `patient-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const patient: Patient = {
            id,
            name: input.name,
            dateOfBirth: input.dateOfBirth,
            clinicId: input.clinicId,
            createdAt: new Date(),
        };
        store.patients.set(id, patient);
        return patient;
    },

    updatePatient: (input: z.infer<typeof updatePatientInput>): Patient => {
        const patient = store.patients.get(input.id);
        if (!patient) {
            throw new Error(`Patient not found: ${input.id}`);
        }

        const updatedPatient: Patient = {
            ...patient,
            name: input.name ?? patient.name,
            dateOfBirth: input.dateOfBirth ?? patient.dateOfBirth,
            lastVisit: input.lastVisit ?? patient.lastVisit,
        };
        store.patients.set(input.id, updatedPatient);
        return updatedPatient;
    },

    getPatient: async (id: string, includeDevices: boolean = false) => {
        const patient = store.patients.get(id);
        if (!patient) {
            throw new Error(`Patient not found: ${id}`);
        }

        if (!includeDevices) {
            return { patient };
        }

        // Get associated devices
        const devices = Array.from(store.devices.values())
            .filter(d => d.patientId === id);

        return {
            patient,
            devices,
        };
    },

    getPatients: (input: z.infer<typeof getPatientsInput>) => {
        let patients = Array.from(store.patients.values());

        // Apply clinic filter if provided
        if (input.clinicId) {
            patients = patients.filter(p => p.clinicId === input.clinicId);
        }

        // Sort by creation date (newest first)
        patients.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

        return {
            patients: patients.slice(input.offset, input.offset + input.limit),
            total: patients.length,
        };
    },

    // Device operations
    registerDevice: (input: z.infer<typeof registerDeviceInput>): Device => {
        // Verify patient exists
        if (!store.patients.has(input.patientId)) {
            throw new Error(`Patient not found: ${input.patientId}`);
        }

        const id = `device-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const device: Device = {
            id,
            patientId: input.patientId,
            type: input.type,
            manufacturer: input.manufacturer,
            model: input.model,
            serialNumber: input.serialNumber,
            implantDate: input.implantDate,
            createdAt: new Date(),
        };
        store.devices.set(id, device);
        return device;
    },

    updateDevice: (input: z.infer<typeof updateDeviceInput>): Device => {
        const device = store.devices.get(input.id);
        if (!device) {
            throw new Error(`Device not found: ${input.id}`);
        }

        const updatedDevice: Device = {
            ...device,
            lastTransmission: input.lastTransmission ?? device.lastTransmission,
        };
        store.devices.set(input.id, updatedDevice);
        return updatedDevice;
    },

    getDevicesByPatient: (patientId: string) => {
        // Verify patient exists
        if (!store.patients.has(patientId)) {
            throw new Error(`Patient not found: ${patientId}`);
        }

        const devices = Array.from(store.devices.values())
            .filter(d => d.patientId === patientId);

        return {
            devices,
            total: devices.length,
        };
    },

    // Interrogation operations
    markInterrogationReviewed: (input: z.infer<typeof markInterrogationReviewedInput>): Interrogation => {
        const interrogation = store.interrogations.get(input.id);
        if (!interrogation) {
            throw new Error(`Interrogation not found: ${input.id}`);
        }

        const updatedInterrogation: Interrogation = {
            ...interrogation,
            reviewedBy: input.reviewedBy,
            reviewedAt: new Date(),
            notes: input.notes,
        };
        store.interrogations.set(input.id, updatedInterrogation);
        return updatedInterrogation;
    },

    getInterrogation: async (id: string) => {
        const interrogation = store.interrogations.get(id);
        if (!interrogation) {
            throw new Error(`Interrogation not found: ${id}`);
        }

        const device = store.devices.get(interrogation.deviceId);
        if (!device) {
            throw new Error(`Device not found: ${interrogation.deviceId}`);
        }

        const patient = store.patients.get(interrogation.patientId);
        if (!patient) {
            throw new Error(`Patient not found: ${interrogation.patientId}`);
        }

        return {
            interrogation,
            device,
            patient,
        };
    },

    getInterrogations: (input: z.infer<typeof getInterrogationsInput>) => {
        let interrogations = Array.from(store.interrogations.values());

        // Apply filters
        if (input.patientId) {
            interrogations = interrogations.filter(i => i.patientId === input.patientId);
        }

        if (input.deviceType) {
            const deviceIds = Array.from(store.devices.values())
                .filter(d => d.type === input.deviceType)
                .map(d => d.id);
            interrogations = interrogations.filter(i => deviceIds.includes(i.deviceId));
        }

        if (input.priority) {
            interrogations = interrogations.filter(i => i.priority === input.priority);
        }

        if (input.reviewed !== undefined) {
            interrogations = interrogations.filter(i => (!!i.reviewedBy) === input.reviewed);
        }

        if (input.sessionDateFrom) {
            interrogations = interrogations.filter(i => i.sessionDate >= input.sessionDateFrom!);
        }

        if (input.sessionDateTo) {
            interrogations = interrogations.filter(i => i.sessionDate <= input.sessionDateTo!);
        }

        // Sort by session date (newest first)
        interrogations.sort((a, b) => b.sessionDate.getTime() - a.sessionDate.getTime());

        // Get full context for each interrogation
        const results = interrogations
            .slice(input.offset, input.offset + input.limit)
            .map(interrogation => {
                const device = store.devices.get(interrogation.deviceId)!;
                const patient = store.patients.get(interrogation.patientId)!;
                return { interrogation, device, patient };
            });

        return {
            interrogations: results,
            total: interrogations.length,
        };
    },

    getInterrogationStats: (input: z.infer<typeof getInterrogationStatsInput>) => {
        const cutoffDate = new Date(Date.now() - input.days * 24 * 60 * 60 * 1000);
        let interrogations = Array.from(store.interrogations.values())
            .filter(i => i.sessionDate >= cutoffDate);

        // Apply clinic filter if provided
        if (input.clinicId) {
            const patientIds = Array.from(store.patients.values())
                .filter(p => p.clinicId === input.clinicId)
                .map(p => p.id);
            interrogations = interrogations.filter(i => patientIds.includes(i.patientId));
        }

        // Calculate statistics
        const stats = {
            totalInterrogations: interrogations.length,
            pendingReview: interrogations.filter(i => !i.reviewedBy).length,
            highPriority: interrogations.filter(i => i.priority === 1).length,
            mediumPriority: interrogations.filter(i => i.priority === 2).length,
            lowPriority: interrogations.filter(i => i.priority === 3).length,
            deviceBreakdown: [] as { type: string; count: number }[],
            inclinicTransmissions: interrogations.filter(i => i.type === 'InClinic').length,
            remoteTransmissions: interrogations.filter(i => i.type === 'Remote').length,
        };

        // Calculate device type breakdown
        const deviceCounts = new Map<DeviceType, number>();
        interrogations.forEach(i => {
            const device = store.devices.get(i.deviceId)!;
            deviceCounts.set(device.type, (deviceCounts.get(device.type) || 0) + 1);
        });

        stats.deviceBreakdown = Array.from(deviceCounts.entries())
            .map(([type, count]) => ({ type, count }));

        return stats;
    },

    createInterrogation: (input: z.infer<typeof createInterrogationInput>) => {
        // Find or create device
        let device = Array.from(store.devices.values())
            .find(d => d.serialNumber === input.deviceSerialNumber);

        // Find patient
        const patient = Array.from(store.patients.values())
            .find(p => p.id === input.patientIdentifier);

        if (!patient) {
            throw new Error(`Patient not found: ${input.patientIdentifier}`);
        }

        // Create device if not found
        if (!device) {
            device = store.registerDevice({
                patientId: patient.id,
                type: input.deviceInfo.type,
                manufacturer: input.deviceInfo.manufacturer,
                model: input.deviceInfo.model,
                serialNumber: input.deviceSerialNumber,
            });
        }

        // Update device last transmission
        store.updateDevice({
            id: device.id,
            lastTransmission: input.sessionDate,
        });

        // Create interrogation
        const id = `interrogation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const priority = calculatePriority(input.measurements, device.type);
        const interrogation: Interrogation = {
            id,
            deviceId: device.id,
            patientId: patient.id,
            sessionDate: input.sessionDate,
            type: input.type,
            priority,
            pdfUrl: input.pdfUrl,
            measurements: input.measurements,
            createdAt: new Date(),
        };
        store.interrogations.set(id, interrogation);

        return {
            interrogationId: id,
            deviceId: device.id,
            patientId: patient.id,
            priority,
        };
    },
};

// Helper functions
export const getPriorityLabel = (priority: 1 | 2 | 3): string => {
    switch (priority) {
        case 1: return 'HIGH';
        case 2: return 'MEDIUM';
        case 3: return 'LOW';
    }
};

// Calculate priority based on measurements and device type
function calculatePriority(measurements: Record<string, any>, deviceType: DeviceType): Priority {
    // This is a simplified example. In a real system, this would be a complex
    // algorithm based on device-specific thresholds and medical guidelines.
    
    // For demonstration, we'll randomly assign priorities
    // In reality, this would analyze the measurements based on device type
    const random = Math.random();
    if (random < 0.1) return 1; // 10% high priority
    if (random < 0.3) return 2; // 20% medium priority
    return 3; // 70% low priority
}
