import type { CQRS } from '@matricsio/datakit';
import type { AppRegistry } from '../appRegistry';
import type { AppContext } from '../schemas/types';
import { store } from './store';

export const addResources = (cqrs: CQRS<AppRegistry, AppContext>) => {
    // Add MCP Resources
    cqrs.addResource({
        uri: 'cardiac://schemas/patient',
        name: 'Patient Schema',
        description: 'JSON schema for patient objects',
        mimeType: 'application/json',
        load: () => ({
            text: JSON.stringify({
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    dateOfBirth: { type: 'string', format: 'date-time' },
                    clinicId: { type: 'string' },
                    lastVisit: { type: 'string', format: 'date-time' },
                    createdAt: { type: 'string', format: 'date-time' }
                },
                required: ['id', 'name', 'dateOfBirth', 'clinicId', 'createdAt']
            }, null, 2),
            mimeType: 'application/json'
        })
    });

    cqrs.addResource({
        uri: 'cardiac://schemas/device',
        name: 'Device Schema',
        description: 'JSON schema for cardiac device objects',
        mimeType: 'application/json',
        load: () => ({
            text: JSON.stringify({
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    patientId: { type: 'string' },
                    type: { type: 'string', enum: ['PPM', 'ILR', 'ICD'] },
                    manufacturer: { type: 'string' },
                    model: { type: 'string' },
                    serialNumber: { type: 'string' },
                    implantDate: { type: 'string', format: 'date-time' },
                    lastTransmission: { type: 'string', format: 'date-time' },
                    createdAt: { type: 'string', format: 'date-time' }
                },
                required: ['id', 'patientId', 'type', 'manufacturer', 'model', 'serialNumber', 'createdAt']
            }, null, 2),
            mimeType: 'application/json'
        })
    });

    cqrs.addResource({
        uri: 'cardiac://schemas/interrogation',
        name: 'Interrogation Schema',
        description: 'JSON schema for device interrogation objects',
        mimeType: 'application/json',
        load: () => ({
            text: JSON.stringify({
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    deviceId: { type: 'string' },
                    patientId: { type: 'string' },
                    sessionDate: { type: 'string', format: 'date-time' },
                    type: { type: 'string', enum: ['DAILY', 'MONTHLY'] },
                    priority: { type: 'number', enum: [1, 2, 3] },
                    pdfUrl: { type: 'string', format: 'uri' },
                    measurements: { type: 'object' },
                    reviewedBy: { type: 'string' },
                    reviewedAt: { type: 'string', format: 'date-time' },
                    notes: { type: 'string' },
                    createdAt: { type: 'string', format: 'date-time' }
                },
                required: ['id', 'deviceId', 'patientId', 'sessionDate', 'type', 'priority', 'pdfUrl', 'measurements', 'createdAt']
            }, null, 2),
            mimeType: 'application/json'
        })
    });

    // Add MCP Resource Templates
    cqrs.addResourceTemplate({
        uriTemplate: 'cardiac://patients/{id}',
        name: 'Patient Profile',
        description: 'Get comprehensive patient profile with device and interrogation history',
        mimeType: 'application/json',
        arguments: [
            { name: 'id', description: 'Patient ID', required: true }
        ],
        load: async (args) => {
            const patient = store.patients.get(args.id);
            if (!patient) {
                return {
                    text: JSON.stringify({ error: 'Patient not found' }),
                    mimeType: 'application/json'
                };
            }
            
            const patientDevices = Array.from(store.devices.values()).filter(d => d.patientId === args.id);
            const patientInterrogations = Array.from(store.interrogations.values())
                .filter(i => i.patientId === args.id)
                .sort((a, b) => b.sessionDate.getTime() - a.sessionDate.getTime())
                .slice(0, 10); // Last 10 interrogations
            
            const pendingReview = patientInterrogations.filter(i => !i.reviewedBy).length;
            const highPriority = patientInterrogations.filter(i => i.priority === 1).length;
            
            return {
                text: JSON.stringify({
                    ...patient,
                    devices: patientDevices,
                    recentInterrogations: patientInterrogations,
                    summary: {
                        totalDevices: patientDevices.length,
                        recentInterrogations: patientInterrogations.length,
                        pendingReview,
                        highPriority,
                        lastTransmission: patientDevices.reduce((latest, device) => {
                            return device.lastTransmission && (!latest || device.lastTransmission > latest) 
                                ? device.lastTransmission 
                                : latest;
                        }, null as Date | null)
                    }
                }, null, 2),
                mimeType: 'application/json'
            };
        }
    });

    cqrs.addResourceTemplate({
        uriTemplate: 'cardiac://interrogations/{id}/pdf',
        name: 'Interrogation PDF Report',
        description: 'Access the manufacturer PDF report for an interrogation',
        mimeType: 'text/plain',
        arguments: [
            { name: 'id', description: 'Interrogation ID', required: true }
        ],
        load: async (args) => {
            const interrogation = store.interrogations.get(args.id);
            if (!interrogation) {
                return {
                    text: 'Interrogation not found',
                    mimeType: 'text/plain'
                };
            }
            
            return {
                text: `PDF Report Access: ${interrogation.pdfUrl}

Note: This is a reference to the manufacturer's PDF report.
In a real system, this would either:
1. Return the actual PDF content
2. Provide a secure download link
3. Return extracted text content from the PDF

Interrogation Details:
- ID: ${interrogation.id}
- Patient: ${store.patients.get(interrogation.patientId)?.name || 'Unknown'}
- Date: ${interrogation.sessionDate.toISOString()}
- Priority: ${interrogation.priority}
- Type: ${interrogation.type}`,
                mimeType: 'text/plain'
            };
        }
    });
};
