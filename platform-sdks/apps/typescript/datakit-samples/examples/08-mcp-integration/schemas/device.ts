import { z } from 'zod';
import { DeviceType } from './types';
import { 
    store, 
    registerDeviceInput, 
    updateDeviceInput, 
    getDevicesByPatientInput 
} from '../services/store';
import { define } from '../appCQRS';

// Schema
export const DeviceSchema = z.object({
    id: z.string(),
    patientId: z.string(),
    type: DeviceType,
    manufacturer: z.string(),
    model: z.string(),
    serialNumber: z.string(),
    implantDate: z.date().optional(),
    lastTransmission: z.date().optional(),
    createdAt: z.date(),
});

export type Device = z.infer<typeof DeviceSchema>;

const { command, query } = define;

// Commands and Queries
export const registerDevice = command({
    input: registerDeviceInput.omit({
        implantDate: true,
    }).extend({
        implantDate: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
            message: "Invalid date format"
        }),
    }),
    output: DeviceSchema,
    metadata: {
        title: 'Register Device',
        description: 'Associates a cardiac monitoring device with a patient',
        tags: ['Device Management'],
        rest: {
            method: 'POST',
            path: '/devices',
        },
        mcp: {},
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.registerDevice({
            ...input,
            implantDate: input.implantDate ? new Date(input.implantDate) : undefined,
        });
    },
});

export const updateDevice = command({
    input: updateDeviceInput.omit({
        lastTransmission: true,
    }).extend({
        lastTransmission: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
            message: "Invalid date format"
        }),
    }),
    output: DeviceSchema,
    metadata: {
        title: 'Update Device',
        description: 'Updates device details like last transmission date',
        tags: ['Device Management'],
        rest: {
            method: 'PUT',
            path: '/devices/update',
        },
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.updateDevice({
            id: input.id,
            lastTransmission: input.lastTransmission ? new Date(input.lastTransmission) : undefined,
        });
    },
});

export const getDevicesByPatient = query({
    input: getDevicesByPatientInput,
    output: z.object({
        devices: z.array(DeviceSchema),
        total: z.number(),
    }),
    metadata: {
        title: 'Get Devices by Patient',
        description: 'Retrieves all devices associated with a patient',
        tags: ['Device Management'],
        rest: {
            method: 'GET',
            path: '/devices/byPatient',
        },
        mcp: {},
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.getDevicesByPatient(input.patientId);
    },
});

// Export all procedures for registry
export const deviceProcedures = {
    registerDevice,
    updateDevice,
    getDevicesByPatient,
};
