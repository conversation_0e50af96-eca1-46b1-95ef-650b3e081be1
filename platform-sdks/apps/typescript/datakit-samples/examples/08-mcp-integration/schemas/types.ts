import { z } from 'zod';

// Common types used across schemas
export type AppContext = {
    userId?: string;
    requestId: string;
    clinicId?: string;
};

// Device type enum
export const DeviceType = z.enum(['PPM', 'ILR', 'ICD']);
export type DeviceType = z.infer<typeof DeviceType>;

// Priority type
export const Priority = z.union([z.literal(1), z.literal(2), z.literal(3)]);
export type Priority = z.infer<typeof Priority>;

// Transmission type
export const TransmissionType = z.enum(['InClinic', 'Remote']);
export type TransmissionType = z.infer<typeof TransmissionType>;
