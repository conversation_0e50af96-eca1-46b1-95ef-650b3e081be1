import { z } from 'zod';
import { DeviceType, Priority, TransmissionType } from './types';
import { PatientSchema } from './patient';
import { DeviceSchema } from './device';
import {
    createInterrogationInput,
    markInterrogationReviewedInput,
    getInterrogationInput,
    getInterrogationsInput,
    getInterrogationStatsInput,
    store
} from '../services/store';
import { define } from '../appCQRS';

const { command, query } = define;

// Schema
export const InterrogationSchema = z.object({
    id: z.string(),
    deviceId: z.string(),
    patientId: z.string(),
    sessionDate: z.date(),
    type: TransmissionType,
    priority: Priority,
    pdfUrl: z.string(),
    measurements: z.record(z.any()),
    reviewedBy: z.string().optional(),
    reviewedAt: z.date().optional(),
    notes: z.string().optional(),
    createdAt: z.date(),
});

export type Interrogation = z.infer<typeof InterrogationSchema>;

// Commands and Queries
export const markInterrogationReviewed = command({
    input: markInterrogationReviewedInput,
    output: InterrogationSchema,
    metadata: {
        title: 'Mark Interrogation Reviewed',
        description: 'Records review details and clinical notes for a device interrogation',
        tags: ['Interrogation Management'],
        rest: {
            method: 'POST',
            path: '/interrogations/review',
        },
        mcp: {},
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.markInterrogationReviewed(input);
    },
});

export const getInterrogation = query({
    input: getInterrogationInput,
    output: z.object({
        interrogation: InterrogationSchema,
        patient: PatientSchema,
        device: DeviceSchema,
    }),
    metadata: {
        title: 'Get Interrogation',
        description: 'Retrieves comprehensive information about a device interrogation',
        tags: ['Interrogation Management'],
        rest: {
            method: 'GET',
            path: '/interrogations/getInterrogation',
        },
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.getInterrogation(input.id);
    },
});

export const getInterrogations = query({
    input: getInterrogationsInput.omit({
        sessionDateFrom: true,
        sessionDateTo: true,
    }).extend({
        sessionDateFrom: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
            message: "Invalid date format"
        }),
        sessionDateTo: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
            message: "Invalid date format"
        }),
    }),
    output: z.object({
        interrogations: z.array(z.object({
            interrogation: InterrogationSchema,
            patient: PatientSchema,
            device: DeviceSchema,
        })),
        total: z.number(),
    }),
    metadata: {
        title: 'Get Interrogations',
        description: 'Retrieves a filtered list of device interrogations with patient and device context',
        tags: ['Interrogation Management'],
        rest: {
            method: 'GET',
            path: '/interrogations',
        },
        mcp: {
            enabled: true,
            readOnlyHint: true,
            idempotentHint: true,
        },
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.getInterrogations({
            ...input,
            sessionDateFrom: input.sessionDateFrom ? new Date(input.sessionDateFrom) : undefined,
            sessionDateTo: input.sessionDateTo ? new Date(input.sessionDateTo) : undefined,
        });
    },
});

export const getInterrogationStats = query({
    input: getInterrogationStatsInput,
    output: z.object({
        totalInterrogations: z.number(),
        pendingReview: z.number(),
        highPriority: z.number(),
        mediumPriority: z.number(),
        lowPriority: z.number(),
        deviceBreakdown: z.array(z.object({
            type: z.string(),
            count: z.number(),
        })),
        inclinicTransmissions: z.number(),
        remoteTransmissions: z.number(),
    }),
    metadata: {
        title: 'Get Interrogation Stats',
        description: 'Provides analytics about interrogation volume, priorities, and device types',
        tags: ['Interrogation Management'],
        rest: {
            path: '/interrogations/stats',
        },
        mcp: {
            enabled: true,
        },
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.getInterrogationStats(input);
    },
});

export const createInterrogation = command({
    input: createInterrogationInput.omit({
        sessionDate: true,
        processedDate: true,
        processedBy: true,
    }).extend({
        sessionDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
            message: "Invalid date format"
        }),
    }),
    output: z.object({
        interrogationId: z.string(),
        deviceId: z.string(),
        patientId: z.string(),
        priority: Priority,
    }),
    metadata: {
        title: 'Process an Interrogation',
        description: 'Marks interrogations as processed',
        tags: ['Interrogation Management'],
        rest: {
            method: 'POST',
            path: '/interrogations/process',
        },
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.createInterrogation({
            ...input,
            sessionDate: new Date(input.sessionDate),
            processedDate: new Date(),
            processedBy: ctx.userId,
        });
    },
});

// Export all procedures for registry
export const interrogationProcedures = {
    markInterrogationReviewed,
    getInterrogation,
    getInterrogations,
    getInterrogationStats,
    createInterrogation,
};
