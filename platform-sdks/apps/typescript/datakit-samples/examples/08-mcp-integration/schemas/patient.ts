import { z } from 'zod';
import { 
    store, 
    createPatientInput, 
    updatePatientInput, 
    getPatientInput, 
    getPatientsInput 
} from '../services/store';
import { define } from '../appCQRS';
import { DeviceSchema } from './device';

const { command, query } = define;

export const PatientSchema = z.object({
    id: z.string(),
    name: z.string(),
    dateOfBirth: z.date(),
    clinicId: z.string().describe('The clinic to which the patient belongs'),
    lastVisit: z.date().optional(),
    createdAt: z.date(),
});

export type Patient = z.infer<typeof PatientSchema>;

export const createPatient = command({
    input: createPatientInput.omit({
        dateOfBirth: true,
    }).extend({
        dateOfBirth: z.string().refine((date) => !isNaN(Date.parse(date)), {
            message: "Invalid date format"
        }),
    }),
    output: PatientSchema,
    metadata: {
        title: 'Create Patient',
        description: 'Creates a new patient record with basic information',
        tags: ['Patient Management'],
        rest: {
            method: 'POST',
            path: '/patients',
        },
        mcp: {},
    },
    handler: async ({ input, ctx }) => {
        return store.createPatient({
            name: input.name,
            dateOfBirth: new Date(input.dateOfBirth),
            clinicId: input.clinicId,
        });
    },
});

export const updatePatient = command({
    input: updatePatientInput.omit({
        lastVisit: true,
    }).extend({
        lastVisit: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
            message: "Invalid date format"
        }),
    }),
    output: PatientSchema,
    metadata: {
        title: 'Update Patient',
        description: 'Updates patient details like name and last visit date',
        tags: ['Patient Management'],
        rest: {
            method: 'PUT',
            path: '/patients/update',
        },
        mcp: {},
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.updatePatient({
            id: input.id,
            name: input.name,
            dateOfBirth: input.dateOfBirth ? new Date(input.dateOfBirth) : undefined,
            lastVisit: input.lastVisit ? new Date(input.lastVisit) : undefined,
        });
    },
});

export const getPatient = query({
    input: getPatientInput,
    output: z.object({
        patient: PatientSchema,
        devices: z.array(DeviceSchema).optional(),
    }),
    metadata: {
        title: 'Get Patient',
        description: 'Retrieves detailed patient information and optionally includes associated devices',
        tags: ['Patient Management'],
        rest: {
            method: 'GET',
            path: '/patients/getPatient',
        },
        mcp: {
            enabled: true,
            readOnlyHint: true,
            idempotentHint: true,
        },
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.getPatient(input.id, input.includeDevices);
    },
});

export const getPatients = query({
    input: getPatientsInput,
    output: z.object({
        patients: z.array(PatientSchema),
        total: z.number(),
    }),
    metadata: {
        title: 'List Patients',
        description: 'Retrieves a paginated list of patients with optional clinic filtering',
        tags: ['Patient Management'],
        rest: {
            path: '/patients',
        },
        mcp: {
            readOnlyHint: true,
            idempotentHint: true,
        },
        trpc: {},
    },
    handler: async ({ input, ctx }) => {
        return store.getPatients(input);
    },
});

// Export all procedures for registry
export const patientProcedures = {
    createPatient,
    updatePatient,
    getPatient,
    getPatients,
};
