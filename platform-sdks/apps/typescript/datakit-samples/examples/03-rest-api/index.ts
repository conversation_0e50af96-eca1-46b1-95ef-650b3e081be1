import { z } from 'zod';
import { CQRS } from '@matricsio/datakit';

// Context for REST requests
type RestContext = {
  userId?: string;
  requestId: string;
  userAgent?: string;
  authorization?: string;
};

// Create CQRS instance
const { define, registry } = CQRS.create<RestContext>();
const { command, query } = define;

// In-memory storage
const posts = new Map<string, { id: string; title: string; content: string; authorId: string; createdAt: Date }>();
const users = new Map<string, { id: string; name: string; email: string }>();

// Seed some data
users.set('user-1', { id: 'user-1', name: '<PERSON>', email: '<EMAIL>' });
users.set('user-2', { id: 'user-2', name: '<PERSON>', email: '<EMAIL>' });

let postIdCounter = 1;

// Health check endpoint
const healthCheck = query({
  input: z.void(),
  output: z.object({
    status: z.string(),
    timestamp: z.number(),
    version: z.string(),
  }),
  metadata: {
    title: 'Health Check',
    description: 'Check if the API is running',
    tags: ['System'],
    rest: {
      method: 'GET',
      path: '/health',
    },
  },
  handler: async () => {
    return {
      status: 'healthy',
      timestamp: Date.now(),
      version: '1.0.0',
    };
  },
});

// Get all posts
const getPosts = query({
  input: z.object({
    limit: z.number().min(1).max(100).default(10),
    offset: z.number().min(0).default(0),
  }).optional(),
  output: z.object({
    posts: z.array(z.object({
      id: z.string(),
      title: z.string(),
      content: z.string(),
      authorId: z.string(),
      createdAt: z.string(),
    })),
    total: z.number(),
    limit: z.number(),
    offset: z.number(),
  }),
  metadata: {
    title: 'List Posts',
    description: 'Get a paginated list of all posts',
    tags: ['Posts'],
    rest: {
      method: 'GET',
      path: '/posts',
      openapi: {
        operationId: 'listPosts',
      },
    },
  },
  handler: async ({ input }) => {
    const { limit = 10, offset = 0 } = input || {};
    const allPosts = Array.from(posts.values());
    const paginatedPosts = allPosts.slice(offset, offset + limit);

    return {
      posts: paginatedPosts.map(post => ({
        ...post,
        createdAt: post.createdAt.toISOString(),
      })),
      total: allPosts.length,
      limit,
      offset,
    };
  },
});

// Get single post by ID
const getPost = query({
  input: z.object({
    id: z.string().min(1, 'Post ID is required'),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    authorId: z.string(),
    createdAt: z.string(),
    author: z.object({
      id: z.string(),
      name: z.string(),
      email: z.string(),
    }).optional(),
  }),
  metadata: {
    title: 'Get Post',
    description: 'Get a single post by its ID',
    tags: ['Posts'],
    rest: {
      method: 'GET',
      path: '/posts/:id',
      openapi: {
        operationId: 'getPostById',
      },
    },
  },
  handler: async ({ input }) => {
    const post = posts.get(input.id);
    if (!post) {
      throw new Error(`Post with ID ${input.id} not found`);
    }

    const author = users.get(post.authorId);

    return {
      ...post,
      createdAt: post.createdAt.toISOString(),
      author,
    };
  },
});

// Create new post
const createPost = command({
  input: z.object({
    title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
    content: z.string().min(1, 'Content is required').max(5000, 'Content too long'),
    authorId: z.string().min(1, 'Author ID is required'),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    authorId: z.string(),
    createdAt: z.string(),
  }),
  metadata: {
    title: 'Create Post',
    description: 'Create a new blog post',
    tags: ['Posts'],
    rest: {
      method: 'POST',
      path: '/posts',
      openapi: {
        operationId: 'createPost',
      },
    },
  },
  handler: async ({ input, ctx }) => {
    // Verify author exists
    const author = users.get(input.authorId);
    if (!author) {
      throw new Error(`Author with ID ${input.authorId} not found`);
    }

    const postId = `post-${postIdCounter++}`;
    const post = {
      id: postId,
      title: input.title,
      content: input.content,
      authorId: input.authorId,
      createdAt: new Date(),
    };

    posts.set(postId, post);

    console.log(`📝 [${ctx.requestId}] Post created: ${post.title}`);

    return {
      ...post,
      createdAt: post.createdAt.toISOString(),
    };
  },
});

// Update post
const updatePost = command({
  input: z.object({
    id: z.string().min(1, 'Post ID is required'),
    title: z.string().min(1).max(200).optional(),
    content: z.string().min(1).max(5000).optional(),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    authorId: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
  }),
  metadata: {
    title: 'Update Post',
    description: 'Update an existing post',
    tags: ['Posts'],
    rest: {
      method: 'PATCH',
      path: '/posts/:id',
      openapi: {
        operationId: 'updatePost',
      },
    },
  },
  handler: async ({ input, ctx }) => {
    const existingPost = posts.get(input.id);
    if (!existingPost) {
      throw new Error(`Post with ID ${input.id} not found`);
    }

    const updatedPost = {
      ...existingPost,
      title: input.title ?? existingPost.title,
      content: input.content ?? existingPost.content,
    };

    posts.set(input.id, updatedPost);

    console.log(`✏️  [${ctx.requestId}] Post updated: ${updatedPost.title}`);

    return {
      ...updatedPost,
      createdAt: updatedPost.createdAt.toISOString(),
      updatedAt: new Date().toISOString(),
    };
  },
});

// Delete post
const deletePost = command({
  input: z.object({
    id: z.string().min(1, 'Post ID is required'),
  }),
  output: z.object({
    success: z.boolean(),
    deletedId: z.string(),
  }),
  metadata: {
    title: 'Delete Post',
    description: 'Delete a post by its ID',
    tags: ['Posts'],
    rest: {
      method: 'DELETE',
      path: '/posts/:id',
      openapi: {
        operationId: 'deletePost',
      },
    },
  },
  handler: async ({ input, ctx }) => {
    const post = posts.get(input.id);
    if (!post) {
      throw new Error(`Post with ID ${input.id} not found`);
    }

    posts.delete(input.id);

    console.log(`🗑️  [${ctx.requestId}] Post deleted: ${post.title}`);

    return {
      success: true,
      deletedId: input.id,
    };
  },
});

// Get all users
const getUsers = query({
  input: z.void(),
  output: z.array(z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
  })),
  metadata: {
    title: 'List Users',
    description: 'Get all users',
    tags: ['Users'],
    rest: {
      method: 'GET',
      path: '/users',
    },
  },
  handler: async () => {
    return Array.from(users.values());
  },
});

// Create registry
const appRegistry = registry({
  healthCheck,
  getPosts,
  getPost,
  createPost,
  updatePost,
  deletePost,
  getUsers,
});

// Create CQRS instance with context extraction from REST headers
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: (params) => {
    const headers = params?.rest?.headers || {};

    return {
      requestId: params?.traceId || `req-${Date.now()}`,
      userAgent: headers['user-agent'],
      authorization: headers['authorization'],
      userId: headers['x-user-id'], // Custom header for user ID
    };
  },
});

async function runRestExample() {
  console.log('🌐 REST API Example\n');

  try {
    console.log('Starting REST API server...');

    // Start the server with REST and Swagger documentation
    await cqrs.run({
      http: {
        adapter: 'fastify',
        config: {
          port: 3001,
          protocols: {
            rest: {
              enabled: true,
              prefix: '/api',
              swagger: {
                enabled: true,
                prefix: '/docs',
                title: 'Blog API',
                version: '1.0.0',
              },
            },
            trpc: {
              enabled: false, // Disable tRPC for this example
            },
          },
          cors: {
            origin: ['*'],
            credentials: true,
          },
        },
      },
    });

    console.log('✅ Server started successfully!');
    console.log('📖 API Documentation: http://localhost:3001/docs');
    console.log('🔍 Health Check: http://localhost:3001/api/health');
    console.log('📝 Posts API: http://localhost:3001/api/posts');
    console.log('👥 Users API: http://localhost:3001/api/users');
    console.log('\nPress Ctrl+C to stop the server');

    // Keep the server running
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down server...');
      await cqrs.stop();
      console.log('✅ Server stopped');
      process.exit(0);
    });

    // Demonstrate programmatic usage
    console.log('\n🧪 Testing programmatic usage:');

    // Create a post
    const newPost = await cqrs.commands.createPost({
      title: 'Hello REST API',
      content: 'This post was created programmatically!',
      authorId: 'user-1',
    });
    console.log('✅ Created post:', newPost.title);

    // Get the post
    const retrievedPost = await cqrs.queries.getPost({ id: newPost.id });
    console.log('✅ Retrieved post:', retrievedPost.title);

    // List all posts
    const allPosts = await cqrs.queries.getPosts({ limit: 5, offset: 0 });
    console.log('✅ Listed posts:', allPosts.posts.length, 'posts');

  } catch (error) {
    console.error('❌ Error in REST example:', error);
    process.exit(1);
  }
}

// Run the example
runRestExample().catch(console.error);
