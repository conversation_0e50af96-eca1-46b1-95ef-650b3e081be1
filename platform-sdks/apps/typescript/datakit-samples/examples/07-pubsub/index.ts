import { createPublisher, createSubscriber } from '@matricsio/datakit/pubsub';

const APP_PORT = process.env.APP_PORT ?? '3000';
const DAPR_PORT = process.env.DAPR_PORT ?? '3500';
const PUBSUB_NAME = 'pubsub';

/**
Usage:

First start dapr
`dapr run --app-id demo --app-port 3000 --dapr-http-port 3500`

Then run the example
`pnpm example:pubsub`

*/
async function main() {
  // 1. Start the subscriber first (it's a real HTTP server)
  console.log(`[pubsub-demo] Starting subscriber on port ${APP_PORT}`);
  const subscriber = createSubscriber({
    port: Number(APP_PORT),
    pubsubName: PUBSUB_NAME,
  });

  // 2. Subscribe to messages
  await subscriber.subscribe('demo-topic', async (msg) => {
    console.log('[pubsub-demo] Subscriber received:', msg);
    setTimeout(() => process.exit(0), 2000);
  });

  // 3. Start the HTTP server
  await subscriber.start();
  console.log('[pubsub-demo] Subscriber is running');

  // 4. Create a publisher pointing at Dapr
  const publisher = createPublisher({
    daprPort: Number(DAPR_PORT),
    pubsubName: PUBSUB_NAME,
  });

  // 5. Publish a test message
  const payload = { hello: 'world', ts: Date.now() };
  console.log('[pubsub-demo] Publishing message:', payload);
  await publisher.publish('demo-topic', payload);

  // Keep running until interrupted
  process.on('SIGINT', async () => {
    await subscriber.close();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    await subscriber.close();
    process.exit(0);
  });
}

// Run the demo
main().catch((err) => {
  console.error(err);
  process.exit(1);
});
