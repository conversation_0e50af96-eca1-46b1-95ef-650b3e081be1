import { z } from 'zod';
import { CQRS } from '@matricsio/datakit';

// Define your application context
type AppContext = {
  userId: string;
  requestId: string;
};

// Create CQRS instance
const { define, registry } = CQRS.create<AppContext>();
const { command, query } = define;

// In-memory storage for this example
const users = new Map<string, { id: string; name: string; email: string }>();
let userIdCounter = 1;

// Define a command to create users
const createUser = command({
  input: z.object({
    name: z.string().min(1, 'Name is required'),
    email: z.string().email('Invalid email format'),
  }),
  output: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
  }),
  handler: async ({ input, ctx }) => {
    const userId = `user-${userIdCounter++}`;
    const user = {
      id: userId,
      name: input.name,
      email: input.email,
    };

    users.set(userId, user);

    console.log(`[${ctx.requestId}] User created by ${ctx.userId}:`, user);
    return user;
  },
});

// Define a query to get users
const getUser = query({
  input: z.object({
    id: z.string().min(1, 'User ID is required'),
  }),
  output: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
  }),
  handler: async ({ input, ctx }) => {
    const user = users.get(input.id);

    if (!user) {
      throw new Error(`User with ID ${input.id} not found`);
    }

    console.log(`[${ctx.requestId}] User retrieved by ${ctx.userId}:`, user);
    return user;
  },
});

// Define a query to list all users
const listUsers = query({
  input: z.undefined(),
  output: z.array(z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
  })),
  handler: async ({ ctx }) => {
    const allUsers = Array.from(users.values());
    console.log(`[${ctx.requestId}] Listed ${allUsers.length} users for ${ctx.userId}`);
    return allUsers;
  },
});

// Create registry with all procedures
const appRegistry = registry({
  createUser,
  getUser,
  listUsers,
});

// Create CQRS instance with context
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: () => ({
    userId: 'demo-user',
    requestId: `req-${Date.now()}`,
  }),
});

// Demo function
async function runBasicExample() {
  console.log('🚀 Basic CQRS Example\n');

  try {
    // Create some users
    console.log('1. Creating users...');
    const user1 = await cqrs.commands.createUser({
      name: 'John Doe',
      email: '<EMAIL>',
    });

    console.log('✅ Users created successfully\n');

    // Get a specific user
    console.log('2. Getting user by ID...');
    const retrievedUser = await cqrs.queries.getUser({ id: user1.id });
    console.log('✅ User retrieved:', retrievedUser.name, '\n');

    // List all users
    console.log('3. Listing all users...');
    const allUsers = await cqrs.queries.listUsers();
    console.log('✅ All users:', allUsers.map(u => u.name).join(', '), '\n');

    // Demonstrate validation
    console.log('4. Testing validation...');
    try {
      await cqrs.commands.createUser({
        name: '',
        email: 'invalid-email',
      });
    } catch (error) {
      console.log('✅ Validation working:', (error as Error).message, '\n');
    }

    // Try to get non-existent user
    console.log('5. Testing error handling...');
    try {
      await cqrs.queries.getUser({ id: 'non-existent-id' });
    } catch (error) {
      console.log('✅ Error handling working:', (error as Error).message, '\n');
    }

    console.log('🎉 Basic example completed successfully!');

  } catch (error) {
    console.error('❌ Error in basic example:', error);
  }
}

// Run the example
runBasicExample().catch(console.error); 