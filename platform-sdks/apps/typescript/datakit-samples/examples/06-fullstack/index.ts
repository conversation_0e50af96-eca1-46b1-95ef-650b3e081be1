import { z } from 'zod';
import { CQRS } from '@matricsio/datakit';
import { createTRPCClient, httpBatchLink } from '@trpc/client';
import fetch from 'cross-fetch';

// Context type for the application
type AppContext = {
  userId?: string;
  requestId: string;
  userAgent?: string;
  authorization?: string;
  timestamp: Date;
};

// Create CQRS instance
const { define, registry } = CQRS.create<AppContext>();
const { command, query } = define;

// Data models
type User = {
  id: string;
  username: string;
  email: string;
  role: 'user' | 'admin';
  createdAt: Date;
  lastLoginAt?: Date;
};

type Post = {
  id: string;
  title: string;
  content: string;
  authorId: string;
  published: boolean;
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
};

// In-memory storage (in real app, this would be a database)
const users = new Map<string, User>();
const posts = new Map<string, Post>();
const sessions = new Map<string, { userId: string; expiresAt: Date }>();

// Initialize with some data
function initializeData() {
  // Create admin user
  const adminUser: User = {
    id: 'user-admin',
    username: 'admin',
    email: '<EMAIL>',
    role: 'admin',
    createdAt: new Date(),
  };
  users.set(adminUser.id, adminUser);
  sessions.set('admin-token', { userId: adminUser.id, expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) });

  // Create regular user
  const regularUser: User = {
    id: 'user-john',
    username: 'john',
    email: '<EMAIL>',
    role: 'user',
    createdAt: new Date(),
  };
  users.set(regularUser.id, regularUser);
  sessions.set('user-token', { userId: regularUser.id, expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) });

  // Create sample posts
  const post1: Post = {
    id: 'post-1',
    title: 'Getting Started with CQRS',
    content: 'CQRS (Command Query Responsibility Segregation) is a powerful pattern...',
    authorId: adminUser.id,
    published: true,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    tags: ['cqrs', 'architecture', 'tutorial'],
  };
  posts.set(post1.id, post1);

  const post2: Post = {
    id: 'post-2',
    title: 'TypeScript Best Practices',
    content: 'Here are some best practices for writing maintainable TypeScript code...',
    authorId: regularUser.id,
    published: false,
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    tags: ['typescript', 'best-practices'],
  };
  posts.set(post2.id, post2);
}

// Helper functions
function getUserFromContext(ctx: AppContext): User | null {
  if (!ctx.authorization) return null;

  const token = ctx.authorization.replace('Bearer ', '');
  const session = sessions.get(token);

  if (!session || session.expiresAt < new Date()) {
    return null;
  }

  return users.get(session.userId) || null;
}

function requireAuth(ctx: AppContext): User {
  const user = getUserFromContext(ctx);
  if (!user) {
    throw new Error('Authentication required');
  }
  return user;
}

function requireAdmin(ctx: AppContext): User {
  const user = requireAuth(ctx);
  if (user.role !== 'admin') {
    throw new Error('Admin privileges required');
  }
  return user;
}

// Define procedures

// Health check
const healthCheck = query({
  input: z.void(),
  output: z.object({
    status: z.string(),
    timestamp: z.number(),
    uptime: z.number(),
    version: z.string(),
  }),
  metadata: {
    title: 'Health check endpoint',
    rest: {
      method: 'GET',
      path: '/health',
    },
  },
  handler: async () => {
    return {
      status: 'healthy',
      timestamp: Date.now(),
      uptime: process.uptime(),
      version: '1.0.0',
    };
  },
});

// Authentication
const login = command({
  input: z.object({
    username: z.string(),
    password: z.string(),
  }),
  output: z.object({
    token: z.string(),
    user: z.object({
      id: z.string(),
      username: z.string(),
      email: z.string(),
      role: z.enum(['user', 'admin']),
    }),
  }),
  metadata: {
    rest: {
      method: 'POST',
      path: '/auth/login',
    },
    title: 'User login',
  },
  handler: async ({ input, ctx }) => {
    // Simple authentication (in real app, check password hash)
    const user = Array.from(users.values()).find(u => u.username === input.username);

    if (!user || input.password !== 'password') {
      throw new Error('Invalid credentials');
    }

    // Update last login
    user.lastLoginAt = new Date();
    users.set(user.id, user);

    // Create session
    const token = `${user.role}-token`;
    sessions.set(token, {
      userId: user.id,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
    });

    console.log(`🔐 [${ctx.requestId}] User logged in: ${user.username}`);

    return {
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
      },
    };
  },
});

// User management
const getUsers = query({
  input: z.object({
    limit: z.number().min(1).max(100).optional(),
    offset: z.number().min(0).optional(),
  }).optional(),
  output: z.object({
    users: z.array(z.object({
      id: z.string(),
      username: z.string(),
      email: z.string(),
      role: z.enum(['user', 'admin']),
      createdAt: z.string(),
      lastLoginAt: z.string().optional(),
    })),
    total: z.number(),
  }),
  metadata: {
    rest: {
      method: 'GET',
      path: '/users',
    },
    title: 'Get all users (admin only)',
  },
  handler: async ({ input, ctx }) => {
    requireAdmin(ctx);

    const allUsers = Array.from(users.values());
    const limit = input?.limit ?? 10;
    const offset = input?.offset ?? 0;

    const paginatedUsers = allUsers
      .slice(offset, offset + limit)
      .map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        createdAt: user.createdAt.toISOString(),
        lastLoginAt: user.lastLoginAt?.toISOString(),
      }));

    console.log(`👥 [${ctx.requestId}] Users fetched: ${paginatedUsers.length}/${allUsers.length}`);

    return {
      users: paginatedUsers,
      total: allUsers.length,
    };
  },
});

const getProfile = query({
  input: z.void(),
  output: z.object({
    id: z.string(),
    username: z.string(),
    email: z.string(),
    role: z.enum(['user', 'admin']),
    createdAt: z.string(),
    lastLoginAt: z.string().optional(),
    postCount: z.number(),
  }),
  metadata: {
    rest: {
      method: 'GET',
      path: '/profile',
    },
    title: 'Get current user profile',
  },
  handler: async ({ ctx }) => {
    const user = requireAuth(ctx);

    const userPosts = Array.from(posts.values()).filter(p => p.authorId === user.id);

    console.log(`👤 [${ctx.requestId}] Profile fetched: ${user.username}`);

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      createdAt: user.createdAt.toISOString(),
      lastLoginAt: user.lastLoginAt?.toISOString(),
      postCount: userPosts.length,
    };
  },
});

// Post management
const getPosts = query({
  input: z.object({
    published: z.boolean().optional(),
    authorId: z.string().optional(),
    tag: z.string().optional(),
    limit: z.number().min(1).max(50).optional(),
    offset: z.number().min(0).optional(),
  }).optional(),
  output: z.object({
    posts: z.array(z.object({
      id: z.string(),
      title: z.string(),
      content: z.string(),
      authorId: z.string(),
      authorUsername: z.string(),
      published: z.boolean(),
      createdAt: z.string(),
      updatedAt: z.string(),
      tags: z.array(z.string()),
    })),
    total: z.number(),
  }),
  metadata: {
    rest: {
      method: 'GET',
      path: '/posts',
    },
    title: 'Get posts with optional filters',
  },
  handler: async ({ input, ctx }) => {
    let filteredPosts = Array.from(posts.values());

    // Apply filters
    if (input?.published !== undefined) {
      filteredPosts = filteredPosts.filter(p => p.published === input.published);
    }

    if (input?.authorId) {
      filteredPosts = filteredPosts.filter(p => p.authorId === input.authorId);
    }

    if (input?.tag) {
      filteredPosts = filteredPosts.filter(p => p.tags.includes(input.tag!));
    }

    // Sort by creation date (newest first)
    filteredPosts.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    // Paginate
    const limit = input?.limit ?? 10;
    const offset = input?.offset ?? 0;
    const paginatedPosts = filteredPosts.slice(offset, offset + limit);

    // Add author information
    const postsWithAuthor = paginatedPosts.map(post => {
      const author = users.get(post.authorId);
      return {
        id: post.id,
        title: post.title,
        content: post.content,
        authorId: post.authorId,
        authorUsername: author?.username || 'Unknown',
        published: post.published,
        createdAt: post.createdAt.toISOString(),
        updatedAt: post.updatedAt.toISOString(),
        tags: post.tags,
      };
    });

    console.log(`📄 [${ctx.requestId}] Posts fetched: ${postsWithAuthor.length}/${filteredPosts.length}`);

    return {
      posts: postsWithAuthor,
      total: filteredPosts.length,
    };
  },
});

const getPost = query({
  input: z.object({
    id: z.string(),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    authorId: z.string(),
    authorUsername: z.string(),
    published: z.boolean(),
    createdAt: z.string(),
    updatedAt: z.string(),
    tags: z.array(z.string()),
  }),
  metadata: {
    rest: {
      method: 'GET',
      path: '/posts/getPost',
    },
  },
  handler: async ({ input, ctx }) => {
    const post = posts.get(input.id);
    if (!post) {
      throw new Error(`Post with ID ${input.id} not found`);
    }

    const author = users.get(post.authorId);

    console.log(`📄 [${ctx.requestId}] Post fetched: ${post.title}`);

    return {
      id: post.id,
      title: post.title,
      content: post.content,
      authorId: post.authorId,
      authorUsername: author?.username || 'Unknown',
      published: post.published,
      createdAt: post.createdAt.toISOString(),
      updatedAt: post.updatedAt.toISOString(),
      tags: post.tags,
    };
  },
});

const createPost = command({
  input: z.object({
    title: z.string().min(1, 'Title is required'),
    content: z.string().min(1, 'Content is required'),
    published: z.boolean().default(false),
    tags: z.array(z.string()).default([]),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    authorId: z.string(),
    published: z.boolean(),
    createdAt: z.string(),
    updatedAt: z.string(),
    tags: z.array(z.string()),
  }),
  metadata: {
    rest: {
      method: 'POST',
      path: '/posts/createPost',
    },
    title: 'Create a new post',
  },
  handler: async ({ input, ctx }) => {
    const user = requireAuth(ctx);

    const postId = `post-${Date.now()}`;
    const now = new Date();

    const post: Post = {
      id: postId,
      title: input.title,
      content: input.content,
      authorId: user.id,
      published: input.published,
      createdAt: now,
      updatedAt: now,
      tags: input.tags,
    };

    posts.set(postId, post);

    console.log(`✍️  [${ctx.requestId}] Post created: ${post.title} by ${user.username}`);

    return {
      id: post.id,
      title: post.title,
      content: post.content,
      authorId: post.authorId,
      published: post.published,
      createdAt: post.createdAt.toISOString(),
      updatedAt: post.updatedAt.toISOString(),
      tags: post.tags,
    };
  },
});

const updatePost = command({
  input: z.object({
    id: z.string(),
    title: z.string().optional(),
    content: z.string().optional(),
    published: z.boolean().optional(),
    tags: z.array(z.string()).optional(),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    authorId: z.string(),
    published: z.boolean(),
    createdAt: z.string(),
    updatedAt: z.string(),
    tags: z.array(z.string()),
  }),
  metadata: {
    rest: {
      method: 'PUT',
      path: '/posts/updatePost',
    },
    title: 'Update an existing post',
  },
  handler: async ({ input, ctx }) => {
    const user = requireAuth(ctx);

    const existingPost = posts.get(input.id);
    if (!existingPost) {
      throw new Error(`Post with ID ${input.id} not found`);
    }

    // Check ownership (or admin)
    if (existingPost.authorId !== user.id && user.role !== 'admin') {
      throw new Error('You can only update your own posts');
    }

    const updatedPost: Post = {
      ...existingPost,
      title: input.title ?? existingPost.title,
      content: input.content ?? existingPost.content,
      published: input.published ?? existingPost.published,
      tags: input.tags ?? existingPost.tags,
      updatedAt: new Date(),
    };

    posts.set(input.id, updatedPost);

    console.log(`📝 [${ctx.requestId}] Post updated: ${updatedPost.title} by ${user.username}`);

    return {
      id: updatedPost.id,
      title: updatedPost.title,
      content: updatedPost.content,
      authorId: updatedPost.authorId,
      published: updatedPost.published,
      createdAt: updatedPost.createdAt.toISOString(),
      updatedAt: updatedPost.updatedAt.toISOString(),
      tags: updatedPost.tags,
    };
  },
});

const deletePost = command({
  input: z.object({
    id: z.string(),
  }),
  output: z.object({
    id: z.string(),
    deleted: z.boolean(),
  }),
  metadata: {
    rest: {
      method: 'DELETE',
      path: '/posts/deletePost',
    },
    title: 'Delete a post',
  },
  handler: async ({ input, ctx }) => {
    const user = requireAuth(ctx);

    const existingPost = posts.get(input.id);
    if (!existingPost) {
      throw new Error(`Post with ID ${input.id} not found`);
    }

    // Check ownership (or admin)
    if (existingPost.authorId !== user.id && user.role !== 'admin') {
      throw new Error('You can only delete your own posts');
    }

    posts.delete(input.id);

    console.log(`🗑️  [${ctx.requestId}] Post deleted: ${existingPost.title} by ${user.username}`);

    return {
      id: input.id,
      deleted: true,
    };
  },
});

// Create registry
const appRegistry = registry({
  healthCheck,
  login,
  getUsers,
  getProfile,
  getPosts,
  getPost,
  createPost,
  updatePost,
  deletePost,
});

// Create CQRS instance
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: ({ rest }) => {
    const requestId = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    return {
      requestId,
      userId: rest?.headers?.['x-user-id'],
      userAgent: rest?.headers?.['user-agent'],
      authorization: rest?.headers?.authorization,
      timestamp: new Date(),
    };
  },
});

// Get router type for client
const trpcRouter = cqrs.getTRPCRouter();
type AppRouter = typeof trpcRouter;

// Server functions
async function startServer() {
  console.log('🚀 Starting full-stack server...');

  // Initialize data
  initializeData();

  await cqrs.run({
    http: {
      adapter: 'fastify',
      config: {
        port: 3003,
        protocols: {
          trpc: {
            enabled: true,
            prefix: '/trpc',
          },
          rest: {
            enabled: true,
            prefix: '/api',
            swagger: {
              enabled: true,
              prefix: '/docs',
              title: 'Full-Stack CQRS API',
              version: '1.0.0',
            },
          },
        },
        cors: {
          origin: ['*'],
          credentials: true,
        },
      },
    },
  });

  console.log('✅ Server started:');
  console.log('   📡 REST API: http://localhost:3003/api');
  console.log('   🔗 tRPC: http://localhost:3003/trpc');
  console.log('   📚 OpenAPI: http://localhost:3003/docs');
}

// Client demonstration
async function demonstrateRESTClient() {
  console.log('\n🌐 REST Client Example\n');

  try {
    const baseUrl = 'http://localhost:3003/api';

    // 1. Health check
    console.log('1. Health check...');
    const healthResponse = await fetch(`${baseUrl}/health`);
    const health = await healthResponse.json();
    console.log('✅ Health:', health.status);

    // 2. Login
    console.log('\n2. Login...');
    const loginResponse = await fetch(`${baseUrl}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'password' }),
    });
    const loginData = await loginResponse.json();
    console.log('✅ Logged in as:', loginData.user.username, 'token:', loginData.token);

    const authHeaders = {
      'Authorization': `Bearer ${loginData.token}`,
      'Content-Type': 'application/json',
    };

    // 3. Get profile
    console.log('\n3. Get profile...');
    const profileResponse = await fetch(`${baseUrl}/profile`, {
      headers: authHeaders,
    });
    const profile = await profileResponse.json();
    console.log('✅ Profile:', profile.username, `(${profile.postCount} posts)`);

    // 4. Get posts
    console.log('\n4. Get posts...');
    const postsResponse = await fetch(`${baseUrl}/posts?published=true`);
    const postsData = await postsResponse.json();
    console.log('✅ Published posts:', postsData.posts.length);

    // 5. Create post
    console.log('\n5. Create post...');
    const createResponse = await fetch(`${baseUrl}/posts`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify({
        title: 'REST API Example',
        content: 'This post was created via REST API!',
        published: true,
        tags: ['rest', 'api', 'example'],
      }),
    });
    const newPost = await createResponse.json();
    console.log('✅ Created post:', newPost.title);

    // 6. Get specific post
    console.log('\n6. Get specific post...');
    const postResponse = await fetch(`${baseUrl}/posts/${newPost.id}`);
    const postData = await postResponse.json();
    console.log('✅ Retrieved post:', postData.title);

  } catch (error) {
    console.error('❌ REST client error:', error);
  }
}

async function demonstrateTRPCClient() {
  console.log('\n🔗 tRPC Client Example\n');

  try {
    const client = createTRPCClient<AppRouter>({
      links: [
        httpBatchLink({
          url: 'http://localhost:3003/trpc',
          fetch,
        }),
      ],
    });

    // 1. Health check
    console.log('1. Health check...');
    const health = await client.healthCheck.query();
    console.log('✅ Health:', health.status, `(uptime: ${Math.round(health.uptime)}s)`);

    // 2. Login
    console.log('\n2. Login...');
    const loginResult = await client.login.mutate({
      username: 'john',
      password: 'password',
    });
    console.log('✅ Logged in as:', loginResult.user.username, 'token:', loginResult.token);

    // 3. Create client with auth
    const authClient = createTRPCClient<AppRouter>({
      links: [
        httpBatchLink({
          url: 'http://localhost:3003/trpc',
          headers: {
            authorization: `Bearer ${loginResult.token}`,
          },
          fetch,
        }),
      ],
    });

    // 4. Get profile
    console.log('\n3. Get profile...');
    const profile = await authClient.getProfile.query();
    console.log('✅ Profile:', profile.username, `(${profile.postCount} posts)`);

    // 5. Get posts
    console.log('\n4. Get posts...');
    const posts = await authClient.getPosts.query({
      published: false,
    });
    console.log('✅ Draft posts:', posts.posts.length);

    // 6. Create post
    console.log('\n5. Create post...');
    const newPost = await authClient.createPost.mutate({
      title: 'tRPC Example',
      content: 'This post was created via tRPC!',
      published: true,
      tags: ['trpc', 'typescript', 'example'],
    });
    console.log('✅ Created post:', newPost.title);

    // 7. Update post
    console.log('\n6. Update post...');
    const updatedPost = await authClient.updatePost.mutate({
      id: newPost.id,
      content: 'This post was created and updated via tRPC!',
    });
    console.log('✅ Updated post:', updatedPost.title);

    // 8. Get updated post
    console.log('\n7. Get updated post...');
    const retrievedPost = await authClient.getPost.query({ id: newPost.id });
    console.log('✅ Retrieved post content length:', retrievedPost.content.length);

  } catch (error) {
    console.error('❌ tRPC client error:', error);
  }
}

async function runFullStackExample() {
  console.log('🏗️  Full-Stack Example\n');

  try {
    // Start the server
    await startServer();

    // Wait for server to be ready
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Demonstrate REST client
    await demonstrateRESTClient();

    // Demonstrate tRPC client
    await demonstrateTRPCClient();

    console.log('\n🎉 Full-stack example completed successfully!');
    console.log('\n💡 Try these URLs in your browser:');
    console.log('   📚 OpenAPI Docs: http://localhost:3003/docs');
    console.log('   💚 Health Check: http://localhost:3003/health');
    console.log('   📄 Public Posts: http://localhost:3003/api/posts?published=true');

    console.log('\n🛑 Press Ctrl+C to stop the server');

    // Keep server running
    await new Promise(() => {}); // This will keep the server running indefinitely

  } catch (error) {
    console.error('❌ Error in full-stack example:', error);
    try {
      await cqrs.stop();
    } catch (stopError) {
      console.error('❌ Error stopping server:', stopError);
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  try {
    await cqrs.stop();
    console.log('✅ Server stopped');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error stopping server:', error);
    process.exit(1);
  }
});

runFullStackExample().catch(console.error);
