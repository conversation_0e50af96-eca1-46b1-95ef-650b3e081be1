import { z } from 'zod';
import { CQRS } from '@matricsio/datakit';
import { createTRPCClient, httpBatchLink } from '@trpc/client';
import fetch from 'cross-fetch';

// Context type
type AppContext = {
  userId?: string;
  requestId: string;
};

// Create CQRS instance
const { define, registry } = CQRS.create<AppContext>();
const { command, query } = define;

// Mock data storage
const tasks = new Map<string, { id: string; title: string; completed: boolean; userId: string; createdAt: Date }>();
let taskIdCounter = 1;

// Define procedures
const getTasks = query({
  input: z.object({
    userId: z.string().optional(),
    completed: z.boolean().optional(),
  }).optional(),
  output: z.array(z.object({
    id: z.string(),
    title: z.string(),
    completed: z.boolean(),
    userId: z.string(),
    createdAt: z.string(),
  })),
  handler: async ({ input }) => {
    let filteredTasks = Array.from(tasks.values());

    if (input?.userId) {
      filteredTasks = filteredTasks.filter(task => task.userId === input.userId);
    }

    if (input?.completed !== undefined) {
      filteredTasks = filteredTasks.filter(task => task.completed === input.completed);
    }

    return filteredTasks.map(task => ({
      ...task,
      createdAt: task.createdAt.toISOString(),
    }));
  },
});

const getTask = query({
  input: z.object({
    id: z.string(),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    completed: z.boolean(),
    userId: z.string(),
    createdAt: z.string(),
  }),
  handler: async ({ input }) => {
    const task = tasks.get(input.id);
    if (!task) {
      throw new Error(`Task with ID ${input} not found`);
    }

    return {
      ...task,
      createdAt: task.createdAt.toISOString(),
    };
  },
});

const createTask = command({
  input: z.object({
    title: z.string().min(1, 'Title is required'),
    userId: z.string().min(1, 'User ID is required'),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    completed: z.boolean(),
    userId: z.string(),
    createdAt: z.string(),
  }),
  handler: async ({ input, ctx }) => {
    const taskId = `task-${taskIdCounter++}`;
    const task = {
      id: taskId,
      title: input.title,
      completed: false,
      userId: input.userId,
      createdAt: new Date(),
    };

    tasks.set(taskId, task);

    console.log(`✅ [${ctx.requestId}] Task created: ${task.title}`);

    return {
      ...task,
      createdAt: task.createdAt.toISOString(),
    };
  },
});

const updateTask = command({
  input: z.object({
    id: z.string(),
    title: z.string().optional(),
    completed: z.boolean().optional(),
  }),
  output: z.object({
    id: z.string(),
    title: z.string(),
    completed: z.boolean(),
    userId: z.string(),
    createdAt: z.string(),
    updatedAt: z.string(),
  }),
  handler: async ({ input, ctx }) => {
    const existingTask = tasks.get(input.id);
    if (!existingTask) {
      throw new Error(`Task with ID ${input.id} not found`);
    }

    const updatedTask = {
      ...existingTask,
      title: input.title ?? existingTask.title,
      completed: input.completed ?? existingTask.completed,
    };

    tasks.set(input.id, updatedTask);

    console.log(`📝 [${ctx.requestId}] Task updated: ${updatedTask.title}`);

    return {
      ...updatedTask,
      createdAt: updatedTask.createdAt.toISOString(),
      updatedAt: new Date().toISOString(),
    };
  },
});

const deleteTask = command({
  input: z.object({
    id: z.string(),
  }),
  output: z.object({
    success: z.boolean(),
    deletedId: z.string(),
  }),
  handler: async ({ input, ctx }) => {
    const task = tasks.get(input.id);
    if (!task) {
      throw new Error(`Task with ID ${input.id} not found`);
    }

    tasks.delete(input.id);

    console.log(`🗑️  [${ctx.requestId}] Task deleted: ${task.title}`);

    return {
      success: true,
      deletedId: input.id,
    };
  },
});

// Create registry
const appRegistry = registry({
  getTasks,
  getTask,
  createTask,
  updateTask,
  deleteTask,
});

// Create CQRS instance
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: () => ({
    requestId: `req-${Date.now()}`,
    userId: 'demo-user',
  }),
});

// Get the tRPC router type for client
const trpcRouter = cqrs.getTRPCRouter();
type AppRouter = typeof trpcRouter;

// Server setup function
async function startServer() {
  console.log('🚀 Starting tRPC server...');

  await cqrs.run({
    http: {
      adapter: 'fastify',
      config: {
        port: 3002,
        protocols: {
          trpc: {
            enabled: true,
            prefix: '/trpc',
          },
          rest: {
            enabled: false, // Disable REST for this example
          },
        },
        cors: {
          origin: ['*'],
          credentials: true,
        },
      },
    },
  });

  console.log('✅ tRPC server started on http://localhost:3002/trpc');
}

// Client demonstration
async function demonstrateClient() {
  console.log('📱 Setting up tRPC client...');

  // Create tRPC client
  const client = createTRPCClient<AppRouter>({
    links: [
      httpBatchLink({
        url: 'http://localhost:3002/trpc',
        fetch,
      }),
    ],
  });

  console.log('✅ tRPC client created\n');

  try {
    // Create some tasks
    console.log('1. Creating tasks...');
    const task1 = await client.createTask.mutate({
      title: 'Learn tRPC',
      userId: 'user-1',
    });
    console.log('✅ Created task:', task1.title);

    const task2 = await client.createTask.mutate({
      title: 'Build awesome app',
      userId: 'user-1',
    });
    console.log('✅ Created task:', task2.title);

    const task3 = await client.createTask.mutate({
      title: 'Write documentation',
      userId: 'user-2',
    });
    console.log('✅ Created task:', task3.title, '\n');

    // Get all tasks
    console.log('2. Fetching all tasks...');
    const allTasks = await client.getTasks.query();
    console.log('✅ All tasks:', allTasks.map(t => t.title).join(', '), '\n');

    // Get tasks for specific user
    console.log('3. Fetching tasks for user-1...');
    const user1Tasks = await client.getTasks.query({ userId: 'user-1' });
    console.log('✅ User 1 tasks:', user1Tasks.map(t => t.title).join(', '), '\n');

    // Get a specific task
    console.log('4. Fetching specific task...');
    const specificTask = await client.getTask.query({ id: task1.id });
    console.log('✅ Specific task:', specificTask.title, '\n');

    // Update a task
    console.log('5. Updating task...');
    const updatedTask = await client.updateTask.mutate({
      id: task1.id,
      completed: true,
    });
    console.log('✅ Updated task:', updatedTask.title, 'completed:', updatedTask.completed, '\n');

    // Get completed tasks
    console.log('6. Fetching completed tasks...');
    const completedTasks = await client.getTasks.query({ completed: true });
    console.log('✅ Completed tasks:', completedTasks.map(t => t.title).join(', '), '\n');

    // Delete a task
    console.log('7. Deleting task...');
    const deleteResult = await client.deleteTask.mutate({ id: task3.id });
    console.log('✅ Deleted task:', deleteResult.success, '\n');

    // Final task list
    console.log('8. Final task list...');
    const finalTasks = await client.getTasks.query();
    console.log('✅ Final tasks:', finalTasks.map(t => `${t.title} (${t.completed ? 'done' : 'pending'})`).join(', '), '\n');

    console.log('🎉 tRPC client example completed successfully!');

  } catch (error) {
    console.error('❌ Client error:', error);
  }
}

// Type safety demonstration
function demonstrateTypeSafety() {
  console.log('\n🛡️  Type Safety Demonstration:');
  console.log('The following would cause TypeScript errors:');
  console.log('');
  console.log('// ❌ Wrong input type');
  console.log('// client.createTask.mutate({ wrongField: "value" });');
  console.log('');
  console.log('// ❌ Wrong method (query vs mutate)');
  console.log('// client.createTask.query({ title: "Task", userId: "user-1" });');
  console.log('');
  console.log('// ❌ Missing required fields');
  console.log('// client.createTask.mutate({ title: "Task" }); // missing userId');
  console.log('');
  console.log('// ✅ Correct usage with full type inference');
  console.log('// const task = await client.createTask.mutate({');
  console.log('//   title: "Learn TypeScript", // string (required)');
  console.log('//   userId: "user-1"          // string (required)');
  console.log('// });');
  console.log('// task.id        // string (inferred)');
  console.log('// task.completed // boolean (inferred)');
  console.log('// task.createdAt // string (inferred)');
}

async function runTRPCExample() {
  console.log('🔗 tRPC Client Example\n');

  try {
    // Start the server
    await startServer();

    // Wait a bit for server to be ready
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Demonstrate client usage
    await demonstrateClient();

    // Show type safety features
    demonstrateTypeSafety();

    console.log('\n🛑 Stopping server...');
    await cqrs.stop();
    console.log('✅ Server stopped');

  } catch (error) {
    console.error('❌ Error in tRPC example:', error);
    try {
      await cqrs.stop();
    } catch (stopError) {
      console.error('❌ Error stopping server:', stopError);
    }
  }
}

// Run the example
runTRPCExample().catch(console.error);
