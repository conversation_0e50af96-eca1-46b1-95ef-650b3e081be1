import { z } from 'zod';
import { CQRS, MiddlewareOptions } from '@matricsio/datakit';

// Base context
type BaseContext = {
  requestId: string;
  userAgent?: string;
};

// Enhanced context after authentication
type AuthenticatedContext = BaseContext & {
  userId: string;
  user: {
    id: string;
    name: string;
    role: 'admin' | 'user';
  };
};

// Logging middleware
const loggingMiddleware = async (opts: MiddlewareOptions) => {
    const start = Date.now();
    console.log(`📝 [${opts.ctx.requestId}] Starting ${opts.type.toUpperCase()}: ${opts.path}`);
    console.log(`   Input:`, JSON.stringify(opts.input));

    try {
        const result = await opts.next();
        const duration = Date.now() - start;
        console.log(`✅ [${opts.ctx.requestId}] Completed ${opts.path} in ${duration}ms`);
        return result;
    } catch (error) {
        const duration = Date.now() - start;
        console.log(`❌ [${opts.ctx.requestId}] Failed ${opts.path} in ${duration}ms:`, (error as Error).message);
        throw error;
    }
};

// Create CQRS instance
const { define, registry } = CQRS.create<BaseContext>();
const { command: rawCommand, query: rawQuery } = define;

// Apply logging middleware to all commands/queries
const command = rawCommand.use(loggingMiddleware);
const query = rawQuery.use(loggingMiddleware);

// Mock user database
const users = new Map([
  ['user-1', { id: 'user-1', name: 'John Doe', role: 'user' as const, token: 'user-token' }],
  ['admin-1', { id: 'admin-1', name: 'Admin User', role: 'admin' as const, token: 'admin-token' }],
]);

// Authentication middleware
const authMiddleware = async (opts: MiddlewareOptions) => {
  const { ctx, input } = opts;

  // Extract token from input or context
  const token = input?.token || ctx.authToken;

  if (!token) {
    throw new Error('UNAUTHORIZED: No token provided');
  }

  // Find user by token
  const user = Array.from(users.values()).find(u => u.token === token);
  if (!user) {
    throw new Error('UNAUTHORIZED: Invalid token');
  }

  console.log(`🔐 [${ctx.requestId}] Authenticated as ${user.name} (${user.role})`);

  // Extend context with user information
  return opts.next({
    ctx: {
      ...ctx,
      userId: user.id,
      user: {
        id: user.id,
        name: user.name,
        role: user.role,
      },
    },
  });
};

// Admin-only middleware
const adminMiddleware = async (opts: MiddlewareOptions) => {
  const { ctx } = opts;

  if (!ctx.user || ctx.user.role !== 'admin') {
    throw new Error('FORBIDDEN: Admin access required');
  }

  console.log(`👑 [${ctx.requestId}] Admin access granted to ${ctx.user.name}`);
  return opts.next();
};

// Rate limiting middleware (simplified)
const rateLimitMiddleware = async (opts: MiddlewareOptions) => {
  const { ctx } = opts;

  // In a real app, you'd use Redis or similar
  const rateLimitKey = `rate_limit_${ctx.userId || 'anonymous'}`;

  console.log(`⏱️  [${ctx.requestId}] Rate limit check for ${rateLimitKey}`);

  // Simulate rate limiting (always pass in this example)
  return opts.next();
};

// Public query (no auth required)
const getPublicInfo = query({
  input: z.void(),
  output: z.object({
    message: z.string(),
    timestamp: z.number(),
  }),
  handler: async () => {
    return {
      message: 'This is public information',
      timestamp: Date.now(),
    };
  },
});

// Protected command (requires authentication)
const createPost = command
  .use(authMiddleware)
  .use(rateLimitMiddleware)({
    input: z.object({
      title: z.string().min(1),
      content: z.string().min(1),
      token: z.string(),
    }),
    output: z.object({
      id: z.string(),
      title: z.string(),
      authorId: z.string(),
      authorName: z.string(),
    }),
    handler: async ({ input, ctx }) => {
      const authCtx = ctx as AuthenticatedContext;

      const post = {
        id: `post-${Date.now()}`,
        title: input.title,
        authorId: authCtx.user.id,
        authorName: authCtx.user.name,
      };

      console.log(`📝 [${authCtx.requestId}] Post created by ${authCtx.user.name}`);
      return post;
    },
  });

// Admin-only command
const deleteUser = command
  .use(authMiddleware)
  .use(adminMiddleware)({
    input: z.object({
      userId: z.string(),
      token: z.string(),
    }),
    output: z.object({
      success: z.boolean(),
      deletedUserId: z.string(),
    }),
    handler: async ({ input, ctx }) => {
      const authCtx = ctx as AuthenticatedContext;

      console.log(`🗑️  [${authCtx.requestId}] Admin ${authCtx.user.name} deleting user ${input.userId}`);

      return {
        success: true,
        deletedUserId: input.userId,
      };
    },
  });

// User profile query (requires authentication)
const getProfile = query
  .use(authMiddleware)({
    input: z.object({
      token: z.string(),
    }),
    output: z.object({
      id: z.string(),
      name: z.string(),
      role: z.string(),
    }),
    handler: async ({ ctx }) => {
      const authCtx = ctx as AuthenticatedContext;

      return {
        id: authCtx.user.id,
        name: authCtx.user.name,
        role: authCtx.user.role,
      };
    },
  });

// Create registry
const appRegistry = registry({
  getPublicInfo,
  createPost,
  deleteUser,
  getProfile,
});

// Create CQRS instance
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: () => ({
    requestId: `req-${Date.now()}`,
    userAgent: 'Mozilla/5.0 (Example Browser)',
  }),
});

async function runMiddlewareExample() {
  console.log('🔧 Middleware Example\n');

  try {
    // 1. Public access (no auth required)
    console.log('1. Testing public access...');
    const publicInfo = await cqrs.queries.getPublicInfo();
    console.log('✅ Public info:', publicInfo.message, '\n');

    // 2. Authenticated user operations
    console.log('2. Testing authenticated user...');
    const userPost = await cqrs.commands.createPost({
      title: 'My First Post',
      content: 'Hello world!',
      token: 'user-token',
    });
    console.log('✅ Post created:', userPost.title, '\n');

    const userProfile = await cqrs.queries.getProfile({
      token: 'user-token',
    });
    console.log('✅ User profile:', userProfile.name, userProfile.role, '\n');

    // 3. Admin operations
    console.log('3. Testing admin access...');
    const deleteResult = await cqrs.commands.deleteUser({
      userId: 'user-1',
      token: 'admin-token',
    });
    console.log('✅ Admin delete:', deleteResult.success, '\n');

    // 4. Test unauthorized access
    console.log('4. Testing unauthorized access...');
    try {
      await cqrs.commands.createPost({
        title: 'Unauthorized Post',
        content: 'This should fail',
        token: 'invalid-token',
      });
    } catch (error) {
      console.log('✅ Auth failed as expected:', (error as Error).message, '\n');
    }

    // 5. Test forbidden access (user trying admin operation)
    console.log('5. Testing forbidden access...');
    try {
      await cqrs.commands.deleteUser({
        userId: 'user-1',
        token: 'user-token', // Regular user token
      });
    } catch (error) {
      console.log('✅ Forbidden access blocked:', (error as Error).message, '\n');
    }

    console.log('🎉 Middleware example completed successfully!');

  } catch (error) {
    console.error('❌ Error in middleware example:', error);
  }
}

// Run the example
runMiddlewareExample().catch(console.error); 