{"name": "@matricsio/datakit-samples", "version": "0.1.0", "description": "Sample applications demonstrating CQRS SDK functionality", "private": true, "type": "module", "scripts": {"preinstall": "npx google-artifactregistry-auth", "lint": "nx lint datakit-samples", "serve:basic": "nx serve:basic datakit-samples", "serve:middleware": "nx serve:middleware datakit-samples", "serve:rest": "nx serve:rest datakit-samples", "serve:trpc": "nx serve:trpc datakit-samples", "serve:testing": "nx serve:testing datakit-samples", "serve:pubsub": "nx serve:pubsub datakit-samples", "serve:mcp": "nx serve:mcp datakit-samples", "serve": "nx serve datakit-samples"}, "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.2", "@matricsio/datakit": "workspace:*", "@trpc/client": "^11.3.1", "@trpc/server": "^11.3.1", "cross-fetch": "^4.1.0", "fastify": "^5.3.3", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.24", "jest": "^29.5.0", "nodemon": "^3.1.10", "tsx": "^4.7.1", "typescript": "^5.3.3"}}