{"name": "documentation", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/documentation/src", "projectType": "application", "tags": [], "targets": {"docusaurus": {"executor": "nx:run-commands", "options": {"commands": [{"command": "docusaurus apps/documentation"}]}}, "start": {"executor": "nx:run-commands", "options": {"command": "docusaurus start apps/documentation"}}, "build": {"executor": "nx:run-commands", "inputs": [], "outputs": ["{workspaceRoot}/dist/apps/documentation"], "options": {"command": "docusaurus build apps/documentation --out-dir=../../dist/apps/documentation"}}, "swizzle": {"executor": "nx:run-commands", "options": {"command": "docusaurus swizzle", "cwd": "apps/documentation"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "docusaurus deploy apps/documentation"}}, "clear": {"executor": "nx:run-commands", "options": {"command": "docusaurus clear apps/documentation"}}, "serve": {"executor": "nx:run-commands", "options": {"command": "docusaurus serve apps/documentation --dir=../../dist/apps/documentation"}}, "write-translations": {"executor": "nx:run-commands", "options": {"command": "docusaurus write-translations apps/documentation"}}, "write-heading-ids": {"executor": "nx:run-commands", "options": {"command": "docusaurus write-heading-ids apps/documentation"}}, "typecheck": {"executor": "nx:run-commands", "options": {"command": "tsc", "cwd": "apps/documentation"}}}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "lint": {"executor": "@nx/eslint:lint"}, "engines": {"node": ">=18.0"}}