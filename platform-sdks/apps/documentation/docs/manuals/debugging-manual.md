# Go Debugging Manual

## Overview

This manual explains how to debug Go services in the Healthcare Platform using VS Code/Cursor.

## VS Code Debug Configuration

The project includes pre-configured debugging settings for Go services. The debug configuration is located in `.vscode/launch.json` and provides two debugging modes.

- **Debug Current Go Package**: Debugs the Go package of the currently open file
- **Debug with Input Prompt**: Allows you to specify which Go package to debug

## Quick Start

### 1. Basic Setup

1. Open any `.go` file in the service you want to debug
2. Set breakpoints by clicking on line numbers (red dots will appear)
3. Press `F5` and select "Debug Current Go Package"
4. The debugger will start and pause at your breakpoints

## Adding New Packages to Debugging

When you create new Go packages or services that need debugging, you must add them to the Go workspace using `go work use ./path/to/package` to enable debugging.

```bash
# Basic syntax
go work use ./path/to/your/new/package

# Examples:
go work use ./apps/my-new-service
go work use ./apps/debug-simple
```

### Troubleshooting

**Error: "current directory is contained in a module that is not one of the workspace modules listed in go.work" during debugging**

- Solution: Make sure you've added the package with `go work use`

**Error: "no Go files in directory"**

- Solution: Ensure the directory contains `.go` files and a `go.mod`

## Setting Breakpoints

### 1. Basic Breakpoint Setup

- **Click** on the line number gutter (left margin) where you want to pause execution
- A **red dot** will appear indicating an active breakpoint
- **Right-click** on a breakpoint for additional options (conditional, log points, etc.)

### 2. Breakpoint Types

- **Standard Breakpoint**: Pauses execution at the specified line
- **Conditional Breakpoint**: Pauses only when a condition is met (e.g., `user.ID == 5`)
- **Log Point**: Logs a message without stopping execution

## Starting Debug Sessions

### Method 1: Debug Current Package

1. Open any `.go` file in the package you want to debug
2. Press `F5` or go to **Run and Debug** panel
3. Select **"Debug Current Go Package"**
4. The debugger will start and stop at your breakpoints

### Method 2: Debug Specific Package

1. Press `F5` and select **"Debug with Input Prompt"**
2. Enter the path to the Go package (e.g., `./apps/debug-simple`)
3. The debugger will launch the specified package

## Debug Controls

| Action | Shortcut | Description |
|--------|----------|-------------|
| Continue | `F5` | Resume execution until next breakpoint |
| Step Over | `F10` | Execute current line, don't enter functions |
| Step Into | `F11` | Enter function calls for detailed debugging |
| Step Out | `Shift+F11` | Exit current function |
| Stop | `Shift+F5` | Terminate debug session |

## Inspecting Variables

### Debug Console

- **Variables Panel**: View all local variables and their values
- **Watch Panel**: Add expressions to monitor (e.g., `user.Name`, `len(users)`)
- **Call Stack**: See the function call hierarchy
- **Debug Console**: Execute Go expressions during debugging

### Hover Inspection

- **Hover** over any variable to see its current value
- **Expand** complex objects (structs, slices, maps) to inspect nested data

## Tips for Effective Debugging

1. **Start Small**: Begin with breakpoints in main functions, then drill down
2. **Use Conditional Breakpoints**: For loops or frequent calls, use conditions like `i == 5`
3. **Watch Key Variables**: Add important variables to the Watch panel
4. **Check Error Values**: Always inspect `err` variables when debugging failures
5. **Use Debug Console**: Test expressions and function calls during debugging

## Troubleshooting

### Breakpoints Not Hit

- Check if the file is part of the current debug session
- Verify the Go module is included in `go.work`

### Debug Session Won't Start

- Check that the Go package has a `main` function
- Verify the package path is correct
- Ensure all dependencies are installed (`go mod tidy`)

### Performance Issues

- Limit the number of active breakpoints
- Use conditional breakpoints instead of stopping on every iteration
- Close debug sessions when not needed
