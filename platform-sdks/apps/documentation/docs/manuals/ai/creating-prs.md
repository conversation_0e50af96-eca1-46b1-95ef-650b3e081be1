---
title: Creating PRs
sidebar_position: 2
---

# Creating Pull Requests in Cursor

To quickly open a pull request using automation in Cursor, follow these steps:

1. **Set Up the Notepad (Optional)**
   - Open `.cursor/notepads/create-pr.md` in your project.
   - Copy its entire content.
   - In Cursor, go to the [Notepads section](https://docs.cursor.com/beta/notepads) in the sidebar and create a new notepad. Paste the copied content and save it as `Create Pull Request`.

2. **Open a PR in Chat**
   - In any Cursor chat, type:

     ```sh
     @Create Pull Request
     ```

     - Optionally, you can provide a diff context if you want to specify the changes.

3. **Sit Back and Relax**
   - The automation will:
     - Analyze your changes
     - Generate a PR title and description following best practices
     - Open the pull request for you using the GitHub MCP tool

That's it! Your pull request will be created with a clear, standardized description.

*Note: Cursor might require you to approve steps it needs to take to execute this flow.*
