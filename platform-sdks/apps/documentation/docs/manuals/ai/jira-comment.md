---
title: Leaving Jira Comments
sidebar_position: 3
---

# Leaving Jira Progress Comments in Cursor

To quickly leave a progress update as a comment on a Jira issue using automation in Cursor, follow these steps:

1. **Set Up the Notepad (Optional)**
   - Open `.cursor/notepads/jira-comment.md` in your project.
   - Copy its entire content.
   - In Cursor, go to the [Notepads section](https://docs.cursor.com/beta/notepads) in the sidebar and create a new notepad. Paste the copied content and save it as `Jira Comment`.

2. **Leave a Jira Comment in Chat**
   - In any Cursor chat, type:

     ```sh
     @Leave Jira Comment
     ```

     - When prompted, provide:
       - The Jira issue key (e.g., HAR-456)
       - A quick brain dump of what you've done or are doing for that issue

3. **Automation Handles the Rest**
   - The automation will post a comprehensive & structured comment to the Jira issue.

That's it! Your Jira issue will be updated with a clear, standardized progress comment.

*Note: Cursor might require you to approve steps it needs to take to execute this flow.*
