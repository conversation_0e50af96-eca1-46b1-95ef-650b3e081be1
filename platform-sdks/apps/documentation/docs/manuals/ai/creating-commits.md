---
title: Creating Commits
sidebar_position: 1
---

# Creating Conventional Commits in Cursor

To quickly generate Conventional Commits using automation in Cursor, follow these steps:

1. **Set Up the Notepad (Optional)**
   - Open `.cursor/notepads/create-commit.md` in your project.
   - Copy its entire content.
   - In Cursor, go to the [Notepads section](https://docs.cursor.com/beta/notepads) in the sidebar and create a new notepad. Paste the copied content and save it as `Create Commit`.

2. **Generate a Commit in Chat**
   - In any Cursor chat, type:

     ```sh
     @Create Commit
     ```

3. **Sit Back and Relax**
   - The automation will:
     - Analyze your changes
     - Generate a Conventional Commit message
     - Execute the git commit command for you

That's it! Your commit will be created following best practices and Conventional Commits format.

*Note: Cursor might require you to approve steps it needs to take to execute this flow.*
