---
title: Start Jira work branch
sidebar_position: 0
---

# Starting Work on a Jira Issue in Cursor

To quickly start work on a Jira issue and automate branch creation and "In Progress" status update, follow these steps:

1. **Set Up the Notepad (Optional)**
   - Open `.cursor/notepads/start-jira-issue.md` in your project.
   - Copy its entire content.
   - In Cursor, go to the [Notepads section](https://docs.cursor.com/beta/notepads) in the sidebar and create a new notepad. Paste the copied content and save it as `Start Jira Issue`.

2. **Start Work in Chat**
   - In any Cursor chat, type:

     ```sh
     @Start Jira Issue har-333
     ```

     Replace `har-333` with your Jira issue key (any case, e.g., HAR-333 or har-333).

3. **Sit Back and Relax**
   - The automation will:
     - Fetch the Jira issue details
     - Squash changes if any
     - Create a new branch from develop with the correct naming convention
     - Pop squashed changes
     - Move the Jira issue to "In Progress"

That's it! You're ready to start coding on your new branch.

*Note: Cursor might require you to approve steps it needs to take to execute this flow.*
