# Data Kit Sample Apps

This directory contains sample applications demonstrating how to use the Data Kit SDK in real-world scenarios, with a focus on bitemporal data management.

---

## Overview

The Data Kit sample apps show how to implement bitemporal CRUD APIs in Go using the [`bitemporality`](../../../sdks/datakit/bitemporality.md) SDK. They demonstrate:
- How to manage bitemporal entities (`Patient`, `Provider`) and non-bitemporal entities (`Appointment`)
- How to track both system and business (valid) time for your entities
- How to use different bitemporal storage strategies (`SameTableStrategy` vs. `SeparateTableStrategy`)
- How to use generic repositories for cleaner data access
- How to schedule future-dated changes (e.g., deletions)
- How to use both raw SQL (pgx) and GORM as persistence backends

---

## Architecture

```plantuml
@startuml
skinparam componentStyle rectangle
skinparam monochrome true

component "HTTP Client\n(curl, Postman)" as client
component "Sample App\n(bitemporality or\nbitemporality_gorm)" as app
database "Postgres DB" as db

client <--> app : REST API
app --> db : Generic Repositories\n(pgx or GORM)
@enduml
```

- **API Layer:** Gin HTTP server exposes CRUD endpoints for `Patient`, `Provider`, and `Appointment` entities.
- **Repository Layer:** Implements generic repositories for bitemporal and standard entities using either pgx or GORM.
- **Bitemporal Manager:** Handles all bitemporal logic (versioning, archiving, end dates).
- **Database:** Stores current and historical versions of entities, either in the same or separate tables.

---

## Features Demonstrated
- Bitemporal CRUD for multiple entities (`Patient`, `Provider`)
- Standard CRUD for non-bitemporal entities (`Appointment`)
- Flexible bitemporal storage strategies
- Generic repositories for `pgx` and `GORM`
- Future-dated deletions with custom valid end dates
- Versioning and historical archiving
- RESTful API with Gin
- Docker Compose for local development
- SQL migrations with golang-migrate

---

## Sample App Details

### 1. `bitemporality_gorm` (GORM/Postgres)
- **Location:** `apps/go/datakit-samples/bitemporality_gorm`
- **Backend:** GORM ORM with Postgres
- **Storage Strategy:** `SameTableStrategy` (Default) - Historical records are stored in the same table as current records.
- **Highlights:** ORM-based, less boilerplate, generic repositories for bitemporal and standard entities.

### 2. `bitemporality` (pgx/Postgres)
- **Location:** `apps/go/datakit-samples/bitemporality`
- **Backend:** pgx (raw SQL) with Postgres
- **Storage Strategy:** `SeparateTableStrategy` - Historical records are moved to dedicated `_historical` tables.
- **Highlights:** Direct SQL, explicit transaction control, generic repositories.

---

## How to Run

1. **Start Postgres and run migrations:**
   ```sh
   # For the GORM sample
   cd apps/go/datakit-samples/bitemporality_gorm
   docker-compose up -d

   # For the PGX sample
   cd ../bitemporality
   docker-compose up -d
   ```
2. **Set the `DATABASE_URL` environment variable** (if running locally):
   ```sh
   export DATABASE_URL=postgres://postgres:postgres@localhost:5432/bitemporality?sslmode=disable
   go run .
   ```
3. **Test the API:**
   Use the following curl requests.

---

## Example API Requests

### Patients

#### Create a Patient
```sh
curl -X POST http://localhost:8080/api/patients/ \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "gender": "male",
    "birthDate": "1980-01-01",
    "phoneNumber": "555-1234",
    "email": "<EMAIL>",
    "address": "123 Main St",
    "active": true
  }'
```

_Copy the `id` from the response to use in other requests._

### Providers

#### Create a Provider
```sh
curl -X POST http://localhost:8080/api/providers/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Dr. Emily Carter",
    "specialty": "Cardiology"
  }'
```
### List All Patients
```sh
curl http://localhost:8080/api/patients/
```

### Get a Patient by ID
```sh
curl http://localhost:8080/api/patients/<id>
```

### Update a Patient
```sh
curl -X PUT http://localhost:8080/api/patients/<id> \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "Jane",
    "lastName": "Doe",
    "gender": "female",
    "birthDate": "1985-05-05",
    "phoneNumber": "555-5678",
    "email": "<EMAIL>",
    "address": "456 Elm St",
    "active": false
  }'
```

### Delete a Patient (Immediate)
```sh
curl -X DELETE http://localhost:8080/api/patients/<id>
```

### Delete a Patient (Future-dated)
```sh
curl -X DELETE "http://localhost:8080/api/patients/<id>?validEndDate=2024-12-31T23:59:59Z"
```

### Appointments

#### Create an Appointment
Replace `<PATIENT_ID>` with a valid patient ID.
```sh
curl -X POST http://localhost:8080/api/appointments \
-H "Content-Type: application/json" \
-d '{
    "patientId": "<PATIENT_ID>",
    "appointmentDate": "2024-09-15T14:00:00Z",
    "description": "Annual Check-up"
}'
```
---

## Bitemporality Explained

- **System Time:** When the record was stored/updated in the database (audit trail).
- **Valid Time:** When the record is considered valid in the business domain.
- **Storage Strategy:** You can choose to store historical records in the same table as current data (`SameTableStrategy`) or in separate tables (`SeparateTableStrategy`).
- **Versioning:** Every update creates a new version; old versions are archived according to the chosen storage strategy.
- **Delete:** Can be immediate or future-dated, marking the end of validity while maintaining system time.

---

## Troubleshooting
- **Database connection errors:** Ensure Postgres is running and `DATABASE_URL` is correct.
- **Migrations not applied:** Check Docker Compose logs for the `migrate` service.
- **API returns 500:** Check app logs for details; ensure DB schema matches the app.

---

## More Information
- See the [Bitemporal SDK documentation](../../../sdks/datakit/bitemporality.md) for details on the SDK and how to implement your own repositories.
- See the [Go SDK documentation](../../../sdks/datakit/go.md) for a broader overview. 