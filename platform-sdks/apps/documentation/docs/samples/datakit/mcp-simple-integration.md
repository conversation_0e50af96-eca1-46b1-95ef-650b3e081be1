# MCP Integration Sample

This sample demonstrates MCP (Model Context Protocol) integration with the DataKit CQRS SDK, allowing AI tools to discover and execute your commands and queries.

## Overview

The MCP implementation provides:

- **Automatic Tool Discovery**: Commands and queries automatically become MCP tools
- **JSON-RPC Endpoints**: Compliant MCP protocol implementation
- **Zero Configuration**: MCP endpoints created automatically when you register commands/queries
- **Type Safety**: Full validation and error handling with proper MCP response format

## Sample Location

The complete working sample is available at:
```
apps/go/datakit-samples/mcp/
```

## Key Features Demonstrated

1. **User Management Service**: Complete CRUD operations for users
2. **MCP Tool Integration**: Commands and queries exposed as AI tools
3. **REST + MCP**: Dual transport support
4. **In-Memory Storage**: Simple repository pattern for demo

## Quick Start

### 1. Run the Sample

```bash
cd apps/go/datakit-samples/mcp
go run .
```

The service starts on port 8080 with both REST and MCP endpoints available.

### 2. Test MCP Integration

```bash
# List available MCP tools
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'

# Create a user via MCP
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"create-user","arguments":{"name":"John Doe","email":"<EMAIL>"}}}'
```

### 3. Use with AI Tools

The service exposes these MCP tools:
- `create-user` - Create a new user
- `update-user` - Update user email
- `get-user` - Retrieve user by ID  
- `list-users` - List all users with pagination

## Code Structure

### Commands and Queries

```go
// CreateUserCommand with validation
type CreateUserCommand struct {
    Name  string `json:"name" binding:"required"`
    Email string `json:"email" binding:"required,email"`
}

func (c *CreateUserCommand) Name() string { return "CreateUserCommand" }

// GetUserQuery with validation
type GetUserQuery struct {
    ID string `json:"id" uri:"id" binding:"required"`
}

func (q *GetUserQuery) Name() string { return "GetUserQuery" }
```

### Transport Configuration

```go
// Register command with both REST and MCP transports
engine.RegisterCommands(cqrs.CommandConfig{
    Command: &CreateUserCommand{},
    Handler: userService.createUser,
    Transport: cqrs.Transport{
        REST: &cqrs.REST{Method: "POST", Path: "/api/v1/users"},
        MCP: &cqrs.MCP{
            Description: "Create a new user with name and email",
        },
    },
})
```

### Service Implementation

```go
type UserService struct {
    mu    sync.RWMutex
    users map[string]*User
    idSeq int
}

func (s *UserService) createUser(ctx context.Context, cmd *CreateUserCommand) error {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    id := fmt.Sprintf("%d", s.idSeq)
    s.idSeq++
    
    s.users[id] = &User{
        ID:     id,
        Name:   cmd.Name,
        Email:  cmd.Email,
        Status: "active",
    }
    
    return nil
}
```

## Tool Naming

Commands and queries are automatically converted to kebab-case tool names:
- `CreateUserCommand` → `create-user`
- `GetUserQuery` → `get-user`
- `ListUsersQuery` → `list-users`
- `UpdateUserCommand` → `update-user`

## MCP Response Format

### Command Responses

Commands return standardized success responses:

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "Operation completed successfully"
      }
    ]
  }
}
```

### Query Responses

Queries return formatted JSON data:

```json
{
  "jsonrpc": "2.0", 
  "id": 2,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\n  \"users\": [...],\n  \"total\": 3,\n  \"limit\": 10,\n  \"offset\": 0\n}"
      }
    ]
  }
}
```

## Available Endpoints

### MCP Endpoints
- `GET http://localhost:8080/mcp` - MCP server discovery
- `POST http://localhost:8080/mcp` - JSON-RPC endpoint for tool execution

### REST Endpoints  
- `POST /api/v1/users` - Create user
- `GET /api/v1/users` - List users
- `GET /api/v1/users/:id` - Get user by ID
- `PUT /api/v1/users/:id` - Update user

### Health Check
- `GET /health` - Service health status

## Testing the Sample

### 1. Using MCP Inspector (Recommended)

The easiest way to test your MCP implementation is using the official [MCP Inspector](https://github.com/modelcontextprotocol/inspector) tool:

#### Quick Start with MCP Inspector

1. **Start your service**:
```bash
cd apps/go/datakit-samples/mcp
go run .
```

2. **Launch MCP Inspector**:
```bash
npx @modelcontextprotocol/inspector
```

3. **Connect to your service**:
   - Open the provided URL in your browser (includes auth token)
   - Set **Transport Type** to `Streamable HTTP`
   - Set **URL** to `http://localhost:8080/mcp`
   - Click **Reconnect**

4. **Test your tools**:
   - Navigate to the **Tools** tab
   - Click **List Tools** to see all available commands/queries
   - Select any tool (e.g., `create-user`) and test with parameters:
   ```json
   {
     "name": "Test User",
     "email": "<EMAIL>"
   }
   ```

#### CLI Testing with MCP Inspector

For automated testing or CI/CD integration:

```bash
# List available tools
npx @modelcontextprotocol/inspector --cli http://localhost:8080/mcp --method tools/list

# Test create user
npx @modelcontextprotocol/inspector --cli http://localhost:8080/mcp --method tools/call --tool-name create-user --tool-arg name="CLI User" --tool-arg email="<EMAIL>"

# Test get user
npx @modelcontextprotocol/inspector --cli http://localhost:8080/mcp --method tools/call --tool-name get-user --tool-arg id="1"

# Test list users
npx @modelcontextprotocol/inspector --cli http://localhost:8080/mcp --method tools/call --tool-name list-users --tool-arg limit="10" --tool-arg offset="0"
```

### 2. Manual curl Testing

For low-level testing, you can use curl directly:

```bash
# Initialize MCP connection
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2025-06-18"}}'

# Create users
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"create-user","arguments":{"name":"Alice","email":"<EMAIL>"}}}'

curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"create-user","arguments":{"name":"Bob","email":"<EMAIL>"}}}'

# List users
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":4,"method":"tools/call","params":{"name":"list-users","arguments":{"limit":10,"offset":0}}}'
```

### 2. AI Tool Integration with Cursor

#### Configure MCP in Cursor

1. **Start the service**: `go run .`

2. **Add MCP configuration to Cursor**:
   - Open Cursor IDE
   - Go to Settings > MCP Tools
   - Add a new MCP server configuration:

```json
{
  "cqrs-user-service": {
    "type": "http",
    "url": "http://localhost:8080/mcp",
    "name": "CQRS User Service", 
    "description": "User management with CQRS pattern - supports user queries and updates"
  }
}
```

3. **Alternative: Manual MCP configuration file**:
   - Create or edit `~/.cursor/mcp.json`
   - Add the configuration above to the file

4. **Restart Cursor** to load the new MCP configuration

5. **Use the tools** in Cursor:
   - The MCP tools should appear in the MCP Tools panel
   - Available tools: `create-user`, `update-user`, `get-user`, `list-users`
   - Use natural language to interact with the tools
   - Example: "Create a user named John <NAME_EMAIL>"

#### Troubleshooting Cursor Connection

If you have issues connecting Cursor to your MCP service:

1. **Verify service is running**: Test with MCP Inspector first
2. **Check MCP configuration**: Ensure the JSON configuration is correct
3. **Restart Cursor**: After adding MCP configuration
4. **Check logs**: Look for connection errors in Cursor's output

### 3. Validation with MCP Inspector

MCP Inspector provides excellent validation capabilities:

- ✅ **Protocol Compliance**: Verifies your service implements MCP correctly
- ✅ **Tool Discovery**: Confirms all commands/queries are discoverable
- ✅ **Schema Validation**: Tests input/output schemas work properly
- ✅ **Error Handling**: Validates proper error responses
- ✅ **Interactive Testing**: Visual UI for exploring your MCP tools
- ✅ **Automated Testing**: CLI mode for CI/CD integration

## Key Features

- **Simple Configuration**: Easy transport setup with minimal configuration
- **Automatic Tool Registration**: Commands and queries become MCP tools automatically
- **MCP-Compliant Responses**: Proper content array format for AI tool compatibility
- **Type-Safe Validation**: Full Go type safety with validation tags

## Best Practices Demonstrated

1. **Clear Descriptions**: Provide meaningful descriptions for all MCP tools
2. **Consistent Naming**: Follow consistent patterns for commands and queries
3. **Error Handling**: Proper error responses in MCP format
4. **Type Safety**: Leverage Go's type system with validation tags
5. **Custom Schemas**: Use custom schemas when needed for better AI tool integration

## Next Steps

1. **Extend the Sample**: Add more complex business logic
2. **Persistent Storage**: Replace in-memory storage with database
3. **Authentication**: Add authentication and authorization
4. **Multiple Services**: Create multiple CQRS services with MCP integration
5. **Custom Schemas**: Experiment with custom input/output schemas

## Related Documentation

- [MCP Integration Guide](../../sdks/datakit/mcp-integration.md) - Complete MCP documentation
- [CQRS SDK](../../sdks/datakit/cqrs.md) - Main CQRS documentation

The MCP integration provides a simple and powerful way to expose your CQRS applications to AI tools, with minimal configuration and maximum type safety. 