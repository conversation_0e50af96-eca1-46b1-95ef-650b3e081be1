# CQRS Patients Sample

This sample demonstrates a complete CQRS application using the Datakit SDK with two microservices: Registration Service and Notification Service, connected via Dapr and Kafka.

## Architecture

```plantuml
@startuml

rectangle "Client" as Client

rectangle "Registration Service :8080" as RS
rectangle "Notification Service :8081" as NS

database "Patient Database (In-memory)" as DB1
database "Notification Database (In-memory)" as DB2

queue "Kafka" as Kafka

node "Dapr Runtime" {
    rectangle "Dapr Sidecar :3501" as Dapr1
    rectangle "Dapr Sidecar :3500" as Dapr2
}

Client --> RS
RS --> Kafka
Kafka --> NS
RS --> DB1
NS --> DB2

Dapr1 -[hidden]-> Dapr2

Dapr1 --> Kafka
Dapr2 --> Kafka

RS --> Dapr1
NS --> Dapr2

@enduml
```

## Services

### Registration Service (Port 8080)
Handles patient registration and management.

**Commands:**
- `CreatePatientCommand` - Register a new patient
- `UpdatePatientCommand` - Update patient information
- `DeactivatePatientCommand` - Deactivate a patient

**Queries:**
- `GetPatientByIdQuery` - Retrieve patient by ID
- `SearchPatientsQuery` - Search patients

**Events Published:**
- `PatientRegisteredEvent` - When a patient is registered
- `PatientUpdatedEvent` - When patient info is updated

### Notification Service (Port 8081)
Handles notifications triggered by patient events.

**Commands:**
- `CreateNotificationCommand` - Create a notification
- `MarkNotificationReadCommand` - Mark notification as read

**Queries:**
- `GetNotificationsByPatientIdQuery` - Get notifications for a patient
- `GetAllNotificationsQuery` - Get all notifications

**Events Subscribed:**
- `PatientRegisteredEvent` - Creates welcome notification
- `PatientUpdatedEvent` - Creates update notification

## Quick Start

### Prerequisites
- Go 1.21+
- Docker & Docker Compose
- Dapr CLI

### Using Nx Commands

1. **Start the complete sample:**
```bash
nx start cqrs-patients
```

2. **Test the flow:**
```bash
# Create a patient
nx test:create-patient cqrs-patients

# Check notifications
nx test:list-notifications cqrs-patients

# Run complete flow test
nx test:flow cqrs-patients
```

3. **Check status:**
```bash
nx status cqrs-patients
```

4. **Stop services:**
```bash
nx stop cqrs-patients
```


## API Endpoints

### Registration Service (http://localhost:8080)

#### Create Patient
```bash
POST /api/v1/patients
Content-Type: application/json

{
  "patientName": "John Doe",
  "email": "<EMAIL>",
  "phone": "************",
  "address": "123 Main St"
}
```

#### Update Patient
```bash
PUT /api/v1/patients
Content-Type: application/json

{
  "id": "123",
  "patientName": "John Doe Updated",
  "email": "<EMAIL>",
  "phone": "************",
  "address": "456 Updated St"
}
```

#### Get Patient
```bash
GET /api/v1/patients/{id}
```

### Notification Service (http://localhost:8081)

#### Get All Notifications
```bash
GET /api/v1/notifications
```

#### Get Notifications by Patient
```bash
GET /api/v1/notifications/patient/{patientId}
```

## Event Flow

1. **Patient Registration:**
   - Client sends POST to Registration Service
   - Registration Service creates patient
   - `PatientRegisteredEvent` published to Kafka
   - Notification Service receives event
   - Welcome notification created

2. **Patient Update:**
   - Client sends PUT to Registration Service
   - Registration Service updates patient
   - `PatientUpdatedEvent` published to Kafka
   - Notification Service receives event
   - Update notification created

## Configuration

### Dapr Configuration
The sample uses Dapr for:
- **PubSub**: Kafka message broker
- **State Store**: Redis for state management
- **Service Discovery**: Automatic service discovery

### Docker Services
- **Kafka**: Message broker (port 9092)
- **Zookeeper**: Kafka coordination (port 2181)
- **Redis**: State store (port 6379)

## Development

### Running the sample
```bash
nx start cqrs-patients
```

### Running Tests
```bash
nx test:create-patient cqrs-patients
nx test:list-notifications cqrs-patients
nx test:flow cqrs-patients
```

### Debugging

1. **Check service health:**
```bash
curl http://localhost:8080/health
curl http://localhost:8081/health
```

2. **View Dapr logs:**
```bash
dapr logs --app-id registration-service
dapr logs --app-id notification-service
```

3. **Check Kafka topics:**
```bash
nx status cqrs-patients
```

## Troubleshooting

### Common Issues

1. **Port conflicts:**
   - Run `nx cleanup cqrs-patients ` to kill processes
   - Check port usage with `nx status cqrs-patients`

2. **Kafka not ready:**
   - Wait for Kafka to start completely
   - Check with `docker-compose logs kafka`

3. **Dapr initialization:**
   - Run `dapr init` if not initialized
   - Check Dapr status with `dapr --version`

### Logs and Monitoring

- **Application logs**: Check console output
- **Dapr logs**: `~/.dapr/logs/`
- **Docker logs**: `docker-compose logs`

## Next Steps

- Explore the [Datakit SDK documentation](../../sdks/datakit/overview.md)
- Learn about [CQRS patterns](../../sdks/datakit/cqrs.md)
- Review the source code for implementation details 