---
sidebar_position: 10000
---

# DataKit SDK Releases & Release Notes

This document tracks all official releases and release notes for the DataKit Go SDK. For each release, you will find a summary of changes, improvements, and bug fixes.

---

## Releases

### v0.3.0 (2025-01-03)
Third official release of the [DataKit Go SDK](./datakit.md) with major MCP integration and CQRS improvements.

**[Go SDK](./cqrs.md)**

- **🎉 Native MCP Integration**: Full Model Context Protocol support with automatic JSON-RPC endpoints
- **🚀 Transport Refactoring**: New unified `Transport` configuration supporting REST, gRPC, PubSub, and MCP
- **✨ Enhanced Parameter Binding**: Improved REST parameter binding with better validation
- **🧪 Comprehensive Testing**: Updated test suite with proper MCP response format validation
- **📖 Complete Documentation**: New [MCP Integration Guide](./mcp-integration.md) with examples

**Breaking Changes:**
- Updated `CommandConfig` and `QueryConfig` to use new `Transport` struct instead of `Metadata`
- REST parameter binding now uses `uri` tags instead of `path` tags for URL parameters
- MCP responses now follow official MCP specification format

**Migration Guide:**
```go
// Old format (v0.2.0)
Metadata: cqrs.Metadata{
    REST: &cqrs.REST{Method: "POST", Path: "/users"},
}

// New format (v0.3.0)
Transport: cqrs.Transport{
    REST: &cqrs.REST{Method: "POST", Path: "/users"},
    MCP: &cqrs.MCP{Description: "Create user"},
}
```

---

### v0.2.0 (2024-06-27)
Second official release of the [DataKit Go SDK](./datakit.md).

**[Go SDK](./cqrs.md)**

  - Added full gRPC transport support for commands and queries, alongside REST and PubSub.
  - Improved explicit transport configuration and Dapr-first design.
  - Enhanced documentation and migration notes from v0.

**[TypeScript SDK](./typescript/overview.md)**
  - Initial release with support for REST, tRPC, and Dapr PubSub messaging.

---
### v0.1.0 (2024-06-23)
First official release of the [DataKit Go SDK](./datakit.md).

**[CQRS Module](cqrs.md)**
- Introduced CQRS module with command, query, and event handling.
- Added HTTP REST and PubSub transport support.
- Provided type-safe handler registration and automatic endpoint generation.
- Included comprehensive documentation and real-world usage examples.
- Initial support for event publishing and subscription.

**[Data Bitemporality](bitemporality.md)**
- Fully functional bitemporality module for system and business time tracking, supporting audit, regulatory, and historical data requirements.
---