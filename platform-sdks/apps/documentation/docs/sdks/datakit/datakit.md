---
sidebar_position: 0
---

# Data Kit SDK

The **Data Kit SDK** provides a robust, multi-language foundation for building applications using the CQRS (Command Query Responsibility Segregation) pattern, with integrated messaging (Dapr Pub/Sub), enhanced bitemporality features, and planned MCP (Model Context Protocol) support, automatic data lineage.

---

## SDK Feature Matrix

| Feature / SDK         | [Go](./cqrs.md)   | [TypeScript](./typescript/overview.md) | Java | Python |
|---------------------- |:----:|:----------:|:----:|:------:|
| CQRS (REST)           |  ✅  |    ✅      | Planned | Planned |
| CQRS (Pub/Sub)        |  ✅  |    Planned | Planned | Planned |
| CQRS (gRPC)           |  ✅  |    Planned | Planned | Planned |
| CQRS (tRPC)           |  ❌  |    ✅      | Planned | Planned |
| CQRS (MCP)            |  ✅  |    🚧      | Planned | Planned |
| Dapr Messaging        |  ✅  |    ✅      | Planned | Planned |
| Automatic Data Lineage|  🚧  |    Planned | Planned | Planned |
| Bitemporality         |  ✅  |    Planned | Planned | Planned |

- **✅**: Available now  
- **🚧**: In progress/experimental  
- **❌**: Not planned for this SDK  
- **Planned**: Feature will be available in a future release

Currently, only the Go and Typescript SDKs are available and actively maintained. Support for Java and Python is planned.

---

## Features & Architecture

- **CQRS Pattern:**  
  Clean separation of commands (writes) and queries (reads) with type-safe handlers.
- **Messaging Integration:**  
  Built-in publishers and subscribers for Dapr Pub/Sub, enabling event-driven architectures.
- **MCP (Model Context Protocol):**  
  Full integration allowing AI tools like Cursor to discover and execute commands/queries as tools. Includes automatic JSON-RPC endpoint creation, schema generation, and tool execution.
- **Automatic Data Lineage (Planned):**  
  Will provide end-to-end traceability for REST and messaging operations.
- **Bitemporality:**  
  Support for bitemporal data management with flexible storage strategies.
- **Multi-Language Support:**  
  SDKs are being developed for Go, TypeScript, Java, and Python.

**Modules:**
- **CQRS Module:**  
  Commands, queries, and handlers for business logic with full MCP integration.
- **Messaging Module:**  
  Standardized message abstractions, publishers, and subscribers.
- **MCP Module:**  
  Model Context Protocol support for AI tool integration, automatic endpoint creation, and schema generation.
- **Bitemporality Module:**  
  Bitemporal data management with configurable storage strategies.
- **Data Lineage Module (Planned):**  
  Automatic tracking and audit of all communications.

---