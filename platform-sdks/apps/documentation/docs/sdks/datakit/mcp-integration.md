---
sidebar_position: 3
---

# MCP (Model Context Protocol) Integration

The CQRS SDK provides native MCP (Model Context Protocol) support, allowing AI tools like Cursor to automatically discover and interact with your commands and queries as tools. This integration includes automatic JSON-RPC endpoints and seamless tool execution.

---

## Overview

MCP integration transforms your CQRS applications into AI-accessible services by:

- **Automatic Tool Discovery**: Commands and queries become discoverable MCP tools
- **JSON-RPC Endpoints**: Compliant MCP protocol implementation
- **Type-Safe Execution**: Full validation and error handling
- **Zero Configuration**: Works out-of-the-box with existing CQRS definitions

---

## Quick Start

### 1. Enable MCP in Your Engine

MCP support is automatically enabled when you create a CQRS engine. Simply register your commands and queries with MCP transport configuration:

```go
package main

import (
    "context"
    "github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
)

func main() {
    // Create engine with MCP support
    engine := cqrs.NewEngine("user-service", cqrs.WithREST(8080))
    
    // Register command with MCP transport
    err := engine.RegisterCommands(cqrs.CommandConfig{
        Command: &CreateUserCommand{},
        Handler: userService.CreateUser,
        Transport: cqrs.Transport{
            REST: &cqrs.REST{Method: "POST", Path: "/api/v1/users"},
            MCP: &cqrs.MCP{
                Description: "Create a new user with name and email",
            },
        },
    })
    
    // Register query with MCP transport
    err = engine.RegisterQueries(cqrs.QueryConfig{
        Query:   &GetUserQuery{},
        Handler: userService.GetUser,
        Transport: cqrs.Transport{
            REST: &cqrs.REST{Method: "GET", Path: "/api/v1/users/:id"},
            MCP: &cqrs.MCP{
                Title:       "Get User",
                Description: "Retrieve user information by ID",
            },
        },
    })
    
    // Start the engine - MCP endpoints are automatically available
    ctx := context.Background()
    engine.Start(ctx)
}
```

### 2. Define Commands and Queries

Define your commands and queries with proper validation:

```go
// CreateUserCommand with validation
type CreateUserCommand struct {
    Name  string `json:"name" binding:"required"`
    Email string `json:"email" binding:"required,email"`
}

func (c *CreateUserCommand) Name() string { return "CreateUserCommand" }

// GetUserQuery with validation
type GetUserQuery struct {
    ID string `json:"id" uri:"id" binding:"required"`
}

func (q *GetUserQuery) Name() string { return "GetUserQuery" }

// Response types for query results
type UserResponse struct {
    ID     string `json:"id"`
    Name   string `json:"name"`
    Email  string `json:"email"`
    Status string `json:"status"`
}
```

---

## Automatic MCP Endpoints

When you start your CQRS engine, the following MCP endpoints are automatically available:

### Discovery Endpoint
```
GET http://localhost:8080/mcp
```
Returns MCP server information and capabilities.

### JSON-RPC Endpoint
```
POST http://localhost:8080/mcp
Content-Type: application/json
```

Supports standard MCP methods:
- `initialize` - Initialize MCP connection
- `tools/list` - List available tools
- `tools/call` - Execute a tool

---

## MCP Transport Configuration

The `MCP` transport configuration supports these options:

```go
type MCP struct {
    Title        string                 // Tool title (optional)
    Description  string                 // Tool description (required)
    InputSchema  any                   // Custom input schema (optional)
    OutputSchema any                   // Custom output schema (optional)
    Annotations  map[string]interface{} // Additional metadata (optional)
}
```

### Custom Schemas

You can provide custom input and output schemas:

```go
MCP: &cqrs.MCP{
    Description: "Create a new user with validation",
    InputSchema: map[string]any{
        "type": "object",
        "properties": map[string]any{
            "name": map[string]any{
                "type": "string",
                "description": "User's full name",
            },
            "email": map[string]any{
                "type": "string", 
                "format": "email",
                "description": "User's email address",
            },
        },
        "required": []string{"name", "email"},
        "additionalProperties": false,
    },
    OutputSchema: map[string]any{
        "type": "object",
        "properties": map[string]any{
            "success": map[string]any{"type": "boolean"},
            "message": map[string]any{"type": "string"},
        },
    },
}
```

---

## Tool Execution

### Command Execution

Commands return a standardized success response:

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "Operation completed successfully"
      }
    ]
  }
}
```

### Query Execution

Queries return the actual query result:

```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\n  \"id\": \"user-123\",\n  \"name\": \"John Doe\",\n  \"email\": \"<EMAIL>\",\n  \"status\": \"active\"\n}"
      }
    ]
  }
}
```

---

## Complete Example

Here's a complete working example:

```go
package main

import (
    "context"
    "fmt"
    "log"
    "os"
    "os/signal"
    "sync"
    "syscall"
    
    "github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
)

// User aggregate
type User struct {
    ID     string `json:"id"`
    Name   string `json:"name"`
    Email  string `json:"email"`
    Status string `json:"status"`
}

// Commands
type CreateUserCommand struct {
    Name  string `json:"name" binding:"required"`
    Email string `json:"email" binding:"required,email"`
}

func (c *CreateUserCommand) Name() string { return "CreateUserCommand" }

type UpdateUserCommand struct {
    ID    string `json:"id" uri:"id" binding:"required"`
    Email string `json:"email" binding:"required,email"`
}

func (c *UpdateUserCommand) Name() string { return "UpdateUserCommand" }

// Queries
type GetUserQuery struct {
    ID string `json:"id" uri:"id" binding:"required"`
}

func (q *GetUserQuery) Name() string { return "GetUserQuery" }

type ListUsersQuery struct {
    Limit  int `json:"limit" form:"limit"`
    Offset int `json:"offset" form:"offset"`
}

func (q *ListUsersQuery) Name() string { return "ListUsersQuery" }

// Service implementation
type UserService struct {
    mu    sync.RWMutex
    users map[string]*User
    idSeq int
}

func NewUserService() *UserService {
    return &UserService{
        users: make(map[string]*User),
        idSeq: 1,
    }
}

func (s *UserService) CreateUser(ctx context.Context, cmd *CreateUserCommand) error {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    id := fmt.Sprintf("%d", s.idSeq)
    s.idSeq++
    
    s.users[id] = &User{
        ID:     id,
        Name:   cmd.Name,
        Email:  cmd.Email,
        Status: "active",
    }
    
    return nil
}

func (s *UserService) UpdateUser(ctx context.Context, cmd *UpdateUserCommand) error {
    s.mu.Lock()
    defer s.mu.Unlock()
    
    user, exists := s.users[cmd.ID]
    if !exists {
        return fmt.Errorf("user not found")
    }
    
    user.Email = cmd.Email
    return nil
}

func (s *UserService) GetUser(ctx context.Context, qry *GetUserQuery) (any, error) {
    s.mu.RLock()
    defer s.mu.RUnlock()
    
    user, exists := s.users[qry.ID]
    if !exists {
        return nil, fmt.Errorf("user not found")
    }
    
    return user, nil
}

func (s *UserService) ListUsers(ctx context.Context, qry *ListUsersQuery) (any, error) {
    s.mu.RLock()
    defer s.mu.RUnlock()
    
    // Set defaults
    limit := qry.Limit
    if limit == 0 {
        limit = 100
    }
    
    users := make([]*User, 0, len(s.users))
    for _, user := range s.users {
        users = append(users, user)
    }
    
    // Apply pagination
    start := qry.Offset
    if start >= len(users) {
        users = []*User{}
    } else {
        end := start + limit
        if end > len(users) {
            end = len(users)
        }
        users = users[start:end]
    }
    
    return map[string]any{
        "users":  users,
        "total":  len(s.users),
        "limit":  limit,
        "offset": qry.Offset,
    }, nil
}

func main() {
    userService := NewUserService()
    engine := cqrs.NewEngine("mcp-user-service", cqrs.WithREST(8080))
    
    // Register commands with MCP support
    engine.RegisterCommands(cqrs.CommandConfig{
        Command: &CreateUserCommand{},
        Handler: userService.CreateUser,
        Transport: cqrs.Transport{
            REST: &cqrs.REST{Method: "POST", Path: "/api/v1/users"},
            MCP: &cqrs.MCP{
                Description: "Create a new user with name and email",
            },
        },
    })
    
    engine.RegisterCommands(cqrs.CommandConfig{
        Command: &UpdateUserCommand{},
        Handler: userService.UpdateUser,
        Transport: cqrs.Transport{
            REST: &cqrs.REST{Method: "PUT", Path: "/api/v1/users/:id"},
            MCP: &cqrs.MCP{
                Description: "Change the user's email address",
            },
        },
    })
    
    // Register queries with MCP support
    engine.RegisterQueries(cqrs.QueryConfig{
        Query:   &GetUserQuery{},
        Handler: userService.GetUser,
        Transport: cqrs.Transport{
            REST: &cqrs.REST{Method: "GET", Path: "/api/v1/users/:id"},
            MCP: &cqrs.MCP{
                Description: "Retrieve user information by ID",
            },
        },
    })
    
    engine.RegisterQueries(cqrs.QueryConfig{
        Query:   &ListUsersQuery{},
        Handler: userService.ListUsers,
        Transport: cqrs.Transport{
            REST: &cqrs.REST{Method: "GET", Path: "/api/v1/users"},
            MCP: &cqrs.MCP{
                Title:       "List Users",
                Description: "Retrieve all users with optional pagination",
            },
        },
    })
    
    // Start the engine
    ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
    defer stop()
    
    log.Println("Starting MCP-enabled CQRS service on :8080")
    log.Println("MCP endpoints available at:")
    log.Println("  GET  http://localhost:8080/mcp")
    log.Println("  POST http://localhost:8080/mcp")
    
    if err := engine.Start(ctx); err != nil {
        log.Fatalf("engine failed: %v", err)
    }
}
```

---

## Using with AI Tools

### Cursor IDE

1. **Start your service** with MCP support enabled
2. **Configure Cursor** to connect to your MCP endpoint (if needed)
3. **Use the tools** directly in Cursor:
   - `create-user` - Create new users
   - `update-user` - Update user information
   - `get-user` - Retrieve user details
   - `list-users` - List all users

### Manual Testing

Test the MCP endpoints directly:

```bash
# Initialize MCP connection
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2025-06-18"}}'

# List available tools
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":2,"method":"tools/list","params":{}}'

# Execute a tool
curl -X POST http://localhost:8080/mcp \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"create-user","arguments":{"name":"John Doe","email":"<EMAIL>"}}}'
```

---

## Advanced Features

### Tool Naming

Tool names are automatically generated from command/query names:
- `CreateUserCommand` → `create-user`
- `GetUserByIDQuery` → `get-user-by-id`
- `UpdateUserProfileCommand` → `update-user-profile`

### Error Handling

MCP responses include proper error handling:

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32602,
    "message": "Invalid input: email is required"
  }
}
```

### Schema Validation

All tool inputs are validated against the generated schemas before execution.

---

## Migration from Previous MCP Implementation

If you're migrating from the previous MCP implementation:

1. **Remove manual JSON-RPC handlers** - These are now automatic
2. **Update transport configuration** - Use the new `Transport` struct
3. **Add jsonschema tags** - For better schema generation
4. **Update tool names** - Use the new automatic naming convention

The new implementation is much simpler and requires less boilerplate code while providing better type safety and automatic schema generation.

---

## Best Practices

1. **Use descriptive command/query names** - They become tool names
2. **Add comprehensive jsonschema tags** - For better AI tool integration
3. **Provide clear descriptions** - Help AI tools understand tool purpose
4. **Test with manual curl commands** - Verify MCP integration before AI tool usage
5. **Use consistent naming patterns** - For predictable tool discovery

---

## Troubleshooting

### Common Issues

1. **Tools not discovered**: Check that MCP transport is configured
2. **Schema validation errors**: Verify jsonschema tags and struct fields
3. **Tool execution fails**: Check handler implementation and error handling
4. **Connection refused**: Ensure service is running on correct port

### Debug Mode

Enable debug logging to see MCP request/response details:

```go
// Enable debug logging in your engine configuration
slog.SetDefault(slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
    Level: slog.LevelDebug,
})))
```

---

The new MCP integration provides a seamless bridge between your CQRS applications and AI tools, enabling powerful automation and interaction capabilities with minimal configuration. 