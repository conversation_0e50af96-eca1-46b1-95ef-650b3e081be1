---
sidebar_position: 2
---

# Bitemporality SDK

The `bitemporality` package provides a generic, reusable implementation of bitemporal data management for Go. It allows you to track both system and business (valid) time for your entities, supporting full bitemporal CRUD, versioning, and history.

## Key Features
- Generic bitemporal manager and repository interfaces
- Flexible storage strategies for historical data
- Handles system and valid time tracking for any entity
- Supports Insert, Update, Delete (with custom valid end dates), and historical archiving
- Can be used with any persistence backend (Postgres, GORM, in-memory, etc.)

## Bitemporal Storage Configuration

The SDK provides two strategies for storing historical data, which you can configure when creating a bitemporal `Manager`.

- **`SameTableStrategy` (Default)**: Historical records are stored in the same table as the current, active records. This is the default and is useful for simplifying queries across both current and historical data.
- **`SeparateTableStrategy`**: Historical records are moved to a dedicated historical table (e.g., `patients_historical`). This can help keep your primary table smaller and is useful when you have different performance requirements for historical data.

You can set the strategy like this:

```go
// Use the default strategy (SameTableStrategy)
config := bitemporality.NewConfig()

// Or, explicitly set the strategy
config = bitemporality.NewConfig().WithStorageStrategy(bitemporality.SeparateTableStrategy)

// Initialize the manager with the chosen configuration
manager := bitemporality.NewManager[*YourEntity](repository, config)
```

## Usage

### 1. Implement the `Entity` interface

```go
import "github.com/Matrics-io/platform-sdks/sdks/go/datakit/bitemporality"

type MyEntity struct {
    ID              string
    Version         int64
    ValidStartDate  *time.Time
    ValidEndDate    *time.Time
    SystemStartDate *time.Time
    SystemEndDate   *time.Time
    // ... your fields ...
}

// Implement all Entity methods (GetID, SetID, etc.)
```

### 2. Implement the `Repository` interface
Implement this for your persistence layer (e.g., a generic repository for GORM or pgx).

### 3. Use the `Manager` to manage your entities

```go
repo := NewYourBitemporalRepository[*MyEntity](...)
config := bitemporality.NewConfig() // Use the default storage strategy
mgr := bitemporality.NewManager[*MyEntity](repo, config)
ctx := context.Background()

// Insert
entity := &MyEntity{ID: "1", ...}
inserted, _ := mgr.Insert(ctx, "my_table", entity)

// Update
entity.Name = "Updated"
updated, _ := mgr.Update(ctx, "my_table", entity)

// Delete (immediate)
mgr.Delete(ctx, "my_table", entity.ID)

// Delete (future-dated)
futureDate := time.Now().AddDate(0, 1, 0) // 1 month from now
mgr.Delete(ctx, "my_table", entity.ID, futureDate)
```

### 4. Complete Examples

The monorepo includes two complete, runnable sample applications to demonstrate both storage strategies and how to manage bitemporal and non-bitemporal entities together.

- **GORM Example (`bitemporality_gorm`)**: This sample uses GORM and is configured with the default `SameTableStrategy`. See `apps/go/datakit-samples/bitemporality_gorm`.
- **pgx Example (`bitemporality`)**: This sample uses pgx directly and is configured with the `SeparateTableStrategy`. See `apps/go/datakit-samples/bitemporality`.

## When to Use
- When you need to track both the business validity and system history of your data
- For audit, regulatory, or historical data requirements
- For FHIR, financial, or any domain requiring bitemporal accuracy
- When you need to schedule future changes (e.g., future-dated deletions)

## API Reference
- [Manager API](../../../../../sdks/go/datakit/bitemporality/manager.go)
- [Configuration API](../../../../../sdks/go/datakit/bitemporality/config.go)
- [Entity interface](../../../../../sdks/go/datakit/bitemporality/entity.go)
- [Repository interface](../../../../../sdks/go/datakit/bitemporality/repository.go)

## FAQ

**Q: Can I use this with any database?**
A: Yes! Just implement the `Repository` interface for your backend. The sample apps provide ready-to-use generic repositories for GORM and pgx.

**Q: How do I choose a storage strategy?**
A: Use `SameTableStrategy` (the default) if you need to frequently query across both active and historical data. Use `SeparateTableStrategy` if you want to keep your primary table lean or have different performance needs for historical data.

**Q: Does it support soft deletes and history?**
A: Yes, all updates and deletes are archived with full bitemporal information, regardless of the storage strategy.

**Q: Can I schedule future changes?**
A: Yes, you can use `Delete` with a future `validEndDate` to schedule changes.

**Q: Is there a ready-made Postgres or GORM implementation?**
A: Yes, the sample apps provide ready-to-use, generic repositories for both GORM (`bitemporality_gorm`) and raw SQL with pgx (`bitemporality`). 
