---
title: Middlewares
sidebar_label: Middlewares
sidebar_position: 13
---

# Middlewares

The CQRS SDK supports middleware inspired by tRPC's middleware system. Middleware allows you to add cross-cutting concerns like authentication, logging, and validation to your procedures.

## Basic Middleware

Add middleware to procedures using `.use()`:

```typescript
const loggingMiddleware = async (opts) => {
  const { path, type, input, ctx } = opts;
  console.log(`${type.toUpperCase()}: ${path}`, input);
  
  const result = await opts.next();
  
  console.log(`${type.toUpperCase()}: ${path} completed`);
  return result;
};

const loggedCommand = command.use(loggingMiddleware);

const createUser = loggedCommand({
  input: z.object({ name: z.string() }),
  output: z.object({ id: z.string() }),
  handler: async ({ input, ctx }) => {
    return { id: `user-${input.name}` };
  },
});
```

## Middleware Signature

Middleware functions receive these options:

```typescript
type MiddlewareFunction = (opts: {
  ctx: TContext;           // Current context
  input: any;              // Procedure input
  path: string;            // Procedure name
  type: 'command' | 'query'; // Procedure type
  next: (opts?: { ctx?: TNewContext }) => Promise<any>; // Continue execution
}) => Promise<any>;
```

## Authentication Middleware

Create reusable authentication middleware:

```typescript
const authMiddleware = async (opts) => {
  const { ctx } = opts;

  if (!ctx.userId) {
    throw new Error('UNAUTHORIZED');
  }

  // Simulate user lookup
  const user = await userService.getById(ctx.userId);

  return opts.next({
    ctx: {
      ...ctx,
      user, // Add user to context
    },
  });
};
const protectedCommand = command.use(authMiddleware);

const createUser = protectedCommand({
  input: z.object({ data: z.string() }),
  output: z.object({ success: z.boolean() }),
  handler: async ({ input, ctx }) => {
    console.log(`User ${ctx.user.name} is executing command`);
    return { success: true };
  },
});
```

## Role-Based Authorization

Implement role-based access control:

```typescript
const adminMiddleware = async (opts) => {
  const { ctx } = opts;
  
  if (!ctx.userId) {
    throw new Error('UNAUTHORIZED');
  }
  
  const user = await userService.getById(ctx.userId);
  
  if (user.role !== 'admin') {
    throw new Error('FORBIDDEN');
  }
  
  return opts.next({
    ctx: {
      ...ctx,
      user,
    },
  });
};

const adminCommand = command.use(adminMiddleware)({
  input: z.object({ action: z.string() }),
  output: z.object({ executed: z.boolean() }),
  handler: async ({ input, ctx }) => {
    // Only admins can reach this handler
    return { executed: true };
  },
});
```

## Chaining Middleware

Chain multiple middleware functions:

```typescript
const requestIdMiddleware = async (opts) => {
  return opts.next({
    ctx: {
      ...opts.ctx,
      requestId: generateRequestId(),
    },
  });
};

const timingMiddleware = async (opts) => {
  const start = Date.now();
  const result = await opts.next();
  const duration = Date.now() - start;
  
  console.log(`Execution time: ${duration}ms`);
  return result;
};

const enhancedCommand = command
  .use(requestIdMiddleware)
  .use(authMiddleware)
  .use(timingMiddleware)({
    input: z.object({ data: z.string() }),
    output: z.object({ result: z.string() }),
    handler: async ({ input, ctx }) => {
      // All middleware context is available
      return {
        result: `${input.data} processed by ${ctx.user.name} (${ctx.requestId})`,
      };
    },
  });
```

## Context Extension

Middleware can extend the context type-safely:

```typescript
const enrichmentMiddleware = async (opts) => {
  const timestamp = Date.now();
  const sessionId = generateSessionId();
  
  return opts.next({
    ctx: {
      ...opts.ctx,
      timestamp,
      sessionId,
      isEnriched: true,
    },
  });
};

const enrichedQuery = query.use(enrichmentMiddleware)({
  input: z.string(),
  output: z.object({ data: z.string(), timestamp: z.number() }),
  handler: async ({ input, ctx }) => {
    // TypeScript knows about the enriched context
    return {
      data: input,
      timestamp: ctx.timestamp, // Available and type-safe
    };
  },
});
```

## Error Handling Middleware

Handle and transform errors:

```typescript
const errorHandlingMiddleware = async (opts) => {
  try {
    return await opts.next();
  } catch (error) {
    // Log error
    console.error(`Error in ${opts.path}:`, error);
    
    // Transform error
    if (error instanceof ValidationError) {
      throw new Error('INVALID_INPUT');
    }
    
    // Re-throw other errors
    throw error;
  }
};

const safeCommand = command.use(errorHandlingMiddleware)({
  input: z.object({ data: z.string() }),
  output: z.object({ success: z.boolean() }),
  handler: async ({ input }) => {
    // Errors are automatically handled by middleware
    if (input.data === 'invalid') {
      throw new ValidationError('Invalid data');
    }
    return { success: true };
  },
});
```

## Conditional Middleware

Apply middleware conditionally:

```typescript
const conditionalAuthMiddleware = async (opts) => {
  const { path, ctx } = opts;
  
  // Skip auth for public endpoints
  const publicEndpoints = ['getPublicData', 'healthCheck'];
  if (publicEndpoints.includes(path)) {
    return opts.next();
  }
  
  // Apply auth for protected endpoints
  if (!ctx.userId) {
    throw new Error('UNAUTHORIZED');
  }
  
  return opts.next();
};
```

## Middleware Execution Order

Middleware executes in a nested fashion (like tRPC):

```typescript
const middleware1 = async (opts) => {
  console.log('1: before');
  const result = await opts.next();
  console.log('1: after');
  return result;
};

const middleware2 = async (opts) => {
  console.log('2: before');
  const result = await opts.next();
  console.log('2: after');
  return result;
};

const orderedCommand = command.use(middleware1).use(middleware2)({
  // ... procedure definition
  handler: async () => {
    console.log('handler');
    return { success: true };
  },
});

// Execution order:
// 1: before
// 2: before  
// handler
// 2: after
// 1: after
```

## Type Safety

Middleware maintains full type safety:

```typescript
// TypeScript will infer the enhanced context type
const typedMiddleware = async (opts: {
  ctx: BaseContext;
  input: any;
  path: string;
  type: 'command' | 'query';
  next: (opts?: { ctx?: BaseContext & { enhanced: true } }) => Promise<any>;
}) => {
  return opts.next({
    ctx: {
      ...opts.ctx,
      enhanced: true as const,
    },
  });
};
```

Middleware in the CQRS SDK follows the same patterns as tRPC middleware, making it familiar for teams already using tRPC while providing additional features specific to CQRS patterns. 