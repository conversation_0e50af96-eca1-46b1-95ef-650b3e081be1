---
title: Fastify Adapter
sidebar_label: Fastify Adapter
sidebar_position: 4
---

# Fastify Adapter

The Fastify adapter enables you to expose your CQRS registry as REST and tRPC APIs, with optional OpenAPI/Swagger documentation, CORS, compression, security headers, and WebSocket support. This adapter is highly configurable and leverages Fastify's ecosystem for production-grade APIs.

## Installation

Depending on the features you enable, you may need to install the following peer dependencies:

```bash
pnpm add fastify @trpc/server zod-to-json-schema
# Optional plugins (install as needed):
pnpm add @fastify/swagger @fastify/swagger-ui @fastify/cors @fastify/compress @fastify/helmet @fastify/websocket
```

- `fastify`: Core server
- `@trpc/server`: For tRPC endpoints
- `zod-to-json-schema`: For OpenAPI schema generation
- `@fastify/swagger` and `@fastify/swagger-ui`: For Swagger docs
- `@fastify/cors`: For CORS support
- `@fastify/compress`: For response compression
- `@fastify/helmet`: For security headers
- `@fastify/websocket`: For tRPC WebSocket support

## Configuration Options

The Fastify adapter is configured via the `http` option when running your CQRS instance:

```typescript
await cqrs.run({
  http: {
    adapter: 'fastify',
    config: {
      port: 3000, // default: 3000
      host: '0.0.0.0', // default: '0.0.0.0'
      cors: true, // or an object for fine-grained CORS config
      middleware: {
        compression: true, // Enable gzip compression
        helmet: true,      // Enable security headers
      },
      fastifyOptions: {
        logger: true,      // Enable Fastify logger
        trustProxy: false, // Trust proxy headers
        bodyLimit: 1048576 // Max body size (bytes)
      },
      protocols: {
        rest: {
          enabled: true,
          prefix: '/', // default: '/'
          swagger: {
            enabled: true,
            prefix: '/docs', // Swagger UI path
            title: 'My API',
            version: '1.0.0',
          },
        },
        trpc: {
          enabled: true,
          prefix: '/trpc', // default: '/trpc'
          useWSS: true,    // Enable WebSocket for tRPC
          keepAlive: {
            enabled: true,
            pingMs: 20000,
            pongWaitMs: 5000,
          },
        },
      },
    },
  },
});
```

### REST API
- Enable with `protocols.rest.enabled: true`.
- Customize endpoint prefix with `protocols.rest.prefix`.
- Add OpenAPI/Swagger docs with `protocols.rest.swagger`.

### tRPC API
- Enable with `protocols.trpc.enabled: true`.
- Customize endpoint prefix with `protocols.trpc.prefix`.
- Enable WebSocket with `protocols.trpc.useWSS: true` (requires `@fastify/websocket`).

### CORS
- Enable with `cors: true` or provide an object for advanced config:
  ```typescript
  cors: {
    origin: '*',
    credentials: true,
    methods: ['GET', 'POST'],
  }
  ```

### Middleware
- `compression`: Enable gzip compression (`@fastify/compress`)
- `helmet`: Enable security headers (`@fastify/helmet`)

### Fastify Options
- Pass any native Fastify options via `fastifyOptions`.

## Example: Full-Featured Server

```typescript
import { CQRS } from '@matricsio/datakit';
import { z } from 'zod';

const { define, registry } = CQRS.create();
const { command, query } = define;

const createUser = command({
  input: z.object({ name: z.string(), email: z.string().email() }),
  output: z.object({ id: z.string(), name: z.string() }),
  handler: async ({ input }) => ({ id: '1', name: input.name }),
});

const getUser = query({
  input: z.string(),
  output: z.object({ id: z.string(), name: z.string(), email: z.string() }),
  handler: async ({ input }) => ({ id: input, name: 'John', email: '<EMAIL>' }),
});

const appRegistry = registry({ createUser, getUser });

const cqrs = new CQRS({
  registry: appRegistry,
  metadata: {
    serviceName: 'demo-api',
    serviceVersion: '1.0.0',
    serviceType: 'api',
  },
  getContext: () => ({ userId: 'demo', requestId: 'req-1' }),
});

await cqrs.run({
  http: {
    adapter: 'fastify',
    config: {
      port: 3000,
      cors: true,
      middleware: { compression: true, helmet: true },
      protocols: {
        rest: {
          enabled: true,
          swagger: { enabled: true, prefix: '/docs', title: 'Demo API', version: '1.0.0' },
        },
        trpc: { enabled: true, useWSS: true },
      },
    },
  },
});
```

## Swagger/OpenAPI
- Enable Swagger UI and OpenAPI JSON with `protocols.rest.swagger.enabled: true`.
- Customize the UI path, title, and version.
- OpenAPI spec is generated from your Zod schemas.
- If schema conversion fails, a fallback schema is used and a warning is logged.

## Health Endpoint
- `/health` always available for health checks.

## Troubleshooting & FAQ
- **Missing plugin error?** Make sure to install the required Fastify plugins for the features you enable.
- **Swagger UI not available?** Check that `swagger.enabled` is true and the plugin is installed.
- **WebSocket not working?** Ensure `@fastify/websocket` is installed and `useWSS` is true.
- **CORS issues?** Use an object for fine-grained CORS control.

---

For more advanced usage, see the [middlewares](./middlewares.md) and [context propagation](./context-propagation.md) docs.
