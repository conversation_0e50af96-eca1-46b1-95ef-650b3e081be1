---
title: tRPC Integration
sidebar_label: tRPC Integration
sidebar_position: 5
---

# tRPC Integration

The CQRS SDK automatically generates a fully-typed tRPC v11 router from your procedures. This allows you to use your CQRS procedures with any tRPC client.

## Getting the tRPC Router

```typescript
import { CQRS } from '@matricsio/datakit';

const { define, registry } = CQRS.create<AppContext>({
  // This will enable all your procedures to be used with tRPC
  defaultMetadata: {
    trpc: {},
  }
});
// ... define your procedures ...

const cqrs = new CQRS({
  registry: appRegistry,
  getContext: () => ({ /* your context */ }),
});

// ... run the CQRS server ...
cqrs.run({
  http: {
    adapter: 'fastify',
    config: {
      port: 4000,
      protocols: {
        trpc: {
          enabled: true,
          prefix: '/trpc',
          // Enables trpc-ui if it's installed (pnpm add trpc-ui)
          ui: { enabled: true },
        }
      }
    }
  }
});

// Get the tRPC router
const trpcRouter = cqrs.getTRPCRouter();
export type AppRouter = typeof trpcRouter;
```

## Client Usage

Use the router with any tRPC client. Here's an example with the vanilla client:

```typescript
import { createTRPCClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from './server';

const client = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: 'http://localhost:3000/trpc',
    }),
  ],
});

// Use your procedures
const user = await client.createUser.mutate({
  name: 'John Doe',
  email: '<EMAIL>',
});

const userData = await client.getUser.query(user.id);
```

## Type Inference

The generated router provides full type safety:

```typescript
// Commands become tRPC mutations
await client.createUser.mutate({ /* typed input */ });

// Queries become tRPC queries  
await client.getUser.query(/* typed input */);
```

## Integration with React Query

When using with `@trpc/client` and React Query:

```typescript
import { createTRPCReact } from '@trpc/react-query';
import type { AppRouter } from './server';

export const trpc = createTRPCReact<AppRouter>();

// In your component
function UserProfile({ userId }: { userId: string }) {
  const { data: user } = trpc.getUser.useQuery(userId);
  const createUserMutation = trpc.createUser.useMutation();
  
  // ... rest of component
}
```

## Procedure Mapping

- **Commands** → tRPC mutations (`client.procedureName.mutate()`)
- **Queries** → tRPC queries (`client.procedureName.query()`)

All input/output validation and metadata are preserved when using the tRPC router.

For detailed tRPC client usage, refer to the [official tRPC documentation](https://trpc.io/docs/client/vanilla).
