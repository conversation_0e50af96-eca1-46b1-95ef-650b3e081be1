---
title: REST Integration & Metadata
sidebar_label: REST Integration
sidebar_position: 7
---

# REST Integration & Metadata

CQRS procedures can be exposed as REST endpoints by adding metadata. The SDK provides flexible configuration for HTTP methods, paths, and OpenAPI documentation.

## Basic REST Metadata

Add REST metadata to your procedures:

```typescript
const createUser = command({
  input: z.object({
    name: z.string(),
    email: z.string().email(),
  }),
  output: z.object({
    id: z.string(),
    name: z.string(),
  }),
  metadata: {
    title: 'Create a new user',
    description: 'Creates a new user account with email validation',
    tags: ['Users'],
    rest: {
      method: 'POST',
      path: '/users',
    },
  },
  handler: async ({ input, ctx }) => {
    return await userService.create(input);
  },
});
```

## REST Configuration

The `rest` metadata supports these options:

```typescript
type RestMetadata = {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS';
  path?: string;
  openapi?: {
    summary?: string;
    description?: string;
    tags?: string[];
    deprecated?: boolean;
    operationId?: string;
    externalDocs?: {
      description?: string;
      url: string;
    };
  };
};
```

## Smart Defaults

The SDK provides intelligent defaults based on procedure type:

```typescript
// Query without metadata becomes GET /{procedureName}
const getUser = query({
  input: z.object({ id: z.string() }),
  output: z.object({ id: z.string(), name: z.string() }),
  handler: async ({ input }) => userService.getById(input.id),
});
// → GET /getUser

// Command without metadata becomes POST /{procedureName}
const deleteUser = command({
  input: z.object({ id: z.string() }),
  output: z.object({ success: z.boolean() }),
  handler: async ({ input }) => userService.delete(input.id),
});
// → POST /deleteUser
```

## Custom Paths and Methods

Override defaults with specific configurations:

```typescript
const getUserById = query({
  input: z.object({ id: z.string() }),
  output: z.object({ id: z.string(), name: z.string() }),
  metadata: {
    rest: {
      method: 'GET',
      path: '/users/getUser', // Path parameters NOT supported
    },
  },
  handler: async ({ input }) => userService.getById(input),
});

const updateUser = command({
  input: z.object({
    id: z.string(),
    name: z.string().optional(),
    email: z.string().email().optional(),
  }),
  output: z.object({ id: z.string(), name: z.string() }),
  metadata: {
    rest: {
      method: 'PATCH',
      path: '/users/updateUser',
    },
  },
  handler: async ({ input }) => userService.update(input),
});
```

:::warning
We do NOT support path parameters in the input schema.
:::

- **Query parameters** for GET requests: `/users/getUser?id=123`
- **Request body** for POST/PUT/PATCH requests: `POST /users/create` with `{ "name": "John" }`

**Example endpoint patterns:**

```typescript
// ✅ Supported patterns
GET  /users/getUser?id=123           // Query params for simple inputs
POST /users/create                   // Body for complex inputs
GET  /users/search?name=john&limit=10 // Multiple query params

// ❌ Not supported
GET  /users/:id                      // Path parameters not supported
POST /users/:id/update               // Path parameters not supported
```

## OpenAPI Documentation

Metadata is automatically converted to OpenAPI specification:

```typescript
const createUser = command({
  input: z.object({
    name: z.string().describe('Full name of the user'),
    email: z.string().email().describe('Valid email address'),
  }),
  output: z.object({
    id: z.string().describe('Unique user identifier'),
    name: z.string(),
  }),
  metadata: {
    title: 'Create User',
    description: 'Creates a new user account with the provided information',
    tags: ['User Management'],
    rest: {
      method: 'POST',
      path: '/api/users',
      openapi: {
        operationId: 'createUser',
        externalDocs: {
          description: 'User creation guide',
          url: 'https://docs.example.com/users/create',
        },
      },
    },
  },
  handler: async ({ input }) => userService.create(input),
});
```

## Disabling REST

To prevent a procedure from being exposed as REST:

```typescript
const internalQuery = query({
  input: z.object({ id: z.string() }),
  output: z.object({ data: z.string() }),
  metadata: {
    rest: null, // Explicitly disable REST exposure
  },
  handler: async ({ input }) => internalService.process(input),
});
```

## Type Safety

All REST metadata is fully typed and validated:

```typescript
// ✅ Valid metadata
metadata: {
  rest: {
    method: 'POST',
    path: '/users',
  },
}

// ❌ TypeScript error - invalid method
metadata: {
  rest: {
    method: 'INVALID', // Type error
    path: '/users',
  },
}
```

The REST endpoints are automatically generated based on your metadata configuration when you run the CQRS server.
