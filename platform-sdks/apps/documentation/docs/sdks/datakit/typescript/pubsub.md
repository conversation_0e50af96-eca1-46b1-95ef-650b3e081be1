---
title: PubSub
sidebar_label: PubSub
sidebar_position: 19
---

# Basic Usage

The PubSub SDK provides a type-safe way to publish and subscribe to messages using Dapr. This guide covers the essential features.

## Setup

```typescript
import { createPublisher, createSubscriber } from '@matricsio/datakit/pubsub';

const publisher = createPublisher({
  pubsubName: 'pubsub',
});

const subscriber = createSubscriber({
  pubsubName: 'pubsub',
});
```

### Environment Variables

The PubSub SDK uses the following environment variables:

- `DAPR_HTTP_ADDRESS`: The address of the Dapr HTTP server (default: `http://0.0.0.0:3500`).
- `DAPR_PUBSUB_NAME`: The name of the PubSub component (default: `pubsub`).
- `DAPR_APP_PORT`: The port of the Dapr app (default: `3000`).

## Publishing Messages

```typescript
const publisher = createPublisher({
  pubsubName: 'pubsub',
  daprPort: 3500,
});

// Type-safe publishing
await publisher.publish('user.created', {
  id: 'user-123',
  name: 'Alice',
  timestamp: Date.now(),
} satisfies UserEventType);
```

## Subscribing to Messages

```typescript
const subscriber = createSubscriber({
  pubsubName: 'pubsub',
  appPort: 3000,
});

// Type-safe subscription
await subscriber.subscribe('user.created', async (message) => {
  console.log(`User ${message.name} created at ${new Date(message.timestamp)}`);
});
```

## Bulk Publishing

```typescript
await publisher.publishBulk('user.created', [
  { id: 'user-123', name: 'Alice', timestamp: Date.now() },
  { id: 'user-456', name: 'Bob', timestamp: Date.now() },
]);
```

## Cleanup

Close publishers and subscribers when shutting down:

```typescript
// Cleanup
process.on('SIGTERM', async () => {
  await Promise.all([
    publisher.close(),
    subscriber.close(),
  ]);
});
```
