---
title: FastMCP Integration
sidebar_label: FastMCP
sidebar_position: 3
---

# MCP Integration

The CQRS SDK uses [FastMCP](https://github.com/punkpeye/fastmcp) as its MCP adapter, providing a robust TypeScript implementation of the Model Context Protocol.

## Configuration

Configure the FastMCP adapter when running your CQRS instance:

```typescript
await cqrs.run({
  mcp: {
    adapter: 'fastmcp',
    config: {
      name: 'my-service',
      version: '1.0.0',
      transportType: 'stdio', // or 'httpStream'
      instructions: 'This service provides user and content management tools.',
      // HTTP Stream specific options
      httpStream: {
        port: 8080,
        endpoint: '/mcp',
      },
    },
  },
});
```

## Transport Types

### stdio (Default)

Use with Claude Desktop and similar MCP clients:

```typescript
config: {
  transportType: 'stdio',
}
```

### httpStream

Use for HTTP-based MCP clients:

```typescript
config: {
  transportType: 'httpStream',
  httpStream: {
    port: 8080,
    endpoint: '/mcp',
  },
}
```

## MCP Annotations

The SDK supports FastMCP's annotation system:

```typescript
metadata: {
  mcp: {
    enabled: true,
    openWorldHint: true,      // Tool interacts with external entities
    streamingHint: true,      // Tool supports streaming output
    readOnlyHint: false,      // Tool modifies environment
    destructiveHint: true,    // Tool may perform destructive updates
    idempotentHint: false,    // Repeated calls have additional effects
    timeoutMs: 30000,         // Tool timeout in milliseconds
  },
}
```

## Resources

Add static resources that AI assistants can reference:

```typescript
// Add a static resource
cqrs.addResource({
  uri: 'schema://user',
  name: 'User Schema',
  description: 'User data structure',
  mimeType: 'application/json',
  load: () => ({
    text: JSON.stringify(UserSchema, null, 2),
    mimeType: 'application/json',
  }),
});
```

## Resource Templates

Create dynamic resources with parameters:

```typescript
// Add a resource template
cqrs.addResourceTemplate({
  uriTemplate: 'gs://${storageBucket}/{storageId}',
  name: 'User Profile',
  description: 'Get detailed user profile information',
  mimeType: 'text/markdown',
  arguments: [
    {
      name: 'userId',
      description: 'The user ID to fetch profile for',
      required: true,
    },
  ],
  load: async (args) => {
    const user = await getUserById(args.userId);
    return {
      text: `# ${user.name}\n\n**Email:** ${user.email}\n**Role:** ${user.role}`,
      mimeType: 'text/markdown',
    };
  },
});
```

## Embedded Resources

Reference resources in tool responses:

```typescript
handler: async ({ input, ctx }) => {
  const user = await createUser(input);

  // Include embedded resource
  const profileResource = await cqrs.embedded('user://profile-template');

  await ctx.mcp?.streamContent([
    { type: 'text', text: `Created user: ${user.name}` },
    profileResource,
  ]);

  return user;
}
```

## Content Types

FastMCP supports multiple content types:

```typescript
// Text content
await ctx.mcp?.streamContent({
  type: 'text',
  text: 'Processing complete!'
});

// Image content (base64 encoded)
await ctx.mcp?.streamContent({
  type: 'image',
  data: 'data:image/png;base64,iVBOR...',
  mimeType: 'image/png',
});

// Resource content
await ctx.mcp?.streamContent({
  type: 'resource',
  resource: {
    uri: 'file://result.json',
    text: JSON.stringify(result),
    mimeType: 'application/json',
  },
});
```

## Claude Desktop Integration

To use with Claude Desktop, add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "my-service": {
      "command": "node",
      "args": ["/path/to/your/server.js"],
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

For more FastMCP features and configuration options, see the [FastMCP documentation](https://github.com/punkpeye/fastmcp).
