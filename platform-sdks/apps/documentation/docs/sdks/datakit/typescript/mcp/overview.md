---
title: MCP Overview
sidebar_label: Overview
sidebar_position: 1
---

# MCP Integration

The CQRS SDK provides seamless integration with the Model Context Protocol (MCP), allowing you to expose your commands and queries as AI-accessible tools with minimal configuration.

## Quick Start

Add MCP metadata to any existing procedure:

```typescript
const createUser = define.command({
  input: z.object({ name: z.string(), email: z.string() }),
  output: z.object({ id: z.string(), name: z.string() }),
  metadata: {
    title: 'Create User Account',
    description: 'Create a new user account',
    mcp: {
      enabled: true,
    },
  },
  handler: async ({ input, ctx }) => {
    // Your existing handler code
    return await userService.create(input);
  },
});
```

## Running MCP Server

Start your CQRS instance with MCP enabled:

```typescript
await cqrs.run({
  mcp: {
    adapter: 'fastmcp',
    config: {
      name: 'my-api',
      version: '1.0.0',
      transportType: 'stdio', // or 'httpStream'
    },
  },
});
```

## Smart Defaults

The SDK automatically applies sensible defaults:

- **Queries**: `readOnlyHint: true`, `idempotentHint: true`
- **Commands**: `readOnlyHint: false`, `idempotentHint: false`

All defaults can be overridden in your `mcp` metadata.

## MCP Context

Access MCP-specific features in your handlers:

```typescript
handler: async ({ input, ctx }) => {
  // Stream content updates
  await ctx.mcp?.streamContent({
    type: 'text',
    text: 'Processing request...'
  });

  // Report progress
  await ctx.mcp?.reportProgress({ progress: 50, total: 100 });

  return result;
}
```

## Custom MCP Result

By default, the SDK will try to return the result of your handler as a string, using JSON.stringify for any objects.

If you want to return a better mcp-suited response you may specify an `_mcp` property in your result.

```typescript
const result = await userService.create(input);
Object.assign(result, {
  _mcp: `✅ Created user: ${result.name} (${result.email})`
});
return result;
```

This `_mcp` property will be stripped out when calling the procedure from a non-MCP context (like REST or tRPC).

:::note

Most of the time you will not need to do this, as the SDK will automatically convert your result to a string if it is an object.

:::

## Next Steps

- [FastMCP Integration](./fastmcp.md) - Detailed FastMCP configuration
- See the complete example in `examples/08-mcp-integration/` 