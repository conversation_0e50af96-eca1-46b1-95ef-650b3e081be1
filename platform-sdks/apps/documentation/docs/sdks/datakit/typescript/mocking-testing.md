---
title: Mocking & Testing
sidebar_label: Mocking & Testing
sidebar_position: 15
---

# Mocking & Testing

The CQRS SDK provides a Jest-inspired mocking system that allows you to mock procedures for testing. The mocking system is type-safe and provides familiar Jest-like APIs.

## Enabling Mocking

```typescript
import { CQRS } from '@matricsio/datakit';

// Set up your CQRS instance
const { define, registry } = CQRS.create<AppContext>();
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: () => ({ userId: 'test-user' }),
});

// Enable mocking
const mockRegistry = cqrs.enableMocking();
```

## Mocking Individual Procedures

Mock specific procedures using `spyOn`:

```typescript
// Mock a command
const createUserMock = mockRegistry.spyOn('createUser');
createUserMock.mockResolvedValue({ id: 'mocked-user-id', name: 'Mocked User' });

// Mock a query
const getUserMock = mockRegistry.spyOn('getUser');
getUserMock.mockResolvedValue({
  id: 'user-123',
  name: 'Test User',
  email: '<EMAIL>'
});

// Execute through CQRS instance - uses mocked implementation
const result = await cqrs.commands.createUser({ name: 'John', email: '<EMAIL>' });
expect(result).toEqual({ id: 'mocked-user-id', name: 'Mocked User' });
```

## Mock Methods

The mocking system provides familiar Jest-like methods:

```typescript
const userMock = mockRegistry.spyOn('getUser');

// Mock return values
userMock.mockResolvedValue({ id: '123', name: 'Test', email: '<EMAIL>' });
userMock.mockRejectedValue(new Error('User not found'));
userMock.mockReturnValue(Promise.resolve({ id: '123', name: 'Test', email: '<EMAIL>' }));

// Mock implementations
userMock.mockImplementation(async (userId) => {
  if (userId === 'invalid') throw new Error('Invalid user');
  return { id: userId, name: 'Dynamic User', email: '<EMAIL>' };
});

// One-time implementations
userMock.mockImplementationOnce(async () => ({ id: '1', name: 'First', email: '<EMAIL>' }));
userMock.mockImplementationOnce(async () => ({ id: '2', name: 'Second', email: '<EMAIL>' }));
```

## Call Tracking and Assertions

Track and assert on procedure calls:

```typescript
const createUserMock = mockRegistry.spyOn('createUser');
createUserMock.mockResolvedValue({ id: 'test-id', name: 'Test User' });

// Make calls
await cqrs.commands.createUser({ name: 'John', email: '<EMAIL>' });
await cqrs.commands.createUser({ name: 'Jane', email: '<EMAIL>' });

// Jest-style assertions
expect(createUserMock).toHaveBeenCalledTimes(2);
expect(createUserMock).toHaveBeenCalledWith({ name: 'John', email: '<EMAIL>' });
expect(createUserMock).toHaveBeenLastCalledWith({ name: 'Jane', email: '<EMAIL>' });

// Access call data directly
expect(createUserMock.mock.calls).toHaveLength(2);
expect(createUserMock.mock.calls[0]).toEqual([{ name: 'John', email: '<EMAIL>' }]);
expect(createUserMock.mock.lastCall).toEqual([{ name: 'Jane', email: '<EMAIL>' }]);
```

## Managing Mock State

Control mock state with these methods:

```typescript
const userMock = mockRegistry.spyOn('getUser');

// Clear call history but keep mock implementations
userMock.mockClear();

// Clear calls and remove mock implementations
userMock.mockReset();

// Restore to original implementation (note: requires registry-level coordination)
userMock.mockRestore();

// Bulk operations
mockRegistry.clearAllMocks();  // Clear all call histories
mockRegistry.resetAllMocks();  // Reset all mocks
mockRegistry.restoreAllMocks(); // Restore all to original implementations
```

## Testing with Custom Context

Provide specific context for mocked procedures:

```typescript
// Mock with custom context
const mockRegistry = cqrs.enableMocking({
  userId: 'test-user',
  requestId: 'test-request',
  isAdmin: true,
});

// Or with context function
const mockRegistry = cqrs.enableMocking(() => ({
  userId: getCurrentTestUserId(),
  requestId: generateTestRequestId(),
}));
```

## Example Test

Here's a complete test example:

```typescript
import { expect, test, beforeEach } from 'vitest';

test('user creation workflow', async () => {
  // Enable mocking
  const mockRegistry = cqrs.enableMocking();

  // Set up mocks
  const createUserMock = mockRegistry.spyOn('createUser');
  const getUserMock = mockRegistry.spyOn('getUser');

  createUserMock.mockResolvedValue({ id: 'user-123', name: 'John Doe' });
  getUserMock.mockResolvedValue({
    id: 'user-123',
    name: 'John Doe',
    email: '<EMAIL>'
  });

  // Test the workflow
  const newUser = await cqrs.commands.createUser({
    name: 'John Doe',
    email: '<EMAIL>'
  });

  const retrievedUser = await cqrs.queries.getUser(newUser.id);

  // Assertions
  expect(createUserMock).toHaveBeenCalledWith({
    name: 'John Doe',
    email: '<EMAIL>'
  });
  expect(getUserMock).toHaveBeenCalledWith('user-123');
  expect(retrievedUser.name).toBe('John Doe');
});
```

The mocking system preserves all type safety while providing a familiar testing experience similar to Jest mocks.
