---
title: gRPC Client
sidebar_label: gRPC Client
sidebar_position: 15
---

# gRPC Client

The gRPC client allows you to call remote CQRS services over HTTP or gRPC using Dapr service invocation with full type safety.

## Setup

```typescript
import { createClient } from '@matricsio/datakit/grpc';
import type generated from './generated/my-service_pb'; // Your protobuf types

type ServiceTypes = typeof generated;

const client = createClient<ServiceTypes>({
  serviceAppId: 'my-remote-service',
});
```

## Using Commands and Queries

The client automatically generates camelCase methods that call PascalCase service endpoints:

```typescript
// Commands (modify state)
const user = await client.commands.createUser({
  name: '<PERSON>',
  email: '<EMAIL>'
});

// Queries (read data)
const userData = await client.queries.getUser({ id: user.id });
```

## HTTP Methods

For HTTP protocol, specify the method:

```typescript
await client.commands.createUser(input, { methodName: 'POST' });
await client.queries.getUser(input, { methodName: 'GET' });
```

## Type Safety

The client provides full TypeScript type safety:

```typescript
// ✅ Correctly typed input/output
const user = await client.commands.createUser({
  name: 'John',
  email: '<EMAIL>'
});

// ❌ TypeScript error - wrong input type
await client.queries.getUser({ invalid: 'field' });
```

Types are automatically inferred from your protobuf-generated TypeScript definitions.

Type safety supports both `ts-protoc-gen` and `ts-proto` generated types.

### Generating types from protobuf files

Use protoc with plugins to generate TypeScript types from protobuf files.

```sh
protoc --plugin=./node_modules/.bin/protoc-gen-ts --ts_out=./generated ./service.proto ./base.proto
```

OR

```sh
protoc --plugin=./node_modules/.bin/protoc-gen-ts --ts_out=./generated ./service.proto ./base.proto
```

The important thing is that the commands and queries have a `Command` or `Query` suffix.
Their results should have a `Result` suffix.
E.g. `CreateUserCommand` and `CreateUserCommandResult`.
Or `GetUserQuery` and `GetUserQueryResult`.

These will automatically generate `createUser` and `getUser` methods in the client.
