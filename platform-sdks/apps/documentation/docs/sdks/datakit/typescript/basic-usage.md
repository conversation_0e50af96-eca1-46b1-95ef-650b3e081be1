---
title: Basic Usage
sidebar_label: Basic Usage
sidebar_position: 3
---

# Basic Usage

The CQRS SDK provides a type-safe way to define commands and queries with input/output validation. If you're familiar with tRPC, the API will feel very familiar.

## Installation

Before installing you have to setup your project with the matrics google registry.

```bash
npm config set @matricsio:registry https://us-east1-npm.pkg.dev/ninetyone-devops/hplus-devops-npm/
```

Then add this preinstall script to your `package.json` file:

```json
"scripts": {
    "preinstall": "npx google-artifactregistry-auth",
```

Installing is simple:

```sh
pnpm add @matricsio/datakit
```

## Setup

```typescript
import { z } from 'zod';
import { CQRS } from '@matricsio/datakit';

// Define your context type
type AppContext = {
  userId: string;
  requestId: string;
};

// Create CQRS instance
const { define, registry } = CQRS.create<AppContext>();
const { command, query } = define;
```

## Defining Commands and Queries

**Commands** are for operations that modify state (like mutations in tRPC):

```typescript
const createUser = command({
  input: z.object({
    name: z.string(),
    email: z.string().email(),
  }),
  output: z.object({
    id: z.string(),
    name: z.string(),
  }),
  handler: async ({ input, ctx }) => {
    // Your business logic here
    const user = await userService.create(input);
    return { id: user.id, name: user.name };
  },
});
```

**Queries** are for reading data (like queries in tRPC):

```typescript
const getUser = query({
  input: z.string(), // User ID
  output: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
  }),
  handler: async ({ input, ctx }) => {
    return await userService.getById(input);
  },
});
```

## Creating a Registry

Combine your procedures into a registry:

```typescript
const appRegistry = registry({
  createUser,
  getUser,
  // ... more procedures
});
```

## Setting up CQRS

```typescript
const cqrs = new CQRS({
  registry: appRegistry,
  metadata: {
    serviceName: 'user-service',
    serviceVersion: '1.0.0',
    serviceType: 'api',
  },
  getContext: () => ({
    userId: getCurrentUserId(),
    requestId: generateRequestId(),
  }),
});
```

The optional `metadata` field provides service identification:
- `serviceName`: Name of your service
- `serviceVersion`: Semantic version
- `serviceType`: Predefined types or custom string for service categorization

## Using Commands and Queries

```typescript
// Execute a command
const newUser = await cqrs.commands.createUser({
  name: 'John Doe',
  email: '<EMAIL>',
});

// Execute a query
const user = await cqrs.queries.getUser(newUser.id);
```

## Type Safety

The SDK provides full TypeScript type safety:

```typescript
// ✅ Correct usage
await cqrs.commands.createUser({ name: 'John', email: '<EMAIL>' });

// ❌ TypeScript error - missing required field
await cqrs.commands.createUser({ name: 'John' });

// ❌ TypeScript error - wrong input type
await cqrs.queries.getUser({ id: 'user-123' }); // should be string, not object
```

## Running a Server

Start HTTP server with both REST and tRPC endpoints:

```typescript
await cqrs.run({
  http: {
    adapter: 'fastify',
    config: {
      port: 3000,
      protocols: {
        rest: { enabled: true },
        trpc: { enabled: true },
      },
    },
  },
});
```

This exposes your procedures as both REST endpoints and tRPC procedures, making them accessible from any client.

You can also expose procedures as AI-accessible tools using [MCP Integration](./mcp/overview.md).
