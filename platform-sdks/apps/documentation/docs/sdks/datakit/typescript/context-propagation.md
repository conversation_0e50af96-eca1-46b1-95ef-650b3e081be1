---
title: Context Propagation
sidebar_label: Context Propagation
sidebar_position: 11
---

# Context Propagation

The CQRS SDK provides automatic context propagation through a tracing system. This allows you to access context information from anywhere in your application, including nested procedure calls.

## How Context Works

Context flows through your application automatically:

```typescript
type AppContext = {
  userId: string;
  requestId: string;
  userRole: 'admin' | 'user';
};

const cqrs = new CQRS({
  registry: appRegistry,
  getContext: (params) => {
    return {
      userId: params.rest?.headers?.['user-id'] || 'anonymous',
      requestId: params.traceId,
      userRole: getUserRole(params.rest?.headers?.['authorization']),
    };
  },
});
```

## Accessing Context

Use `cqrs.getContext()` to access the current context from anywhere:

```typescript
// In a service or utility function
function getCurrentUser() {
  const context = cqrs.getContext();
  return context.userId;
}

// In a procedure handler
const createPost = command({
  input: z.object({ title: z.string(), content: z.string() }),
  output: z.object({ id: z.string(), authorId: z.string() }),
  handler: async ({ input, ctx }) => {
    // Context is available in the handler
    const authorId = ctx.userId;
    
    // Also available through getContext()
    const sameAuthorId = cqrs.getContext().userId;
    
    return await postService.create({
      ...input,
      authorId,
    });
  },
});
```

## Context in Nested Calls

Context propagates through nested procedure calls:

```typescript
const createUserWithProfile = command({
  input: z.object({
    name: z.string(),
    email: z.string(),
    profileData: z.object({ bio: z.string() }),
  }),
  output: z.object({ userId: z.string(), profileId: z.string() }),
  handler: async ({ input }) => {
    // Create user first
    const user = await cqrs.commands.createUser({
      name: input.name,
      email: input.email,
    });
    
    // Create profile - context is automatically propagated
    const profile = await cqrs.commands.createProfile({
      userId: user.id,
      bio: input.profileData.bio,
    });
    
    return { userId: user.id, profileId: profile.id };
  },
});

const createProfile = command({
  input: z.object({ userId: z.string(), bio: z.string() }),
  output: z.object({ id: z.string() }),
  handler: async ({ input }) => {
    // Context from parent call is available here
    const currentUserId = cqrs.getContext().userId;
    
    // Verify the user is creating their own profile
    if (input.userId !== currentUserId) {
      throw new Error('Cannot create profile for another user');
    }
    
    return await profileService.create(input);
  },
});
```

## Context with REST Integration

When procedures are called via REST (or tRPC), context receives additional information:

```typescript
const cqrs = new CQRS({
  registry: appRegistry,
  getContext: (params) => {
    const headers = params.rest?.headers || {};
    
    return {
      userId: headers['x-user-id'] || 'anonymous',
      requestId: params.traceId,
      userAgent: headers['user-agent'],
      authorization: headers['authorization'],
      ipAddress: params.rest?.ip,
    };
  },
});
```

You can use these for authorization.

## Trace Information

Now your context includes automatic tracing information:

```typescript
const auditCommand = command({
  input: z.object({ action: z.string(), resource: z.string() }),
  output: z.object({ logged: z.boolean() }),
  handler: async ({ input }) => {
    const context = cqrs.getContext();
    
    await auditService.log({
      action: input.action,
      resource: input.resource,
      userId: context.userId,
      traceId: context.traceId,      // Automatic trace ID
      parentId: context.parentId,    // Parent trace ID (for nested calls)
      timestamp: context.startTime,  // When the trace started
    });
    
    return { logged: true };
  },
});
```

## Context Outside Handlers

Access context from any part of your application:

```typescript
// In a service class
class UserService {
  async getCurrentUser() {
    const context = cqrs.getContext();
    return this.findById(context.userId);
  }
  
  async createPost(data: PostData) {
    const context = cqrs.getContext();
    
    // Automatic audit logging with context
    await this.auditLog('create_post', {
      userId: context.userId,
      traceId: context.traceId,
    });
    
    return this.postRepository.create({
      ...data,
      authorId: context.userId,
    });
  }
}
```

## Custom Context Function

The context function receives rich information for context creation:

```typescript
type CreateContextParams = {
  traceId: string;
  parentId?: string;
  procedureName: string;
  procedureType: 'command' | 'query';
  input: unknown;
  rest?: {
    headers?: Record<string, string>;
    [key: string]: any;
  };
};

const cqrs = new CQRS({
  registry: appRegistry,
  getContext: (params: CreateContextParams) => {
    return {
      userInfo: getUserInfo(params.rest?.headers?.authorization),
      procedureName: params.procedureName,
      isCommand: params.procedureType === 'command',
      // You can access this logger anywhere via `cqrs.getContext().log`
      log: logger.child({
        traceId: params.traceId,
        lineageId: params.lineageId,
        parentId: params.parentId,
        procedureName: params.procedureName,
      }),
    };
  },
});
```

## Type Safety

Context access is fully type-safe:

```typescript
// ✅ TypeScript knows the context shape
const context = cqrs.getContext();
console.log(context.userId);      // string
console.log(context.userRole);    // 'admin' | 'user'

// ❌ TypeScript error - property doesn't exist
console.log(context.nonExistent); // Type error
```

Context propagation works seamlessly across all procedure calls, ensuring consistent access to request-scoped data throughout your application.
