---
title: Overview
sidebar_label: Overview
sidebar_position: 1
---

# Overview

The TypeScript CQRS SDK provides a type-safe way to build APIs using the Command Query Responsibility Segregation (CQRS) pattern. It currently supports tRPC and REST protocols.

## Key Features

- **Type-Safe Commands & Queries** - Define operations input&output with Zod validation
- **REST API Support** - Automatically expose procedures as REST endpoints
- **tRPC API Support** - Generate fully type-safe tRPC router
- **MCP Integration** - Expose procedures as AI-accessible tools with Model Context Protocol
- **Middleware System** - Add authentication, logging, and more
- **Context Propagation** - Access request context anywhere in your app
- **Comprehensive Testing** - Advanced mocking capabilities for easy testing

## Quick Example

```typescript
import { z } from 'zod';
import { CQRS } from '@matricsio/datakit';

// Define your context
type AppContext = { userId: string };

// Create CQRS instance
const { define, registry } = CQRS.create<AppContext>();
const { command, query } = define;

// Define procedures
const createUser = command({
  input: z.object({ name: z.string() }),
  output: z.object({ id: z.string() }),
  handler: async ({ input }) => ({ id: 'user-123' }),
});

const getUser = query({
  input: z.string(),
  output: z.object({ id: z.string(), name: z.string() }),
  handler: async ({ input }) => ({ id: input, name: 'John' }),
});

// Create registry and CQRS instance
const appRegistry = registry({ createUser, getUser });
const cqrs = new CQRS({
  registry: appRegistry,
  metadata: {
    serviceName: 'my-service',
    serviceVersion: '1.0.0',
    serviceType: 'api',
  },
  getContext: () => ({ userId: 'current-user' }),
});

// Use your procedures
const newUser = await cqrs.commands.createUser({ name: 'John' });
const user = await cqrs.queries.getUser(newUser.id);
```

## Philosophy

The CQRS SDK is designed to be:

- **Familiar** - If you know tRPC, you'll mostly feel at home
- **Flexible** - Support multiple protocols (REST, tRPC, future protocols)
- **Type-Safe** - Full TypeScript support with inference
- **Testable** - Comprehensive mocking and testing utilities
- **Scalable** - Built for production applications

Ready to get started? Head to [Basic Usage](./basic-usage.md)!
