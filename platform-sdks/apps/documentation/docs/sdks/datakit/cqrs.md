---
sidebar_position: 1
---

# CQRS SDK

This document provides a comprehensive reference for the Go implementation of the DataKit SDK, focusing on the CQRS (Command Query Responsibility Segregation) pattern, messaging, event-driven architecture, and related features.

---

## Table of Contents

- [Introduction](#introduction)
- [Installation](#installation)
- [Features & Architecture](#features--architecture)
- [Core Interfaces](#core-interfaces)
- [Registration & Configuration](#registration--configuration)
- [Quick Start](#quick-start)
- [Examples & Samples](#examples--samples)
- [Event Publishing](#event-publishing)
- [MCP Integration](#mcp-integration)
- [Conclusion](#conclusion)

---

## Installation

Install the SDK using Go modules:

```bash
go get github.com/Matrics-io/platform-sdks/sdks/go/datakit
```

Import the CQRS package:

```go
import "github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
```

---

## Features & Architecture

- **CQRS Engine:** Complete command and query handling with automatic registration.
- **Multi-Transport Support:** HTTP REST, PubSub (Dapr), gRPC, and MCP (Model Context Protocol).
- **MCP Integration:** Native support for AI tool integration with automatic JSON-RPC endpoints.
- **Auto-Generated Endpoints:** REST API generation based on naming conventions.
- **Zero-Reflection Runtime:** High-performance execution with compile-time reflection setup.
- **Type-Safe Handlers:** Strongly-typed command and query handlers.
- **Automatic Data Lineage (Planned):** End-to-end traceability for REST and messaging operations.
- **Bitemporality (Experimental):** Support for bitemporal data management is in progress.
- **Domain-Driven Design:** Built with DDD principles in mind.
- **Event-Driven:** Native support for event publishing and subscription.
- **Microservices Ready:** Designed for distributed systems.

**Modules:**
- **CQRS Module:** Commands, queries, and handlers for business logic with MCP support.
- **MCP Module:** Model Context Protocol integration for AI tool discovery and execution.
- **Messaging Module:** Standardized message abstractions, publishers, and subscribers.
- **Data Lineage Module (Planned):** Automatic tracking and audit of all communications.

---

## Core Interfaces

The SDK provides clean, type-safe interfaces for implementing CQRS patterns:

```go
// Named provides type identification without reflection
type Named interface {
    Name() string
}

// Command represents application commands
type Command interface {
    Named
}

// Query represents application queries  
type Query interface {
    Named
}

// Event represents domain events
type Event interface {
    Named
}

// CommandHandler handles business logic for commands
type CommandHandler func(ctx context.Context, cmd Command) error

// QueryHandler handles queries
type QueryHandler func(ctx context.Context, qry Query) (interface{}, error)
```

---

## Registration & Configuration

Commands and queries are registered with configuration structs that support multiple transport configurations:

```go
// CommandConfig configures a command
type CommandConfig struct {
    Command   Command
    Handler   interface{}    // CommandHandler or func(ctx, *ConcreteCmd) error
    Transport Transport      // Multi-transport configuration
    Meta      map[string]interface{} // Additional metadata
}

// QueryConfig configures a query
type QueryConfig struct {
    Query     Query
    Handler   interface{}    // QueryHandler or func(ctx, *ConcreteQuery) (result, error)
    Transport Transport      // Multi-transport configuration
    Meta      map[string]interface{} // Additional metadata
}

// Transport configuration for multiple protocols
type Transport struct {
    REST   *REST   // HTTP REST configuration
    GRPC   *GRPC   // gRPC configuration
    PubSub *PubSub // Pub/Sub configuration
    MCP    *MCP    // Model Context Protocol configuration
}

// REST configuration
type REST struct {
    Method string // HTTP method (GET, POST, PUT, DELETE)
    Path   string // URL path with optional parameters (:id)
}

// GRPC configuration
type GRPC struct {
    Method string // gRPC method name
}

// PubSub configuration
type PubSub struct {
    Topic string // Topic name for publishing/subscribing
}

// MCP configuration for AI tool integration
type MCP struct {
    Title        string                 // Tool title (optional)
    Description  string                 // Tool description (required)
    InputSchema  any                   // Custom input schema (optional, auto-generated if not provided)
    OutputSchema any                   // Custom output schema (optional, auto-generated if not provided)
    Annotations  map[string]interface{} // Additional metadata
}
```

---

## Quick Start

Here's a minimal example to get started:

```go
package main

import (
    "context"
    "github.com/Matrics-io/platform-sdks/sdks/go/datakit/cqrs"
)

// Define your command
type CreateUserCommand struct {
    UserName string `json:"userName" binding:"required"`
    Email    string `json:"email" binding:"required"`
}

func (c *CreateUserCommand) Name() string { return "CreateUserCommand" }

// Define your service
type UserService struct{}

func (s *UserService) CreateUserHandler(ctx context.Context, cmd *CreateUserCommand) error {
    // Your business logic here
    return nil
}

func main() {
    service := &UserService{}
    engine := cqrs.NewEngine("my-service", cqrs.WithREST(8080))
    
    // Register command with multiple transports
    engine.RegisterCommands(cqrs.CommandConfig{
        Command: &CreateUserCommand{},
        Handler: service.CreateUserHandler,
        Transport: cqrs.Transport{
            REST: &cqrs.REST{Method: "POST", Path: "/api/v1/users"},
            MCP: &cqrs.MCP{
                Description: "Creates a new user with name and email",
            },
        },
        Meta: map[string]interface{}{
            "version":     "1.0",
            "description": "Creates a new user",
        },
    })
    
    // Start the engine
    engine.Start(context.Background())
}
```

---

## Examples & Samples

For comprehensive examples and practical implementations, see the sample applications:

### Complete Implementation Examples

- **[MCP Integration Sample](../../samples/datakit/mcp-simple-integration.md)** - MCP integration with AI tool support
- **[CQRS Patients Sample](../../samples/datakit/cqrs-patients.md)** - Complete CQRS implementation with event sourcing
- **[Bitemporality Sample](../../samples/datakit/bitemporality.md)** - Bitemporal data management patterns

### Key Examples Available

- **Basic CQRS Setup**: Simple command and query registration
- **Multi-Transport**: HTTP REST + gRPC + PubSub + MCP in a single service
- **MCP Integration**: AI tool discovery and execution with automatic schema generation
- **Advanced Configuration**: Custom paths, authentication, and metadata
- **Event Publishing**: Domain event handling and publishing
- **Type-Safe Handlers**: Concrete handler implementations without type assertions

### Running the Samples

Each sample includes:
- Complete source code with detailed comments
- Docker Compose setup for dependencies
- Step-by-step implementation guide
- Testing instructions and examples

Visit the [samples directory](../../samples/datakit/) for hands-on examples you can run locally.

---

## Event Publishing

To publish events, implement the `Publisher` interface:

```go
// Publisher publishes events to topics
type Publisher interface {
    PublishEvent(ctx context.Context, topic string, event interface{}) error
}
```

The SDK provides built-in publishers for common scenarios:
- **Dapr PubSub**: For microservices communication
- **In-Memory**: For testing and development
- **Custom**: Implement your own publisher for specific needs

---

## Advanced Features

### Auto-Generated REST Endpoints

Commands and queries automatically generate REST endpoints based on naming conventions:

- `CreateUserCommand` → `POST /api/v1/users`
- `GetUserByIDQuery` → `GET /api/v1/users/:id`
- `UpdateUserCommand` → `PUT /api/v1/users/:id`

### Parameter Binding

The SDK supports multi-source parameter binding with improved validation:

```go
type UpdateUserCommand struct {
    ID       string `json:"id" uri:"id" binding:"required"`     // From path parameter
    UserName string `json:"userName"`                           // From JSON body
    Email    string `json:"email" binding:"email"`              // From JSON body with validation
}
```

### Custom Schemas

You can provide custom schemas for MCP tools:

```go
MCP: &cqrs.MCP{
    Description: "Create a new user",
    InputSchema: map[string]any{
        "type": "object",
        "properties": map[string]any{
            "name": map[string]any{"type": "string"},
            "email": map[string]any{"type": "string", "format": "email"},
        },
        "required": []string{"name", "email"},
    },
}
```

### Rich Metadata

Commands and queries support extensive metadata for documentation, security, and operational concerns:

```go
Transport: cqrs.Transport{
    REST: &cqrs.REST{Method: "POST", Path: "/api/v1/users"},
    MCP: &cqrs.MCP{
        Title:       "Create User Account",
        Description: "Creates a new user account with validation",
        Annotations: map[string]interface{}{
            "category":    "user-management",
            "complexity":  "simple",
            "rateLimit":   "100/minute",
        },
    },
},
Meta: map[string]interface{}{
    "version":       "1.0",
    "tags":          []string{"user", "create", "account"},
    "requiredRoles": []string{"admin", "staff"},
    "auditLevel":    "high",
}
```

---

## MCP Integration

The CQRS SDK includes native Model Context Protocol (MCP) support, enabling AI tools to discover and execute your commands and queries automatically.

### Quick MCP Setup

```go
// Register command with MCP support
engine.RegisterCommands(cqrs.CommandConfig{
    Command: &CreateUserCommand{},
    Handler: userService.CreateUser,
    Transport: cqrs.Transport{
        REST: &cqrs.REST{Method: "POST", Path: "/api/v1/users"},
        MCP: &cqrs.MCP{
            Description: "Create a new user with name and email",
            // Schemas auto-generated from struct tags
        },
    },
})
```

### Available MCP Endpoints

When you start your CQRS engine, these endpoints are automatically available:

- **Discovery**: `GET http://localhost:8080/mcp`
- **JSON-RPC**: `POST http://localhost:8080/mcp`

### Key Features

- **Automatic Tool Discovery**: Commands and queries become AI-accessible tools
- **Custom Schemas**: Support for custom input/output schemas
- **Type Safety**: Full validation and error handling
- **Zero Configuration**: Works with existing CQRS definitions

For complete MCP integration details, see the [MCP Integration Guide](./mcp-integration.md).

---

## Conclusion

The DataKit CQRS SDK provides a powerful, type-safe foundation for building CQRS-based applications in Go. With built-in support for multiple transports and automatic endpoint generation, it enables rapid development of scalable microservices.

Key benefits:
- **Zero Boilerplate**: No factory interfaces or type assertions needed
- **Type Safety**: Compile-time safety with runtime performance
- **Multi-Transport**: REST, gRPC, PubSub, and MCP support out of the box
- **AI Integration**: Native MCP support for AI tool discovery and execution
- **Extensible**: Rich metadata and annotation system for custom requirements

For detailed implementation examples and step-by-step guides, explore the [sample applications](../../samples/datakit/).

---

> **Note:** Documentation for the Bitemporality module is available in a separate file: [bitemporality.md](./bitemporality.md)

---
