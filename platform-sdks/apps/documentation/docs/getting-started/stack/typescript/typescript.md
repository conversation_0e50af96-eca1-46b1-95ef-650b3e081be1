---
sidebar_position: 1
---

# TypeScript SDKs and Sample Apps

## Application Framework

- [**Fastify**](https://www.fastify.io/) for high-performance HTTP server and routing.
- [**tRPC**](https://trpc.io/) for end-to-end typesafe APIs.
- [**Dapr**](https://dapr.io/) for distributed application building blocks (PubSub, service invocation, etc).

## Data Validation & Serialization

- [**zod**](https://zod.dev/) for schema validation and type-safe data parsing.
- [**protobufjs**](https://github.com/protobufjs/protobuf.js) for Protocol Buffers serialization.

## Messaging & RPC

- [**Dapr JS SDK**](https://github.com/dapr/js-sdk) for Dapr integration (PubSub, service invocation).
- [**tRPC**](https://trpc.io/) for typesafe RPC.

## Utilities

- [**uuid**](https://github.com/uuidjs/uuid) for UUID generation.
- [**cross-fetch**](https://github.com/lquixada/cross-fetch) for universal fetch API.

## Testing Tools

- [**Jest**](https://jestjs.io/) for unit and integration testing.
- [**Supertest**](https://github.com/ladjs/supertest) for HTTP assertions and integration testing.
- [**expect-type**](https://github.com/mmkal/expect-type) for compile-time type assertions.

## Linting & Formatting

- [**ESLint**](https://eslint.org/) for code linting.
- [**Prettier**](https://prettier.io/) for code formatting.
