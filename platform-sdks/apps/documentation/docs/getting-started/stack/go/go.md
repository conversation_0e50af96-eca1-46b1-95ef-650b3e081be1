---
sidebar_position: 2
---

# Go SDKs and Sample Apps

## Application Framework

- [**Gin**](https://github.com/gin-gonic/gin) for HTTP web framework and routing.
- [**Dapr**](https://dapr.io/) for distributed application building blocks (PubSub, service invocation, etc).

## Logging

- [**zap**](https://github.com/uber-go/zap) for fast, structured logging.

## Database

- [**PostgreSQL**](https://www.postgresql.org/) as the primary relational database.
- [**pgx**](https://github.com/jackc/pgx) for PostgreSQL driver and connection pooling.
- [**GORM**](https://gorm.io/) as an ORM for PostgreSQL (used in some samples).

## RPC & Messaging

- [**Dapr Go SDK**](https://github.com/dapr/go-sdk) for Dapr integration (PubSub, service invocation).

## Utilities

- [**uuid**](https://github.com/google/uuid) for UUID generation.
- [**sonic**](https://github.com/bytedance/sonic) for high-performance JSON serialization.

## Testing Tools

- [**Ginkgo**](https://github.com/onsi/ginkgo) as the BDD testing framework.
- [**Gomega**](https://github.com/onsi/gomega) for expressive and readable assertions.
- [**GoMock**](https://github.com/golang/mock) for generating and using mocks in tests.
- [**Testify**](https://github.com/stretchr/testify) for assertions and mocking (used in SDK).
