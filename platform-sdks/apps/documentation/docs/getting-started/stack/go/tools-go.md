---
sidebar_position: 3
---

# Go Tools

## Build Systems & Monorepo

- [**Nx**](https://nx.dev/) as the monorepo manager and build tool.
- [**Docker**](https://www.docker.com/) for containerizing Go applications and services.
- [**Docker Compose**](https://docs.docker.com/compose/) for orchestrating multi-container Go environments.

## Linting & Formatting

- [**golangci-lint**](https://golangci-lint.run/) for Go code linting and static analysis.
- [**gofmt**](https://pkg.go.dev/cmd/gofmt) for Go code formatting.

## Testing & Coverage

- [**Go test**](https://pkg.go.dev/testing) for Go unit and integration testing.
- [**Ginkgo**](https://github.com/onsi/ginkgo) and [**Gomega**](https://github.com/onsi/gomega) for BDD-style Go testing.
- [**GoMock**](https://github.com/golang/mock) and [**Testify**](https://github.com/stretchr/testify) for Go mocking and assertions.
- [**Coverage tools**](https://pkg.go.dev/cmd/cover) for Go code coverage reporting.

## CI/CD

- [**GitHub Actions**](https://github.com/features/actions) for continuous integration, testing, and release automation.

## Other Tools

- [**Dapr CLI**](https://docs.dapr.io/getting-started/install-dapr-cli/) for running and managing Dapr sidecars and components in Go development. 