---
sidebar_position: 5
---

# TypeScript Tools

## Build Systems & Monorepo

- [**Nx**](https://nx.dev/) as the monorepo manager and build tool.
- [**Docker**](https://www.docker.com/) for containerizing TypeScript/Node.js applications and services.
- [**Docker Compose**](https://docs.docker.com/compose/) for orchestrating multi-container environments.

## Linting & Formatting

- [**ESLint**](https://eslint.org/) for TypeScript/JavaScript linting.
- [**Prettier**](https://prettier.io/) for TypeScript/JavaScript code formatting.

## Testing & Coverage

- [**Jest**](https://jestjs.io/) for TypeScript/JavaScript unit and integration testing.
- [**Supertest**](https://github.com/ladjs/supertest) for HTTP assertions and integration testing.
- [**expect-type**](https://github.com/mmkal/expect-type) for compile-time type assertions.
- [**Jest coverage**](https://jestjs.io/docs/configuration#coveragereporters-arraystring--string) for code coverage reporting.

## CI/CD

- [**GitHub Actions**](https://github.com/features/actions) for continuous integration, testing, and release automation.

## Other Tools

- [**Dapr CLI**](https://docs.dapr.io/getting-started/install-dapr-cli/) for running and managing Dapr sidecars and components in TypeScript development. 