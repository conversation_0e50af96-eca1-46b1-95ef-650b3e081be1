---
sidebar_position: 2
---

# IDE Setup

This section provides information on setting up your IDE for development in the **Healthcare Platform+** monorepo to ensure you have the necessary tools and configurations to work efficiently.

## GoLand (Jetbrains)

If you are using GoLand, you can follow these steps to configure the IDE for a better development experience:

### Nx Console Plugin

1. Install the [Nx Console Plugin](https://plugins.jetbrains.com/plugin/21060-nx-console) to run Nx commands from the IDE.

### ESLint & Prettier

1. Enable the "Run eslint --fix on save" option under `Settings -> Languages & Frameworks -> JavaScript -> Code Quality Tools -> ESLint`.
   This will automatically fix linting errors on save.
   - Optionally, you can configure a shortcut for the "Run eslint --fix" action in the keymap settings.
2. Enable the "Run on save" option under `Settings -> Languages & Frameworks -> JavaScript -> Prettier`. This will
   automatically format code on save.
   - Optionally, you can configure a shortcut for the "Run Prettier" action in the keymap settings.

## Cursor

If you are using **VS Code** or **Cursor**, follow these steps to set up your development environment:

### Recommended Extensions

We recommend installing the following extensions for the best development experience:

#### Language Support & Development Tools

- [Go](https://marketplace.visualstudio.com/items?itemName=golang.Go) - Rich Go language support for VS Code
- [Code Runner](https://marketplace.visualstudio.com/items?itemName=formulahendry.code-runner) - Run and debug Go code directly from VS Code
- [Docker](https://marketplace.visualstudio.com/items?itemName=ms-azuretools.vscode-docker) - Manage Docker applications directly from VS Code
- [Nx Console](https://marketplace.visualstudio.com/items?itemName=nrwl.angular-console) - Manage Nx workspaces and run Nx commands

#### Code Quality & Formatting

- [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) - Linting utility for JavaScript and TypeScript
- [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) - Code formatter for consistent code style
- [Shell-Format](https://marketplace.visualstudio.com/items?itemName=foxundermoon.shell-format) - Shell script formatting and linting
- [markdownlint](https://marketplace.visualstudio.com/items?itemName=DavidAnson.vscode-markdownlint) - Markdown linting and style checking
- [Code Spell Checker](https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker) - Check for spelling mistakes in your code
- [Error Lens](https://marketplace.visualstudio.com/items?itemName=usernamehw.errorlens) - Show errors and warnings inline in your code

#### Project Management & Collaboration

- [Atlassian for Jira](https://marketplace.visualstudio.com/items?itemName=Atlassian.atlascode) - Receive notifications and see your issues directly in VS Code
- [GitHub Pull Requests](https://marketplace.visualstudio.com/items?itemName=GitHub.vscode-pull-request-github) - See pull requests, do code review, and create pull requests
- [Conventional Commits](https://marketplace.visualstudio.com/items?itemName=vivaxy.vscode-conventional-commits) - Enforce conventional commit messages
- [Todo Tree](https://marketplace.visualstudio.com/items?itemName=Gruntfuggly.todo-tree) - Track and manage TODO comments in your code

#### Documentation & Design

- [PlantUML](https://marketplace.visualstudio.com/items?itemName=jebbs.plantuml) - Create and preview PlantUML diagrams
- [Figma for VS Code](https://marketplace.visualstudio.com/items?itemName=figma.figma-vscode) - View and manage Figma designs directly in VS Code
- [Better Comments](https://marketplace.visualstudio.com/items?itemName=aaron-bond.better-comments) - Improve your code commenting with categorized annotations

### Debugging Setup Prerequisites

1. **Install Go Extension**

   ```bash
   # Via Extensions panel (Ctrl+Shift+X)
   # Search for "Go" by golang and install it
   ```

2. **Install Required Debugging Tools**

   ```bash
   # Install Delve debugger (required for Go debugging)
   go install github.com/go-delve/delve/cmd/dlv@latest
   ```

### Linting Setup

1. **Golangci-lint**

   Ensure you have version 2 of `golangci-lint` installed. Make sure to follow the instructions on the `README.md` file in the root of the project.

2. **Go Extension**

   You need to have version `0.47.4` and above installed to run version 2 of `golangci-lint`. If you do not see this version (or a higher one) listed in the extensions of your Cursor IDE, follow the below steps to manually install it:

   - Go to the releases of `vscode-go` in github [using this link](https://github.com/golang/vscode-go/releases).
   - Under "Assets" of the latest release, you will see a file called `vscode-go-<version>.vsix`. Download this file.
   - Go to the Extensions panel (Ctrl+Shift+X)
   - Drag and drop the `vscode-go-<version>.vsix` file into the Extensions panel
   - Restart your extensions.

3. **Lint a package**

   - To lint a package (or file), go to a file of that package and press `Ctrl+Shift+P` to open the command palette.
   - Type `Go: Lint Current Package` and select the option.
   - This will run the linter on the package and show the results in the "Problems" panel.

### MCP Integration Setup

Since for now Cursor does not support injecting environment variables into `mcp.json`, we have written a script that will generate the `mcp.json` file for you based on your env vars and added the file in `.gitignore`. To have the `mcp.json` file generated under `.cursor` so that you can start using MCP tools in your Cursor Chat, please follow the below steps:

1. **Create a `.env` file inside `scripts/cursor/`**

   Add the following environment variables to the file:

   ```env
   # For GitHub MCP
   GITHUB_PERSONAL_ACCESS_TOKEN=your_github_pat

   # For Jira MCP
   JIRA_URL=https://91life.atlassian.net
   JIRA_USERNAME=your_jira_email
   JIRA_API_TOKEN=your_jira_api_token
   ```

2. **How to get the required environment variables:**
   - **GitHub Personal Access Token:**
     1. Go to [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/personal-access-tokens).
     2. Click "Generate new token" (classic or fine-grained).
     3. Select the `91Life` as the "Resource Owner".
     4. Select the required scopes:
      - Read access to code and metadata
      - Read and Write access to pull requests
     5. Copy the token and set it as `GITHUB_PERSONAL_ACCESS_TOKEN` in your `.env` file.
   - **Jira API Token and Username:**
     1. Go to [Atlassian API tokens](https://id.atlassian.com/manage-profile/security/api-tokens).
     2. Click "Create API token" and copy the generated token.
     3. Your Jira username is usually your Atlassian email address.
     4. Set `JIRA_URL`, `JIRA_USERNAME`, and `JIRA_API_TOKEN` in your `.env` file.

3. **Generate the MCP config file**

   Run the following command from the project root:

   ```bash
   pnpm generate:cursor:mcp-json
   ```

   This will create or update `.cursor/mcp.json` using the values from your `.env` file.

4. **Configure MCP Tools in Cursor**

   - Go to the Cursor Settings.
   - Navigate to the "Tools & Integrations" section.
   - Notice the "MCP Tools" section for the list of your configured MCP tools.
   - Under "GitHub", enable only the tools required for your workflow (refer to the screenshots below for the recommended settings).
   - Under "Atlassian", enable only the necessary tools as shown in the screenshots.
   - Make sure to disable any other unnecessary tools to not overwhelm the Cursor agent (usually it prefers < 40 tools).

   _GitHub MCP Tools: Enable only the necessary tools as shown above._
   ![Recommended GitHub MCP Tools Settings](./img/github-mcp-tools.png)

   _Atlassian MCP Tools: Enable only the necessary tools as shown above._
   ![Recommended Atlassian MCP Tools Settings](./img/atlassian-mcp-tools.png)
