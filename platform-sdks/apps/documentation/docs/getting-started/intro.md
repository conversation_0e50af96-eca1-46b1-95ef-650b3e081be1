---
sidebar_position: 0
---

# Introduction

## Prerequisites

This section assumes that you have already completed the steps provided in the `README.md`
file in the [root directory of the project](https://github.com/Matrics-io/platform-sdks).

The `README.md` file contains the necessary steps:

- Run the project on your local machine.
- Start developing on your local machine.

## Getting Started

### Step 1: Initial Architecture

Please make sure to familiarize yourself with the [Initial Architecture](https://docs.google.com/document/d/1w0PGzjempoanNWJlVy-BxMjI05BmrLw27Y8uqsLsyFI/edit?usp=sharing)
documentation of 91Life and Heart+.

### Step 2: Tech Stack

Please make sure to go through the [Tech Stack](./stack/go.md) documentation to understand the technologies used in the project.

In order to have the best onboarding experience, it is recommended to put an emphasis on understanding [Nx](https://nx.dev/getting-started/intro) before
diving on the rest of the resources.

