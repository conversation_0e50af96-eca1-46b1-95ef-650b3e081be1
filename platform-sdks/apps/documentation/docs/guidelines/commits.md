---
sidebar_position: 3
---

# Commit Guidelines

This document outlines the commit message requirements and best practices for the Healthcare Platform project. Following these guidelines ensures a clear, consistent, and traceable commit history.

## Commit Format

- All commit messages must follow the [Conventional Commits](https://www.conventionalcommits.org/) specification.
- The commit message must include the JIRA issue URL on a new line after the short description, and this is mandatory.

**Format:**

```sh
<type>(<optional scope>): <short description>
<JIRA-ISSUE-URL>
```

## Examples

```sh
feat(auth): add OAuth2 login support
https://jira.company.com/browse/HP-3
```

```sh
fix(api): handle null values in response
https://jira.company.com/browse/HP-12
```

```sh
chore(deps): update dependency versions
https://jira.company.com/browse/HP-99
```

## Commit Guidelines

1. **Conventional Commits Required**: Every commit must use a valid Conventional Commit type (e.g., feat, fix, chore, docs, refactor, test, ci, perf, etc.).
2. **JIRA Issue URL Mandatory**: The JIRA issue URL (e.g., `https://jira.company.com/browse/HP-3`) must be included on a new line after the commit message.
3. **Ticket-Based Commits**: Each commit should only contain changes related to the referenced ticket. This ensures that each commit is easy to trace and aligned with specific tasks or issues.
4. **Avoid Unrelated Changes**: Do not bundle unrelated changes in the same commit. If the changes span multiple areas, create separate commits for each.
5. **Descriptive Commit Messages**: Use clear, concise language to describe what the commit does. A good commit message explains the reason behind the change and what it accomplishes.

## Breaking Changes

If your commit introduces a breaking change (i.e., a change that is not backward compatible and may require consumers to update their code), you must indicate this by adding an exclamation mark (`!`) after the type or scope in your commit message, as specified by the [Conventional Commits](https://www.conventionalcommits.org/) standard.

Additionally, you must include a `BREAKING CHANGE:` footer in the commit message body that describes the nature of the breaking change. This helps reviewers and users understand exactly what is breaking and why.

**Format:**

```sh
<type><optional scope>!: <short description>
<JIRA-ISSUE-URL>

BREAKING CHANGE: <detailed explanation of the breaking change>
```

**Example:**

```sh
chore!: drop support for Node 6
https://jira.company.com/browse/HP-200

BREAKING CHANGE: use JavaScript features not available in Node 6.
```

**When to use:**

- When you make changes that break backward compatibility (e.g., removing or renaming public APIs, changing expected input/output, etc.).
- Always document the nature of the breaking change in the PR description as well.
