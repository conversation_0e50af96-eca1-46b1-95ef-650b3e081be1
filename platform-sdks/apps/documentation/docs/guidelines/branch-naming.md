---
sidebar_position: 2
---

# Branch Naming Conventions

This document describes the branch naming conventions used in the Healthcare Platform project. Our conventions are inspired by the [Conventional Commits](https://www.conventionalcommits.org/) specification and help ensure clarity and consistency across the repository.

## Conventional Commit-Inspired Branch Naming

Each branch name starts with a prefix that reflects the type of implementation, such as `feat` for new features, `fix` for bug fixes, and so on. This helps to clearly communicate the purpose of each branch and maintain consistency across the repository.

## Branch Types and Naming Patterns

### Feature Branches

- **Pattern**: `feat/{JIRA-KEY}/{short-description}`
- **Example**: `feat/HP-123/user-authentication`
- **Purpose**: New feature implementation.

### Bugfix Branches

- **Pattern**: `fix/{JIRA-KEY}/{short-description}`
- **Example**: `fix/HP-456/login-issue`
- **Purpose**: Bug fix implementation.

### Hotfix Branches

- **Pattern**: `hotfix/{JIRA-KEY}/{short-description}`
- **Example**: `hotfix/HP-789/production-crash`
- **Purpose**: Urgent production fixes.

### Release Branches

- **Pattern**: `release/{version}`
- **Example**: `release/1.2.0`
- **Purpose**: Preparing for a new production release.

### Chore Branches

- **Pattern**: `chore/{JIRA-KEY}/{short-description}`
- **Example**: `chore/HP-321/update-dependencies`
- **Purpose**: Routine tasks (e.g., updating dependencies, configuration, or build scripts).

### Docs Branches

- **Pattern**: `docs/{JIRA-KEY}/{short-description}`
- **Example**: `docs/HP-654/update-readme`
- **Purpose**: Documentation-only changes.

### Refactor Branches

- **Pattern**: `refactor/{JIRA-KEY}/{short-description}`
- **Example**: `refactor/HP-987/cleanup-auth-module`
- **Purpose**: Code restructuring or optimization.

### Performance Branches

- **Pattern**: `perf/{JIRA-KEY}/{short-description}`
- **Example**: `perf/HP-222/improve-query-speed`
- **Purpose**: Performance improvements.

### Test Branches

- **Pattern**: `test/{JIRA-KEY}/{short-description}`
- **Example**: `test/HP-333/add-integration-tests`
- **Purpose**: Adding or updating tests only.

### CI Branches

- **Pattern**: `ci/{JIRA-KEY}/{short-description}`
- **Example**: `ci/HP-444/update-github-actions`
- **Purpose**: Changes to CI/CD configuration and scripts.

## General Guidelines

- Always include the JIRA issue key in the branch name to link it to the relevant task or feature.
- Use short, descriptive text for the branch purpose.
- Use lowercase and hyphens for readability.
