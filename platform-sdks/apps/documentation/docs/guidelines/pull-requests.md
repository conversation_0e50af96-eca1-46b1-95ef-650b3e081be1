---
sidebar_position: 4
---

# Pull Requests

At the Platform SDKs, submitting a pull request (PR) is an essential part of our collaborative development process. 
It helps ensure code quality, maintainability, and that all features are implemented according to the project’s 
guidelines. This document outlines the guidelines and structure for submitting PRs.

## PR Guidelines

### Keep It Short and Focused
Each PR should address one specific feature, bugfix, or change. This ensures that the scope of each PR is clear and 
manageable for reviewers.

### Avoid Unrelated Changes
Only include changes that are relevant to the ticket or task. Avoid making unrelated improvements or fixes in the same PR.

### Follow Coding Guidelines
PRs that do not align with the project’s style and coding standards will be rejected. Please refer to the 
[coding guidelines](#) for specific standards to follow.

### Provide Detailed Descriptions
Provide enough information to help reviewers and future maintainers understand the changes. A well-described PR 
will make future modifications easier and reduce the time needed for code reviews.

### Only 1 Commit
Every PR should contain only one commit. If your PR contains multiple commits, squash them into one before submitting.
This helps to:
- Keep a clean commit history.
- Make it easier to track changes.
- Simplify cherry-picking in case of a hotfix or release.

In rare cases, where two commits make sense, the PR will be reviewed and may be approved. However, having more than 
two commits usually means the PR is outside of its defined scope and should be reworked.

### Keep the Branch Up to Date
Before being merged, ensure that your branch is up to date with the "into" branch (usually `develop`). If there are 
any changes in the destination branch, rebase your PR branch to keep it in sync. Do **not** introduce new commits to
the PR after rebasing—ensure that the branch is updated correctly and cleanly.

## Commits Structure

### Commit Format
Keep your commits short and descriptive, following this format: `{ticket-number} | {description}`.

**Example:** `HP-3 | Fix sending null values for optional fields`

### Commit Guidelines

1. **Ticket-Based Commits**: Each commit should only contain changes related to the referenced ticket. This ensures 
  that each commit is easy to trace and aligned with specific tasks or issues.
2. **Avoid Unrelated Changes**: Do not bundle unrelated changes in the same commit. If the changes span multiple 
  areas, create separate commits for each.
3. **Descriptive Commit Messages**: Use clear, concise language to describe what the commit does. A good commit 
  message explains the reason behind the change and what it accomplishes.

## PR Structure

### Title
Every PR title must follow this format: `{ticket-code} | {PR title}`. 

Examples: 
- `HP-3 | Fix sending null values for optional fields`,
- `HP-6 | Setup serverless for S3 pass events for local dev`

The title should be short and clear, summarizing the purpose of the PR. Make sure to use the JIRA ticket code in 
the title to link the PR to the corresponding task.

### Description
Every PR description should include the following:

1. **Links to Jira Tickets**: Include relevant JIRA ticket links for easy reference. This will help reviewers 
  understand the context of the change.
2. **What does this PR do?**: Provide a concise summary of the changes being made in the PR. This should be a 
  high-level explanation of what was modified, fixed, or added.
3. **Are unit tests included?**: Mention whether new unit tests are added, or existing tests are updated to cover 
  the new functionality.
4. **Links to Documentation**: List any documentation created or updated for this implementation. This could 
  include API documentation, architecture changes, or feature-specific docs.
5. **Screenshots (if applicable)**: Attach any relevant visuals to support the PR, especially for UI changes. 
  Screenshots can be helpful to visualize the impact of the changes made.

## Example

Below is an example of a well-structured PR:

![Alt text](img/pr-example.png)


## Review and Approval Process

### 1. PR Submitter Responsibilities
The person submitting the PR is responsible for ensuring that all guidelines are followed before the PR is submitted for review. This includes ensuring that:
- The PR follows the project's [guidelines](#pr-guidelines).
- The branch is up to date with the destination branch (usually `develop`).
- The PR description is clear and complete.

### 2. Team Review
Once the PR is submitted, every team member has the opportunity to review it. Team members should check the code for:
- Compliance with the coding guidelines.
- Correct implementation of the feature or fix.
- Proper testing, including unit tests.
- Correctness of the PR description and documentation.

### 3. Approval by Tech Lead
The PR needs the approval of at least **one tech lead** before it can move to the QA stage. This approval ensures that:
- The code aligns with technical standards.
- It’s ready for further testing and QA.

### 4. QA Responsibilities
Once the PR is approved by a tech lead, it is passed to the **QA engineer** for testing. The QA engineer should:
- Ensure that the necessary unit tests are included or updated.
- Add any additional automated tests if needed.
- Update the PR with changes related to testing.
- Merge the PR if all tests pass, and the code is good to go.

### 5. Iteration Process
If QA finds issues or requires additional changes, the process is repeated to accommodate their requests:
- The developer will update the PR based on the feedback provided by the QA engineer.
- The updated PR will be reviewed and approved again before being merged.

By following this structured process, we ensure a smooth collaboration between developers and QA, leading to higher-quality releases and fewer bugs in production.
