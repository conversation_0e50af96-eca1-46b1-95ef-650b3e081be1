---
sidebar_position: 0
---

# Project Standards

Maintaining code quality, consistency, and scalability in the codebase starts with 
enforcing project standards. Following best practices ensures a clean, organized, and maintainable 
codebase.

## Style Guides

- Please take the time to go through [this document in the official Go website](https://go.dev/doc/effective_go)
  about how to write good Go code.
- [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments/f22d526c92037bbc06d20e687b21f71cb386e26a)
  is a good supplement to the above document that shows the most common comments made during reviews of Go code.
- [This](https://github.com/Pungyeon/clean-go-article) is a simpler helpful guideline on how to write Go code.
- Naming: [This](https://github.com/kettanaito/naming-cheatsheet) is another great guide on how to name your variables.

### Directory Names
When choosing directory names, use the below approaches:

- Use a **plural name** when the directory is going to have a **homogeneous** compound of files. These files are considered
  all files of the same type. An example would be calling the directory services when it will only contain .service files.
- Use a **singular name** when the directory is going to have a **heterogeneous** compound of files. These files are considered
  to belong to the same category, but not necessarily strictly of the same type. An example would be calling the
  directory error as it is going to contain files that deal with errors, but it will not only contain different kinds
  of errors.

## Commenting your code

We are big fans of self-documenting code. This means that we try to write code that is self-explanatory and does not
require comments to be understood. However, there are cases where comments are needed. For example:

- when we are using a third party library and we want to explain why we are using it.
- when we are using a hacky solution and we want to explain why we are doing so.
- when we are using a complex algorithm and we want to explain how it works.
- when we are using a complex regular expression and we want to explain how it works.
- when there is a bug in the code and we want to explain why it exists and how it can be fixed.
- when a workaround is used and we want to explain why it is used and how it can be removed.
- when we want to explain why a specific approach was chosen over another one.
- when we want to include links to external resources or documentation.

With that being said, we believe that the repository is the best place to document what our code does and how it
behaves. This means that you should make sure to always write descriptive and concise messages in the code whenever
there is some ambiguity or you feel that others might not understand what/why the code does.

## Gofmt
[Gofmt](https://pkg.go.dev/cmd/gofmt) is a tool that automatically formats Go code. It's great to ensure that all code in the 
repository is formatted in the same way. We recommend running `gofmt` before committing your code.

To run `gofmt` on your code, you can use the following command:

```bash
gofmt -w .
```

## Code coverage
TODO
