---
sidebar_position: 1
---

# Branching Strategy

At the Platform SDKs, we follow the GitFlow branching model to streamline our development process. This strategy helps us manage 
features, bug fixes, releases, and hotfixes while ensuring stability in our production environment.

## Main Branches

### `main` (Production Branch)
This is the stable branch that always reflects the production-ready state of the software. All changes merged into `
main` should be thoroughly tested and reviewed. It represents the code that is deployed to production.

### `develop` (Development Branch)
The `develop` branch is the integration branch for features. It contains the latest development changes that are 
intended to be released. All new features, bug fixes, and improvements are merged into `develop` before being merged 
into `main` for release.

## Branch Types

### Feature Branches

- **Naming Convention**: Feature branches are named following the pattern `feature/{JIRA-KEY}/{short-description}`, 
  where `{JIRA-KEY}` is the unique identifier of the JIRA issue for the feature 
  (e.g., `feature/HP-123/user-authentication`).

- **Purpose**: These branches are used for developing new features. Each feature branch is created from the `develop` 
  branch and is merged back into `develop` once the feature is complete.
- **Workflow**:
    - Create a new branch from `develop` when starting work on a feature.
    - Once the feature is complete and tested, it will be merged back into `develop`.
    - Ensure the branch includes the JIRA issue key in its name to link it to the task or feature being worked on.
- **May branch off from**: `develop`
- **Must merge back into**: `develop`

### Bugfix Branches

- **Naming Convention**: Bugfix branches follow the pattern `bugfix/{JIRA-KEY}/{short-description}` 
  (e.g., `bugfix/HP-456/login-issue`).
- **Purpose**: Bugfix branches are used to address issues found in the `develop` branch. They are typically created 
  when a bug is identified, and a fix is required.
- **Workflow**:
    - Create a bugfix branch from `develop`.
    - After the fix is completed and tested, the bugfix branch is merged back into `develop`.
    - In some cases, bugfixes may be merged directly into a `release` branch rather than `develop`. If that occurs, 
      the `release` branch must be merged back into `develop`.
    - **Exception**: If the bugfix is critical or urgent, it may be necessary to merge the bugfix directly into `develop`
      as well.
- **May branch off from**: `develop` and `release/*`
- **Must merge back into**: `develop` and `release/*`

### Hotfix Branches

- **Naming Convention**: Hotfix branches follow the pattern `hotfix/{JIRA-KEY}/{short-description}` 
  (e.g., `hotfix/HP-789/production-crash`).
- **Purpose**: Hotfix branches are for critical production issues that need immediate attention. These issues are often
  discovered in the `main` branch, and hotfixes are created directly from `main`.
- **Workflow**:
    - Create the hotfix branch from `main`.
    - After the fix is completed and tested, merge the hotfix branch back into the `main` (for immediate production 
      deployment) and `develop` (to ensure the fix is included in future releases). If the deployment needs testing 
      and is not super urgent, merge the hotfix branch into the `release/*` branch 
      instead of `main` to ensure that it goes through standard release process.
- **May branch off from**: `main`
- **Must merge back into**: `main`/`release/*` and `develop`

### Release Branches

- **Naming Convention**: Release branches are named `release/{version}` (e.g., `release/1.2.0`).
- **Purpose**: Release branches are used to prepare for a new production release. This branch contains the final 
  bug fixes, improvements, and versioning necessary for the release.
- **Workflow**:
    - Create a release branch from `develop` when preparing for a new release.
    - Finalize any remaining tasks, such as documentation, versioning, and bug fixes.
    - Once the release is ready, the release branch is merged into both `main` (for production) and 
      `develop` (to keep both branches in sync).
    - Tag the release with the appropriate version number.
- **May branch off from**: `develop`
- **Must merge back into**: `develop` and `main`

## Deployment Process

1. **Development Environment**:
    - Developers work on `feature` or `bugfix` branches and merge them into the `develop` branch when complete.
    - The `develop` branch is deployed to the development environment for testing.

2. **Release Process**:
    - Once the features in `develop` are ready for production, a `release` branch is created.
    - The release branch is deployed to the qa and staging environments for final testing.
    - Once approved, the `release` branch is merged into `main` for production deployment and tagged with the 
      release version.

3. **Hotfixes**:
    - Critical issues found in production are addressed via `hotfix` branches, which are created from `main`.
    - After resolving the issue, the hotfix branch is merged into both `main` (for immediate production deployment)
      and `develop` (to ensure the fix is included in future releases).

By following this strategy, we ensure that new features and bug fixes are tested and integrated effectively while
keeping the production environment stable.

## Learning resources

Use the list below to read some more detailed explanations about the Gitflow branching strategy, its implementation
and its advantages/disadvantages:

1. [AWS Gitflow branching strategy Documentation](https://docs.aws.amazon.com/prescriptive-guidance/latest/choosing-git-branch-approach/gitflow-branching-strategy.html)
2. [Atlassian Gitflow Workflow Blog Post](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow)
3. [A successful Git branching model](https://nvie.com/posts/a-successful-git-branching-model/)
