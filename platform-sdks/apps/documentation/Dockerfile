# Stage 1: Build the Docusaurus site
FROM node:22-alpine AS builder
WORKDIR /app

# Copy dependency definitions and install
COPY package*.json ./
RUN npm install

# Copy the rest of your application code and build the site
COPY . .
RUN npm run build

# Stage 2: Serve the site using a minimal nginx container
FROM nginx:alpine
# Copy the built files from the previous stage to the nginx html folder
COPY --from=builder /app/build /usr/share/nginx/html

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]