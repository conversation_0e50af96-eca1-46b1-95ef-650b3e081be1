// scripts/cursor/generate-mcp-json.js
// <PERSON>ript to generate .cursor/mcp.json from a template, injecting env vars from .env

const fs = require("fs");
const path = require("path");
const dotenv = require("dotenv");

// Load .env file
const envPath = path.resolve(__dirname, ".env");
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.error(".env file not found at", envPath);
  process.exit(1);
}

// Define the template for mcp.json
const mcpTemplate = {
  mcpServers: {
    "nx-mcp": {
      url: process.env.NX_MCP_URL || "http://localhost:9033/sse",
    },
    context7: {
      command: "npx",
      args: ["-y", "@upstash/context7-mcp"],
    },
    github: {
      command: "docker",
      args: [
        "run",
        "-i",
        "--rm",
        "-e",
        "GITHUB_PERSONAL_ACCESS_TOKEN",
        "ghcr.io/github/github-mcp-server",
      ],
      env: {
        GITHUB_PERSONAL_ACCESS_TOKEN:
          process.env.GITHUB_PERSONAL_ACCESS_TOKEN || "",
      },
    },
    "mcp-atlassian": {
      command: "docker",
      args: [
        "run",
        "-i",
        "--rm",
        "-e",
        "JIRA_URL",
        "-e",
        "JIRA_USERNAME",
        "-e",
        "JIRA_API_TOKEN",
        "ghcr.io/sooperset/mcp-atlassian:latest",
      ],
      env: {
        JIRA_URL: process.env.JIRA_URL || "",
        JIRA_USERNAME: process.env.JIRA_USERNAME || "",
        JIRA_API_TOKEN: process.env.JIRA_API_TOKEN || "",
      },
    },
  },
};

// Write the output file
const outputDir = path.resolve(__dirname, "../../.cursor");
const outputPath = path.join(outputDir, "mcp.json");

if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

fs.writeFileSync(outputPath, JSON.stringify(mcpTemplate, null, 2));
console.log("Generated", outputPath);
