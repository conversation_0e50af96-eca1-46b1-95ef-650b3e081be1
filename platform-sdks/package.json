{"name": "@platform-sdks/source", "version": "0.0.0", "license": "MIT", "scripts": {"docs": "nx run documentation:start", "test": "nx run-many --target=test --all", "test:watch": "nx run-many --target=test --all --watch", "test:coverage": "nx run-many --target=test --all --configuration=ci", "build": "nx run-many --target=build --all", "lint": "nx run-many --target=lint --all", "generate:cursor:mcp-json": "node scripts/cursor/generate-mcp-json.js"}, "private": true, "dependencies": {"@akebifiky/remark-simple-plantuml": "^1.0.2", "@docusaurus/core": "3.7.0", "@docusaurus/preset-classic": "3.7.0", "@mdx-js/react": "^3.1.0", "@nx-go/nx-go": "3.3.1", "clsx": "^2.1.1", "next-themes": "^0.4.5", "prism-react-renderer": "^2.4.1", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.7.0", "@docusaurus/tsconfig": "3.7.0", "@docusaurus/types": "3.7.0", "@eslint/eslintrc": "^3.0.0", "@eslint/js": "^9.29.0", "@jest/types": "^29.5.0", "@nx/eslint": "^21.2.1", "@nx/jest": "^21.1.1", "@nx/js": "^21.1.1", "@nx/workspace": "20.6.4", "@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.0.0", "dotenv": "^16.6.1", "eslint": "^8.57.1", "jest": "^29.5.0", "jest-environment-node": "^29.7.0", "nx": "^20.6.4", "ts-jest": "^29.1.0", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "~5.7.3", "typescript-eslint": "^8.35.0"}}