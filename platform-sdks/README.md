<p style="text-align: center">
  <a href="https://www.91.life/" target="blank">
    <img src="https://cdn.prod.website-files.com/6464fc5c49a35f360e272b62/6638ee51fb46dea59b4a71c4_Group%************.svg" width="300" alt="91Life Logo" />
  </a>
</p>

# Platform SDKs

This monorepo contains the SDKs, their sample applications and documentations provided by 91Life's Engineering Platform. It is built using [Nx](https://nx.dev/), a powerful build system that helps manage multiple applications and libraries in a single repository.

## 🚀 Getting Started

# Local Development Setup

Follow these steps to set up the Platform SDKs monorepo for local development. This guide assumes you are
familiar with the command line and have basic knowledge of Go, JavaScript/Typescript and Node.js.

## Prerequisite Installations

### 1. Node Version Manager

[Nvm](https://github.com/nvm-sh/nvm) is a version manager for node.js, designed to be installed
per-user, and invoked per-shell. It allows you to easily switch between node versions and manage
multiple node installations.

To install it, run one of the following commands in your terminal:

```sh
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
```

```sh
wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
```

Optional: Add the following lines to your shell configuration file (e.g., `.bashrc`, `.zshrc`, etc.):

```sh
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion
```

### 2. Node.js

We use [Node.js](https://nodejs.org/en/) version 22 as our JavaScript runtime. To install it,
run the following command:

```sh
nvm install 22
```

It will automatically set the node version to 22 for the current shell session. If you already had one
version of node installed, you can set the default version to 22 by running:

```sh
nvm alias default 22
```

Ensure that you have not overridden the default node version in your shell configuration file
(e.g., `.bashrc`, `.zshrc`, etc.).

### 3. Pnpm

[Pnpm](https://pnpm.io/) is a fast, disk space-efficient package manager. We use it to manage
our packages and dependencies. To install it, run one of following command:

```sh
npm install -g pnpm@latest-10 # Using npm
```

```sh
brew install pnpm # Using brew
```

We currently use version 10 of pnpm.

### 4. Gvm

[Gvm](https://github.com/moovweb/gvm) is a Go version manager. It allows you to easily switch between
Go versions and manage multiple Go installations.

To install it, run the following command in your terminal:

```sh
bash < <(curl -sSL https://raw.githubusercontent.com/moovweb/gvm/master/binscripts/gvm-installer)
```

Optional: Add the following lines to your shell configuration file (e.g., `.bashrc`, `.zshrc`, etc.):

```sh
[[ -s "$HOME/.gvm/scripts/gvm" ]] && source "$HOME/.gvm/scripts/gvm"
```

### 5. Go

Most of this project is written in [Go](https://go.dev/) version 1.24. To install Go, run the following command:

```sh
gvm install go1.24 # Install Go version 1.24
gvm use go1.24 --default # Set the default Go version to 1.24
```

Ensure that you have not overridden the default Go version in your shell configuration file (e.g., `.bashrc`, `.zshrc`, etc.).

### 6. golangci-lint

[golangci-lint](https://golangci-lint.run/) is a Go linters aggregator that runs linters in parallel.

To install golangci-lint v1.64.8, run the following command:

```bash
curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.64.8
```

For MacOS, you can install it using Homebrew:

```sh
brew install golangci-lint
brew upgrade golangci-lint
```

## Running the Project

### 1. Install Dependencies

To install the dependencies for all packages in the monorepo, run the following command in the root
directory of the project:

```sh
pnpm install
```

### Starting the Documentation Website

To start the Docusaurus documentation use the following command:

```sh
nx run documentation:start # Start the Platform SDKs documentation
```

When the documentation app is started, you can access it at [http://localhost:3000](http://localhost:3000).
Use this documentation to onboard with the project and learn more about the Platform SDKs monorepo.
